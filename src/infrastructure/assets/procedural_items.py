"""
Procedural Item and UI Asset Generation

This module contains functions to procedurally generate item sprites and UI elements using Pygame.
"""

import pygame
from typing import Tu<PERSON>, Dict, Optional

# Import player sprite functions from dedicated module
from .procedural_player import get_base_player_sprite, create_layered_player_sprite


def get_item_icon(item_id: str, size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """
    Generate procedural icon for any item.

    Args:
        item_id: The ID of the item to generate an icon for
        size: Size of the icon to generate

    Returns:
        A pygame.Surface containing the item icon

    Raises:
        ValueError: If the item_id is not recognized
    """
    # Icon registry mapping item IDs to their specific generators
    icon_generators = {
        # Weapons
        "rusty_sword": get_rusty_sword_icon,
        "iron_sword": get_iron_sword_icon,
        "wooden_bow": get_wooden_bow_icon,
        "wooden_shield": get_wooden_shield_icon,
        "iron_shield": get_iron_shield_icon,

        # Armor pieces
        "cloth_cap": get_cloth_cap_icon,
        "cloth_shirt": get_cloth_shirt_icon,
        "cloth_pants": get_cloth_pants_icon,
        "simple_shoes": get_simple_shoes_icon,
        "leather_cap": get_leather_cap_icon,
        "leather_armor": get_leather_armor_icon,
        "leather_boots": get_leather_boots_icon,
        "leather_pants": get_leather_pants_icon,
        "iron_helmet": get_iron_helmet_icon,
        "iron_breastplate": get_iron_breastplate_icon,
        "iron_greaves": get_iron_greaves_icon,
        "iron_boots": get_iron_boots_icon,

        # Consumables
        "health_potion": get_health_potion_icon,
        "mana_potion": get_mana_potion_icon,
        "bread": get_bread_icon,
        "ale": get_ale_icon,
        "cheese": get_cheese_icon,
        "hot_meal": get_hot_meal_icon,

        # Weapons
        "steel_dagger": get_steel_dagger_icon,
        "wooden_bow": get_wooden_bow_icon,
        "battle_axe": get_battle_axe_icon,

        # Miscellaneous
        "gold_coin": get_gold_coin_icon,
        "ruby_gem": get_ruby_gem_icon,
        "ancient_key": get_ancient_key_icon,
        "room_key": get_room_key_icon,

        # Loot items
        "meat": get_meat_icon,
        "leather": get_leather_icon,
        "spider_sac": get_spider_sac_icon,
    }

    generator = icon_generators.get(item_id)
    if not generator:
        raise ValueError(f"No icon generator found for item: {item_id}")

    return generator(size)


def get_rusty_sword_sprite(size: Tuple[int, int] = (24, 24)) -> pygame.Surface:
    """Generate a rusty sword sprite procedurally."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size
    
    # Colors
    rust_color = (184, 115, 51)         # Rusty brown
    dark_rust_color = (139, 69, 19)     # Dark rust
    handle_color = (101, 67, 33)        # Brown handle
    
    # Blade
    blade_points = [
        (s_w*0.5, s_h*0.1),             # Tip
        (s_w*0.4, s_h*0.7),             # Left edge
        (s_w*0.6, s_h*0.7),             # Right edge
    ]
    pygame.draw.polygon(sprite_surface, rust_color, blade_points)
    
    # Rust spots
    pygame.draw.circle(sprite_surface, dark_rust_color, 
        (int(s_w*0.45), int(s_h*0.3)), 2)
    pygame.draw.circle(sprite_surface, dark_rust_color, 
        (int(s_w*0.55), int(s_h*0.5)), 1)
    
    # Handle
    handle_rect = pygame.Rect(s_w*0.45, s_h*0.7, s_w*0.1, s_h*0.25)
    pygame.draw.rect(sprite_surface, handle_color, handle_rect)
    
    # Guard
    guard_rect = pygame.Rect(s_w*0.35, s_h*0.68, s_w*0.3, s_h*0.05)
    pygame.draw.rect(sprite_surface, rust_color, guard_rect)
    
    return sprite_surface


def get_iron_sword_sprite(size: Tuple[int, int] = (24, 24)) -> pygame.Surface:
    """Generate an iron sword sprite procedurally."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size
    
    # Colors
    blade_color = (192, 192, 192)       # Silver blade
    shine_color = (255, 255, 255)       # White shine
    handle_color = (101, 67, 33)        # Brown handle
    guard_color = (105, 105, 105)       # Gray guard
    
    # Blade
    blade_points = [
        (s_w*0.5, s_h*0.05),            # Tip
        (s_w*0.4, s_h*0.7),             # Left edge
        (s_w*0.6, s_h*0.7),             # Right edge
    ]
    pygame.draw.polygon(sprite_surface, blade_color, blade_points)
    
    # Blade shine
    pygame.draw.line(sprite_surface, shine_color, 
        (s_w*0.5, s_h*0.1), (s_w*0.5, s_h*0.6), 1)
    
    # Handle
    handle_rect = pygame.Rect(s_w*0.45, s_h*0.7, s_w*0.1, s_h*0.25)
    pygame.draw.rect(sprite_surface, handle_color, handle_rect)
    
    # Guard
    guard_rect = pygame.Rect(s_w*0.3, s_h*0.68, s_w*0.4, s_h*0.06)
    pygame.draw.rect(sprite_surface, guard_color, guard_rect)
    
    return sprite_surface


def get_wooden_bow_sprite(size: Tuple[int, int] = (16, 24)) -> pygame.Surface:
    """Generate a wooden bow sprite procedurally."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size
    
    # Colors
    wood_color = (139, 69, 19)          # Brown wood
    string_color = (245, 245, 220)      # Beige string
    
    # Bow curve (arc)
    center_x = s_w // 2
    pygame.draw.arc(sprite_surface, wood_color, 
        (0, 0, s_w, s_h), 0, 3.14159, 3)
    
    # Bow string
    pygame.draw.line(sprite_surface, string_color, 
        (2, s_h*0.1), (2, s_h*0.9), 1)
    
    return sprite_surface


def get_health_potion_sprite(size: Tuple[int, int] = (16, 16)) -> pygame.Surface:
    """Generate a health potion sprite procedurally."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size
    
    # Colors
    bottle_color = (169, 169, 169)      # Gray bottle
    liquid_color = (220, 20, 60)        # Red liquid
    cork_color = (101, 67, 33)          # Brown cork
    
    # Bottle shape
    bottle_rect = pygame.Rect(s_w*0.3, s_h*0.2, s_w*0.4, s_h*0.7)
    pygame.draw.rect(sprite_surface, bottle_color, bottle_rect)
    
    # Liquid
    liquid_rect = pygame.Rect(s_w*0.32, s_h*0.3, s_w*0.36, s_h*0.5)
    pygame.draw.rect(sprite_surface, liquid_color, liquid_rect)
    
    # Cork
    cork_rect = pygame.Rect(s_w*0.35, s_h*0.1, s_w*0.3, s_h*0.15)
    pygame.draw.rect(sprite_surface, cork_color, cork_rect)
    
    return sprite_surface


def get_mana_potion_sprite(size: Tuple[int, int] = (16, 16)) -> pygame.Surface:
    """Generate a mana potion sprite procedurally."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size
    
    # Colors
    bottle_color = (169, 169, 169)      # Gray bottle
    liquid_color = (0, 100, 255)        # Blue liquid
    cork_color = (101, 67, 33)          # Brown cork
    
    # Bottle shape
    bottle_rect = pygame.Rect(s_w*0.3, s_h*0.2, s_w*0.4, s_h*0.7)
    pygame.draw.rect(sprite_surface, bottle_color, bottle_rect)
    
    # Liquid
    liquid_rect = pygame.Rect(s_w*0.32, s_h*0.3, s_w*0.36, s_h*0.5)
    pygame.draw.rect(sprite_surface, liquid_color, liquid_rect)
    
    # Cork
    cork_rect = pygame.Rect(s_w*0.35, s_h*0.1, s_w*0.3, s_h*0.15)
    pygame.draw.rect(sprite_surface, cork_color, cork_rect)
    
    return sprite_surface


def get_bread_sprite(size: Tuple[int, int] = (16, 12)) -> pygame.Surface:
    """Generate a bread sprite procedurally."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size
    
    # Colors
    bread_color = (210, 180, 140)       # Tan bread
    crust_color = (139, 69, 19)         # Brown crust
    
    # Bread loaf
    bread_rect = pygame.Rect(s_w*0.1, s_h*0.2, s_w*0.8, s_h*0.6)
    pygame.draw.ellipse(sprite_surface, bread_color, bread_rect)
    
    # Crust outline
    pygame.draw.ellipse(sprite_surface, crust_color, bread_rect, 2)
    
    # Scoring lines
    for i in range(3):
        x_pos = s_w*0.25 + i * s_w*0.2
        pygame.draw.line(sprite_surface, crust_color, 
            (x_pos, s_h*0.3), (x_pos, s_h*0.7), 1)
    
    return sprite_surface


def get_gold_coin_sprite(size: Tuple[int, int] = (12, 12)) -> pygame.Surface:
    """Generate a gold coin sprite procedurally."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size
    
    # Colors
    gold_color = (255, 215, 0)          # Gold
    shine_color = (255, 255, 224)       # Light gold shine
    edge_color = (218, 165, 32)         # Dark gold edge
    
    # Coin circle
    center = (s_w // 2, s_h // 2)
    radius = min(s_w, s_h) // 2 - 1
    pygame.draw.circle(sprite_surface, gold_color, center, radius)
    
    # Edge
    pygame.draw.circle(sprite_surface, edge_color, center, radius, 1)
    
    # Shine spot
    pygame.draw.circle(sprite_surface, shine_color, 
        (center[0] - 2, center[1] - 2), radius // 3)
    
    return sprite_surface


def get_ruby_gem_sprite(size: Tuple[int, int] = (12, 12)) -> pygame.Surface:
    """Generate a ruby gem sprite procedurally."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size
    
    # Colors
    ruby_color = (220, 20, 60)          # Crimson
    shine_color = (255, 192, 203)       # Pink shine
    dark_ruby_color = (139, 0, 0)       # Dark red
    
    # Gem shape (diamond)
    gem_points = [
        (s_w*0.5, s_h*0.1),             # Top
        (s_w*0.2, s_h*0.4),             # Left
        (s_w*0.5, s_h*0.9),             # Bottom
        (s_w*0.8, s_h*0.4),             # Right
    ]
    pygame.draw.polygon(sprite_surface, ruby_color, gem_points)
    
    # Facets
    facet_points = [
        (s_w*0.5, s_h*0.1),             # Top
        (s_w*0.4, s_h*0.3),             # Left facet
        (s_w*0.6, s_h*0.3),             # Right facet
    ]
    pygame.draw.polygon(sprite_surface, shine_color, facet_points)
    
    # Dark edge
    pygame.draw.polygon(sprite_surface, dark_ruby_color, gem_points, 1)
    
    return sprite_surface


def get_leather_armor_sprite(size: Tuple[int, int] = (20, 20)) -> pygame.Surface:
    """Generate leather armor sprite procedurally."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size
    
    # Colors
    leather_color = (139, 69, 19)       # Brown leather
    strap_color = (101, 67, 33)         # Dark brown straps
    buckle_color = (192, 192, 192)      # Silver buckles
    
    # Chest piece
    chest_rect = pygame.Rect(s_w*0.2, s_h*0.2, s_w*0.6, s_h*0.6)
    pygame.draw.ellipse(sprite_surface, leather_color, chest_rect)
    
    # Straps
    pygame.draw.rect(sprite_surface, strap_color, 
        (s_w*0.1, s_h*0.3, s_w*0.1, s_h*0.4))
    pygame.draw.rect(sprite_surface, strap_color, 
        (s_w*0.8, s_h*0.3, s_w*0.1, s_h*0.4))
    
    # Buckles
    pygame.draw.circle(sprite_surface, buckle_color, 
        (int(s_w*0.15), int(s_h*0.4)), 2)
    pygame.draw.circle(sprite_surface, buckle_color, 
        (int(s_w*0.85), int(s_h*0.4)), 2)
    
    return sprite_surface


def get_iron_helmet_sprite(size: Tuple[int, int] = (18, 18)) -> pygame.Surface:
    """Generate iron helmet sprite procedurally."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size
    
    # Colors
    iron_color = (105, 105, 105)        # Gray iron
    shine_color = (192, 192, 192)       # Silver shine
    dark_iron_color = (70, 70, 70)      # Dark gray
    
    # Helmet dome
    helmet_rect = pygame.Rect(s_w*0.1, s_h*0.1, s_w*0.8, s_h*0.7)
    pygame.draw.ellipse(sprite_surface, iron_color, helmet_rect)
    
    # Shine
    shine_rect = pygame.Rect(s_w*0.2, s_h*0.15, s_w*0.3, s_h*0.3)
    pygame.draw.ellipse(sprite_surface, shine_color, shine_rect)
    
    # Visor/face guard
    visor_rect = pygame.Rect(s_w*0.25, s_h*0.45, s_w*0.5, s_h*0.2)
    pygame.draw.rect(sprite_surface, dark_iron_color, visor_rect)
    
    # Eye slits
    pygame.draw.line(sprite_surface, (0, 0, 0), 
        (s_w*0.3, s_h*0.5), (s_w*0.4, s_h*0.5), 1)
    pygame.draw.line(sprite_surface, (0, 0, 0), 
        (s_w*0.6, s_h*0.5), (s_w*0.7, s_h*0.5), 1)
    
    return sprite_surface


def get_iron_breastplate_sprite(size: Tuple[int, int] = (20, 20)) -> pygame.Surface:
    """Generate iron breastplate sprite procedurally."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    iron_color = (105, 105, 105)        # Gray iron
    shine_color = (192, 192, 192)       # Silver shine
    dark_iron_color = (70, 70, 70)      # Dark gray

    # Main chest plate
    chest_rect = pygame.Rect(s_w*0.2, s_h*0.2, s_w*0.6, s_h*0.6)
    pygame.draw.rect(sprite_surface, iron_color, chest_rect)

    # Shoulder guards
    left_shoulder = pygame.Rect(s_w*0.05, s_h*0.15, s_w*0.25, s_h*0.3)
    right_shoulder = pygame.Rect(s_w*0.7, s_h*0.15, s_w*0.25, s_h*0.3)
    pygame.draw.ellipse(sprite_surface, iron_color, left_shoulder)
    pygame.draw.ellipse(sprite_surface, iron_color, right_shoulder)

    # Central ridge
    pygame.draw.line(sprite_surface, dark_iron_color,
        (s_w*0.5, s_h*0.2), (s_w*0.5, s_h*0.8), 1)

    # Shine on chest
    shine_rect = pygame.Rect(s_w*0.3, s_h*0.25, s_w*0.2, s_h*0.3)
    pygame.draw.ellipse(sprite_surface, shine_color, shine_rect)

    return sprite_surface


def get_iron_greaves_sprite(size: Tuple[int, int] = (18, 18)) -> pygame.Surface:
    """Generate iron greaves sprite procedurally."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    iron_color = (105, 105, 105)        # Gray iron
    shine_color = (192, 192, 192)       # Silver shine
    dark_iron_color = (70, 70, 70)      # Dark gray

    # Left greave
    left_greave = pygame.Rect(s_w*0.15, s_h*0.2, s_w*0.25, s_h*0.6)
    pygame.draw.rect(sprite_surface, iron_color, left_greave)

    # Right greave
    right_greave = pygame.Rect(s_w*0.6, s_h*0.2, s_w*0.25, s_h*0.6)
    pygame.draw.rect(sprite_surface, iron_color, right_greave)

    # Knee guards
    left_knee = pygame.Rect(s_w*0.1, s_h*0.15, s_w*0.35, s_h*0.15)
    right_knee = pygame.Rect(s_w*0.55, s_h*0.15, s_w*0.35, s_h*0.15)
    pygame.draw.ellipse(sprite_surface, iron_color, left_knee)
    pygame.draw.ellipse(sprite_surface, iron_color, right_knee)

    # Shine on knee guards
    pygame.draw.ellipse(sprite_surface, shine_color,
        (s_w*0.15, s_h*0.17, s_w*0.15, s_h*0.08))
    pygame.draw.ellipse(sprite_surface, shine_color,
        (s_w*0.7, s_h*0.17, s_w*0.15, s_h*0.08))

    return sprite_surface


def get_iron_boots_sprite(size: Tuple[int, int] = (16, 16)) -> pygame.Surface:
    """Generate iron boots sprite procedurally."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    iron_color = (105, 105, 105)        # Gray iron
    shine_color = (192, 192, 192)       # Silver shine
    dark_iron_color = (70, 70, 70)      # Dark gray

    # Left boot
    left_boot = pygame.Rect(s_w*0.1, s_h*0.4, s_w*0.3, s_h*0.4)
    pygame.draw.rect(sprite_surface, iron_color, left_boot)

    # Right boot
    right_boot = pygame.Rect(s_w*0.6, s_h*0.4, s_w*0.3, s_h*0.4)
    pygame.draw.rect(sprite_surface, iron_color, right_boot)

    # Toe caps (rounded)
    left_toe = pygame.Rect(s_w*0.05, s_h*0.65, s_w*0.2, s_h*0.2)
    right_toe = pygame.Rect(s_w*0.75, s_h*0.65, s_w*0.2, s_h*0.2)
    pygame.draw.ellipse(sprite_surface, iron_color, left_toe)
    pygame.draw.ellipse(sprite_surface, iron_color, right_toe)

    # Shine on toe caps
    pygame.draw.ellipse(sprite_surface, shine_color,
        (s_w*0.08, s_h*0.68, s_w*0.1, s_h*0.08))
    pygame.draw.ellipse(sprite_surface, shine_color,
        (s_w*0.82, s_h*0.68, s_w*0.1, s_h*0.08))

    return sprite_surface


def get_ancient_key_sprite(size: Tuple[int, int] = (16, 20)) -> pygame.Surface:
    """Generate ancient key sprite procedurally."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size
    
    # Colors
    gold_color = (255, 215, 0)          # Gold
    dark_gold_color = (218, 165, 32)    # Dark gold
    rune_color = (138, 43, 226)         # Purple runes
    
    # Key shaft
    shaft_rect = pygame.Rect(s_w*0.4, s_h*0.3, s_w*0.2, s_h*0.5)
    pygame.draw.rect(sprite_surface, gold_color, shaft_rect)
    
    # Key head (ornate circle)
    head_center = (s_w // 2, int(s_h*0.2))
    pygame.draw.circle(sprite_surface, gold_color, head_center, int(s_w*0.25))
    pygame.draw.circle(sprite_surface, dark_gold_color, head_center, int(s_w*0.25), 2)
    
    # Inner decoration
    pygame.draw.circle(sprite_surface, rune_color, head_center, int(s_w*0.15))
    
    # Key teeth
    teeth_points = [
        (s_w*0.6, s_h*0.7),
        (s_w*0.8, s_h*0.7),
        (s_w*0.8, s_h*0.75),
        (s_w*0.6, s_h*0.75),
    ]
    pygame.draw.polygon(sprite_surface, gold_color, teeth_points)
    
    return sprite_surface


def get_base_player_sprite(size: Tuple[int, int] = (128, 128)) -> pygame.Surface:
    """Generate detailed base human character sprite for equipment layering."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Enhanced color palette
    skin_color = (255, 220, 177)        # Warm peach skin
    skin_shadow = (235, 200, 157)       # Darker skin for shadows
    skin_highlight = (255, 235, 195)    # Lighter skin for highlights
    hair_color = (139, 69, 19)          # Rich brown hair
    hair_highlight = (160, 85, 35)      # Hair highlights
    hair_shadow = (120, 55, 15)         # Hair shadows
    underwear_color = (240, 240, 240)   # Light gray underwear
    underwear_shadow = (220, 220, 220)  # Underwear shadows
    eye_color = (70, 130, 180)          # Blue eyes
    eyebrow_color = (100, 50, 25)       # Darker brown eyebrows
    lip_color = (200, 120, 120)         # Natural lip color

    # Head with realistic proportions and features
    head_center = (s_w // 2, int(s_h * 0.18))
    head_radius = int(s_w * 0.09)
    
    # Head base with shading
    pygame.draw.circle(sprite_surface, skin_color, head_center, head_radius)
    pygame.draw.circle(sprite_surface, skin_shadow, 
                      (head_center[0] + 2, head_center[1] + 2), head_radius - 2)
    pygame.draw.circle(sprite_surface, skin_color, 
                      (head_center[0] - 1, head_center[1] - 1), head_radius - 3)
    pygame.draw.circle(sprite_surface, skin_highlight, 
                      (head_center[0] - 3, head_center[1] - 3), head_radius - 6)

    # Detailed hair with texture and layers
    hair_main = pygame.Rect(head_center[0] - int(head_radius * 0.9), head_center[1] - head_radius + 2,
                           int(head_radius * 1.8), int(head_radius * 1.1))
    pygame.draw.ellipse(sprite_surface, hair_color, hair_main)
    
    # Hair highlights and texture
    hair_highlight_rect = pygame.Rect(hair_main.left + 4, hair_main.top + 3, 
                                     hair_main.width - 8, hair_main.height // 3)
    pygame.draw.ellipse(sprite_surface, hair_highlight, hair_highlight_rect)
    
    # Hair shadow areas
    hair_shadow_rect = pygame.Rect(hair_main.left + 2, hair_main.bottom - 8, 
                                  hair_main.width - 4, 6)
    pygame.draw.ellipse(sprite_surface, hair_shadow, hair_shadow_rect)
    
    # Individual hair strands for texture
    for i in range(5):
        strand_x = hair_main.left + 6 + i * 8
        strand_top = hair_main.top + 5
        strand_bottom = hair_main.top + 15
        pygame.draw.line(sprite_surface, hair_highlight, 
                        (strand_x, strand_top), (strand_x, strand_bottom), 1)

    # Detailed facial features
    # Eyes with proper anatomy
    eye_left = (head_center[0] - int(s_w * 0.02), head_center[1] - int(s_h * 0.01))
    eye_right = (head_center[0] + int(s_w * 0.02), head_center[1] - int(s_h * 0.01))
    eye_width = int(s_w * 0.015)
    eye_height = int(s_h * 0.01)
    
    # Eye whites
    eye_left_rect = pygame.Rect(eye_left[0] - eye_width, eye_left[1] - eye_height//2, 
                               eye_width * 2, eye_height)
    eye_right_rect = pygame.Rect(eye_right[0] - eye_width, eye_right[1] - eye_height//2, 
                                eye_width * 2, eye_height)
    pygame.draw.ellipse(sprite_surface, (255, 255, 255), eye_left_rect)
    pygame.draw.ellipse(sprite_surface, (255, 255, 255), eye_right_rect)
    
    # Irises
    iris_radius = int(eye_width * 0.7)
    pygame.draw.circle(sprite_surface, eye_color, eye_left, iris_radius)
    pygame.draw.circle(sprite_surface, eye_color, eye_right, iris_radius)
    
    # Pupils
    pupil_radius = int(iris_radius * 0.5)
    pygame.draw.circle(sprite_surface, (0, 0, 0), eye_left, pupil_radius)
    pygame.draw.circle(sprite_surface, (0, 0, 0), eye_right, pupil_radius)
    
    # Eye highlights
    pygame.draw.circle(sprite_surface, (255, 255, 255), 
                      (eye_left[0] - 1, eye_left[1] - 1), 2)
    pygame.draw.circle(sprite_surface, (255, 255, 255), 
                      (eye_right[0] - 1, eye_right[1] - 1), 2)

    # Eyebrows
    eyebrow_left = pygame.Rect(eye_left[0] - 6, eye_left[1] - 8, 12, 3)
    eyebrow_right = pygame.Rect(eye_right[0] - 6, eye_right[1] - 8, 12, 3)
    pygame.draw.ellipse(sprite_surface, eyebrow_color, eyebrow_left)
    pygame.draw.ellipse(sprite_surface, eyebrow_color, eyebrow_right)

    # Nose (simple but defined)
    nose_tip = (head_center[0], head_center[1] + int(s_h * 0.015))
    pygame.draw.circle(sprite_surface, skin_shadow, nose_tip, 2)
    nose_bridge = pygame.Rect(nose_tip[0] - 1, nose_tip[1] - 6, 2, 6)
    pygame.draw.rect(sprite_surface, skin_shadow, nose_bridge)

    # Mouth with natural lips
    mouth_center = (head_center[0], head_center[1] + int(s_h * 0.035))
    mouth_rect = pygame.Rect(mouth_center[0] - 6, mouth_center[1] - 2, 12, 4)
    pygame.draw.ellipse(sprite_surface, lip_color, mouth_rect)
    
    # Subtle mouth line
    pygame.draw.line(sprite_surface, skin_shadow, 
                    (mouth_center[0] - 4, mouth_center[1]), 
                    (mouth_center[0] + 4, mouth_center[1]), 1)

    # Defined jawline
    jaw_points = [
        (head_center[0] - int(head_radius * 0.7), head_center[1] + int(head_radius * 0.6)),
        (head_center[0], head_center[1] + int(head_radius * 0.8)),
        (head_center[0] + int(head_radius * 0.7), head_center[1] + int(head_radius * 0.6))
    ]
    for i in range(len(jaw_points) - 1):
        pygame.draw.line(sprite_surface, skin_shadow, jaw_points[i], jaw_points[i + 1], 1)

    # Neck with proper anatomy
    neck_rect = pygame.Rect(int(s_w * 0.45), int(s_h * 0.25), int(s_w * 0.1), int(s_h * 0.08))
    pygame.draw.rect(sprite_surface, skin_color, neck_rect)
    pygame.draw.rect(sprite_surface, skin_shadow, 
                    (neck_rect.left + 1, neck_rect.top + 2, neck_rect.width - 2, neck_rect.height - 4))
    
    # Adam's apple (subtle)
    adam_apple = (neck_rect.centerx, neck_rect.centery + 2)
    pygame.draw.circle(sprite_surface, skin_shadow, adam_apple, 2)

    # Torso with realistic proportions and muscle definition
    torso_rect = pygame.Rect(int(s_w * 0.33), int(s_h * 0.32), int(s_w * 0.34), int(s_h * 0.38))
    pygame.draw.rect(sprite_surface, skin_color, torso_rect)
    
    # Chest muscle definition
    chest_left = pygame.Rect(torso_rect.left + 4, torso_rect.top + 6, 
                            int(torso_rect.width * 0.35), int(torso_rect.height * 0.3))
    chest_right = pygame.Rect(torso_rect.right - 4 - int(torso_rect.width * 0.35), torso_rect.top + 6,
                             int(torso_rect.width * 0.35), int(torso_rect.height * 0.3))
    pygame.draw.ellipse(sprite_surface, skin_highlight, chest_left)
    pygame.draw.ellipse(sprite_surface, skin_highlight, chest_right)
    
    # Abdominal definition
    abs_rect = pygame.Rect(torso_rect.centerx - 8, torso_rect.top + int(torso_rect.height * 0.4),
                          16, int(torso_rect.height * 0.5))
    pygame.draw.rect(sprite_surface, skin_shadow, abs_rect)
    
    # Individual ab segments
    for i in range(3):
        ab_segment = pygame.Rect(abs_rect.left + 2, abs_rect.top + 2 + i * 8, 
                                abs_rect.width - 4, 6)
        pygame.draw.rect(sprite_surface, skin_highlight, ab_segment)

    # Shoulders with proper anatomy
    shoulder_left = (int(s_w * 0.28), int(s_h * 0.34))
    shoulder_right = (int(s_w * 0.72), int(s_h * 0.34))
    shoulder_radius = int(s_w * 0.05)
    pygame.draw.circle(sprite_surface, skin_color, shoulder_left, shoulder_radius)
    pygame.draw.circle(sprite_surface, skin_color, shoulder_right, shoulder_radius)
    pygame.draw.circle(sprite_surface, skin_highlight, 
                      (shoulder_left[0] - 2, shoulder_left[1] - 2), shoulder_radius - 3)
    pygame.draw.circle(sprite_surface, skin_highlight, 
                      (shoulder_right[0] + 2, shoulder_right[1] - 2), shoulder_radius - 3)

    # Enhanced arms with muscle definition
    # Left arm (upper and lower)
    left_upper_arm = pygame.Rect(int(s_w * 0.24), int(s_h * 0.38), int(s_w * 0.08), int(s_h * 0.15))
    left_lower_arm = pygame.Rect(int(s_w * 0.24), int(s_h * 0.53), int(s_w * 0.07), int(s_h * 0.15))
    pygame.draw.rect(sprite_surface, skin_color, left_upper_arm)
    pygame.draw.rect(sprite_surface, skin_color, left_lower_arm)
    
    # Muscle definition on left arm
    bicep_highlight = pygame.Rect(left_upper_arm.left + 1, left_upper_arm.top + 2,
                                 left_upper_arm.width - 2, left_upper_arm.height // 2)
    pygame.draw.rect(sprite_surface, skin_highlight, bicep_highlight)
    
    # Right arm (upper and lower)
    right_upper_arm = pygame.Rect(int(s_w * 0.68), int(s_h * 0.38), int(s_w * 0.08), int(s_h * 0.15))
    right_lower_arm = pygame.Rect(int(s_w * 0.69), int(s_h * 0.53), int(s_w * 0.07), int(s_h * 0.15))
    pygame.draw.rect(sprite_surface, skin_color, right_upper_arm)
    pygame.draw.rect(sprite_surface, skin_color, right_lower_arm)
    
    # Muscle definition on right arm
    bicep_highlight = pygame.Rect(right_upper_arm.left + 1, right_upper_arm.top + 2,
                                 right_upper_arm.width - 2, right_upper_arm.height // 2)
    pygame.draw.rect(sprite_surface, skin_highlight, bicep_highlight)

    # Hands with fingers
    left_hand = (int(s_w * 0.27), int(s_h * 0.68))
    right_hand = (int(s_w * 0.73), int(s_h * 0.68))
    hand_radius = int(s_w * 0.025)
    
    # Hand palms
    pygame.draw.circle(sprite_surface, skin_color, left_hand, hand_radius)
    pygame.draw.circle(sprite_surface, skin_color, right_hand, hand_radius)
    
    # Simple finger representation
    for i in range(4):
        finger_offset = i - 1.5
        # Left hand fingers
        finger_x = left_hand[0] + int(finger_offset * 3)
        finger_y = left_hand[1] + hand_radius - 2
        pygame.draw.circle(sprite_surface, skin_color, (finger_x, finger_y), 2)
        
        # Right hand fingers
        finger_x = right_hand[0] + int(finger_offset * 3)
        finger_y = right_hand[1] + hand_radius - 2
        pygame.draw.circle(sprite_surface, skin_color, (finger_x, finger_y), 2)

    # Enhanced underwear with proper fit
    underwear_main = pygame.Rect(int(s_w * 0.37), int(s_h * 0.54), int(s_w * 0.26), int(s_h * 0.16))
    pygame.draw.rect(sprite_surface, underwear_color, underwear_main)
    pygame.draw.rect(sprite_surface, underwear_shadow, 
                    (underwear_main.left + 1, underwear_main.bottom - 4, 
                     underwear_main.width - 2, 3))
    
    # Underwear waistband
    waistband = pygame.Rect(underwear_main.left, underwear_main.top, 
                           underwear_main.width, 4)
    pygame.draw.rect(sprite_surface, underwear_shadow, waistband)

    # Legs with realistic anatomy and muscle definition
    # Left leg (thigh and calf)
    left_thigh = pygame.Rect(int(s_w * 0.37), int(s_h * 0.70), int(s_w * 0.11), int(s_h * 0.18))
    left_calf = pygame.Rect(int(s_w * 0.37), int(s_h * 0.88), int(s_w * 0.10), int(s_h * 0.10))
    pygame.draw.rect(sprite_surface, skin_color, left_thigh)
    pygame.draw.rect(sprite_surface, skin_color, left_calf)
    
    # Thigh muscle definition
    quad_highlight = pygame.Rect(left_thigh.left + 2, left_thigh.top + 2,
                                left_thigh.width - 4, left_thigh.height // 2)
    pygame.draw.rect(sprite_surface, skin_highlight, quad_highlight)
    
    # Calf muscle definition
    calf_highlight = pygame.Rect(left_calf.left + 2, left_calf.top + 2,
                                left_calf.width - 4, left_calf.height - 4)
    pygame.draw.ellipse(sprite_surface, skin_highlight, calf_highlight)
    
    # Right leg (thigh and calf)
    right_thigh = pygame.Rect(int(s_w * 0.52), int(s_h * 0.70), int(s_w * 0.11), int(s_h * 0.18))
    right_calf = pygame.Rect(int(s_w * 0.53), int(s_h * 0.88), int(s_w * 0.10), int(s_h * 0.10))
    pygame.draw.rect(sprite_surface, skin_color, right_thigh)
    pygame.draw.rect(sprite_surface, skin_color, right_calf)
    
    # Thigh muscle definition
    quad_highlight = pygame.Rect(right_thigh.left + 2, right_thigh.top + 2,
                                right_thigh.width - 4, right_thigh.height // 2)
    pygame.draw.rect(sprite_surface, skin_highlight, quad_highlight)
    
    # Calf muscle definition
    calf_highlight = pygame.Rect(right_calf.left + 2, right_calf.top + 2,
                                right_calf.width - 4, right_calf.height - 4)
    pygame.draw.ellipse(sprite_surface, skin_highlight, calf_highlight)

    # Knee definition
    left_knee = (left_thigh.centerx, left_thigh.bottom)
    right_knee = (right_thigh.centerx, right_thigh.bottom)
    pygame.draw.circle(sprite_surface, skin_shadow, left_knee, 4)
    pygame.draw.circle(sprite_surface, skin_shadow, right_knee, 4)

    # Feet with proper anatomy
    left_foot = pygame.Rect(int(s_w * 0.35), int(s_h * 0.97), int(s_w * 0.15), int(s_h * 0.025))
    right_foot = pygame.Rect(int(s_w * 0.50), int(s_h * 0.97), int(s_w * 0.15), int(s_h * 0.025))
    pygame.draw.ellipse(sprite_surface, skin_color, left_foot)
    pygame.draw.ellipse(sprite_surface, skin_color, right_foot)
    
    # Toes
    for i in range(5):
        toe_offset = i - 2
        # Left foot toes
        toe_x = left_foot.centerx + toe_offset * 3
        toe_y = left_foot.top - 2
        pygame.draw.circle(sprite_surface, skin_color, (toe_x, toe_y), 2)
        
        # Right foot toes
        toe_x = right_foot.centerx + toe_offset * 3
        toe_y = right_foot.top - 2
        pygame.draw.circle(sprite_surface, skin_color, (toe_x, toe_y), 2)

    # Ankle definition
    left_ankle = (left_calf.centerx, left_calf.bottom + 2)
    right_ankle = (right_calf.centerx, right_calf.bottom + 2)
    pygame.draw.circle(sprite_surface, skin_shadow, left_ankle, 3)
    pygame.draw.circle(sprite_surface, skin_shadow, right_ankle, 3)

    return sprite_surface


def get_equipped_player_sprite(
    equipment_ids: Dict[str, Optional[str]],
    size: Tuple[int, int] = (128, 128)
) -> pygame.Surface:
    """Generate player sprite with equipment layered on top."""
    # Start with base player sprite
    player_surface = get_base_player_sprite(size)

    # Layer equipment in proper order (back to front)
    layer_order = [
        "legs_equipment",      # Pants/leg armor (behind torso)
        "boots_equipment",     # Boots/foot armor
        "chest_equipment",     # Shirt/chest armor (over torso)
        "head_equipment",      # Hat/helmet (over head)
        "off_hand_equipment",  # Shield (in off-hand)
        "main_hand_weapon"     # Weapon (in main hand)
    ]

    for slot in layer_order:
        equipment_id = equipment_ids.get(slot)
        if equipment_id:
            player_surface = render_equipment_on_player(
                player_surface, equipment_id, slot, size
            )

    return player_surface


def render_equipment_on_player(
    player_surface: pygame.Surface,
    equipment_id: str,
    slot: str,
    size: Tuple[int, int]
) -> pygame.Surface:
    """Render specific equipment piece onto player base."""
    # Import here to avoid circular imports
    try:
        from src.game_data.items import get_item_definition
        item_def = get_item_definition(equipment_id)
        if not item_def:
            print(f"ERROR: Item definition not found for equipment '{equipment_id}' in slot '{slot}'")
            return player_surface

        props = item_def.properties
        visual_style = props.get("visual_style", "default")
        color_primary = props.get("color_primary", (128, 128, 128))
        color_secondary = props.get("color_secondary", (64, 64, 64))

        # Create a copy of the player surface to draw on
        result_surface = player_surface.copy()
        s_w, s_h = size

        # Render equipment based on slot and visual style
        try:
            if slot == "head_equipment":
                _render_head_equipment(result_surface, visual_style, color_primary, color_secondary, size)
            elif slot == "chest_equipment":
                _render_chest_equipment(result_surface, visual_style, color_primary, color_secondary, size)
            elif slot == "legs_equipment":
                _render_legs_equipment(result_surface, visual_style, color_primary, color_secondary, size)
            elif slot == "boots_equipment":
                _render_boots_equipment(result_surface, visual_style, color_primary, color_secondary, size)
            elif slot == "main_hand_weapon":
                _render_main_hand_weapon(result_surface, visual_style, color_primary, color_secondary, size, props)
            elif slot == "off_hand_equipment":
                _render_off_hand_equipment(result_surface, visual_style, color_primary, color_secondary, size, props)
            else:
                print(f"ERROR: Unknown equipment slot '{slot}' for item '{equipment_id}'")
        except Exception as e:
            print(f"ERROR: Failed to render equipment '{equipment_id}' in slot '{slot}': {e}")
            print(f"  Visual style: {visual_style}")
            print(f"  Item properties: {props}")

        return result_surface

    except ImportError as e:
        print(f"ERROR: Failed to import item definitions for equipment rendering: {e}")
        return player_surface
    except Exception as e:
        print(f"ERROR: Unexpected error rendering equipment '{equipment_id}' in slot '{slot}': {e}")
        return player_surface


# Equipment rendering functions

def _render_head_equipment(surface: pygame.Surface, visual_style: str,
                          color_primary: Tuple[int, int, int],
                          color_secondary: Tuple[int, int, int],
                          size: Tuple[int, int]) -> None:
    """Render head equipment (hats, helmets, caps)."""
    s_w, s_h = size

    if visual_style == "simple_cap":
        # Simple cloth cap
        cap_rect = pygame.Rect(s_w*0.36, s_h*0.08, s_w*0.28, s_h*0.12)
        pygame.draw.ellipse(surface, color_primary, cap_rect)
        # Add a small brim
        brim_rect = pygame.Rect(s_w*0.34, s_h*0.15, s_w*0.32, s_h*0.04)
        pygame.draw.ellipse(surface, color_secondary, brim_rect)

    elif visual_style == "light_cap":
        # Leather cap
        cap_rect = pygame.Rect(s_w*0.37, s_h*0.09, s_w*0.26, s_h*0.11)
        pygame.draw.ellipse(surface, color_primary, cap_rect)

    elif visual_style == "full_helmet":
        # Iron helmet covering most of head
        helmet_rect = pygame.Rect(s_w*0.35, s_h*0.08, s_w*0.3, s_h*0.18)
        pygame.draw.ellipse(surface, color_primary, helmet_rect)
        # Add visor/face guard
        visor_rect = pygame.Rect(s_w*0.42, s_h*0.18, s_w*0.16, s_h*0.06)
        pygame.draw.rect(surface, color_secondary, visor_rect)


def _render_chest_equipment(surface: pygame.Surface, visual_style: str,
                           color_primary: Tuple[int, int, int],
                           color_secondary: Tuple[int, int, int],
                           size: Tuple[int, int]) -> None:
    """Render chest equipment (shirts, armor, vests)."""
    s_w, s_h = size

    if visual_style == "simple_shirt":
        # Basic cloth shirt
        shirt_rect = pygame.Rect(s_w*0.32, s_h*0.32, s_w*0.36, s_h*0.28)
        pygame.draw.rect(surface, color_primary, shirt_rect)
        # Add sleeves
        left_sleeve = pygame.Rect(s_w*0.25, s_h*0.35, s_w*0.08, s_h*0.2)
        right_sleeve = pygame.Rect(s_w*0.67, s_h*0.35, s_w*0.08, s_h*0.2)
        pygame.draw.rect(surface, color_primary, left_sleeve)
        pygame.draw.rect(surface, color_primary, right_sleeve)

    elif visual_style == "leather_vest":
        # Leather armor vest
        vest_rect = pygame.Rect(s_w*0.33, s_h*0.33, s_w*0.34, s_h*0.26)
        pygame.draw.rect(surface, color_primary, vest_rect)
        # Add leather straps/details
        strap1 = pygame.Rect(s_w*0.36, s_h*0.36, s_w*0.28, s_h*0.02)
        strap2 = pygame.Rect(s_w*0.36, s_h*0.45, s_w*0.28, s_h*0.02)
        pygame.draw.rect(surface, color_secondary, strap1)
        pygame.draw.rect(surface, color_secondary, strap2)

    elif visual_style == "plate_chest":
        # Iron plate chest armor
        chest_rect = pygame.Rect(s_w*0.32, s_h*0.32, s_w*0.36, s_h*0.28)
        pygame.draw.rect(surface, color_primary, chest_rect)
        # Add shoulder guards
        left_shoulder = pygame.Rect(s_w*0.25, s_h*0.30, s_w*0.12, s_h*0.15)
        right_shoulder = pygame.Rect(s_w*0.63, s_h*0.30, s_w*0.12, s_h*0.15)
        pygame.draw.ellipse(surface, color_primary, left_shoulder)
        pygame.draw.ellipse(surface, color_primary, right_shoulder)
        # Add central ridge
        pygame.draw.line(surface, color_secondary,
            (s_w*0.5, s_h*0.32), (s_w*0.5, s_h*0.6), 2)
        # Add horizontal plate segments
        for i in range(2):
            y_pos = s_h*0.38 + i * s_h*0.08
            pygame.draw.line(surface, color_secondary,
                (s_w*0.34, y_pos), (s_w*0.66, y_pos), 1)


def _render_legs_equipment(surface: pygame.Surface, visual_style: str,
                          color_primary: Tuple[int, int, int],
                          color_secondary: Tuple[int, int, int],
                          size: Tuple[int, int]) -> None:
    """Render leg equipment (pants, leg armor)."""
    s_w, s_h = size

    if visual_style == "simple_pants":
        # Basic cloth pants
        left_leg = pygame.Rect(s_w*0.36, s_h*0.55, s_w*0.12, s_h*0.3)
        right_leg = pygame.Rect(s_w*0.52, s_h*0.55, s_w*0.12, s_h*0.3)
        pygame.draw.rect(surface, color_primary, left_leg)
        pygame.draw.rect(surface, color_primary, right_leg)

    elif visual_style == "leather_pants":
        # Leather pants with reinforced knees
        left_leg = pygame.Rect(s_w*0.35, s_h*0.55, s_w*0.13, s_h*0.31)
        right_leg = pygame.Rect(s_w*0.52, s_h*0.55, s_w*0.13, s_h*0.31)
        pygame.draw.rect(surface, color_primary, left_leg)
        pygame.draw.rect(surface, color_primary, right_leg)
        # Add leather stitching
        pygame.draw.rect(surface, color_secondary, left_leg, 1)
        pygame.draw.rect(surface, color_secondary, right_leg, 1)
        # Knee reinforcement patches
        left_knee = pygame.Rect(s_w*0.37, s_h*0.67, s_w*0.09, s_h*0.06)
        right_knee = pygame.Rect(s_w*0.54, s_h*0.67, s_w*0.09, s_h*0.06)
        pygame.draw.rect(surface, color_secondary, left_knee)
        pygame.draw.rect(surface, color_secondary, right_knee)

    elif visual_style == "plate_legs":
        # Heavy plate leg armor
        left_leg = pygame.Rect(s_w*0.35, s_h*0.55, s_w*0.14, s_h*0.32)
        right_leg = pygame.Rect(s_w*0.51, s_h*0.55, s_w*0.14, s_h*0.32)
        pygame.draw.rect(surface, color_primary, left_leg)
        pygame.draw.rect(surface, color_primary, right_leg)
        # Add plate details
        left_knee = pygame.Rect(s_w*0.37, s_h*0.68, s_w*0.1, s_h*0.04)
        right_knee = pygame.Rect(s_w*0.53, s_h*0.68, s_w*0.1, s_h*0.04)
        pygame.draw.rect(surface, color_secondary, left_knee)
        pygame.draw.rect(surface, color_secondary, right_knee)


def _render_boots_equipment(surface: pygame.Surface, visual_style: str,
                           color_primary: Tuple[int, int, int],
                           color_secondary: Tuple[int, int, int],
                           size: Tuple[int, int]) -> None:
    """Render boot equipment."""
    s_w, s_h = size

    if visual_style == "simple_shoes":
        # Basic shoes
        left_shoe = pygame.Rect(s_w*0.36, s_h*0.92, s_w*0.14, s_h*0.06)
        right_shoe = pygame.Rect(s_w*0.5, s_h*0.92, s_w*0.14, s_h*0.06)
        pygame.draw.rect(surface, color_primary, left_shoe)
        pygame.draw.rect(surface, color_primary, right_shoe)

    elif visual_style == "simple_boots":
        # Leather boots
        left_boot = pygame.Rect(s_w*0.35, s_h*0.88, s_w*0.15, s_h*0.1)
        right_boot = pygame.Rect(s_w*0.5, s_h*0.88, s_w*0.15, s_h*0.1)
        pygame.draw.rect(surface, color_primary, left_boot)
        pygame.draw.rect(surface, color_primary, right_boot)
        # Add boot details
        left_lace = pygame.Rect(s_w*0.37, s_h*0.9, s_w*0.11, s_h*0.02)
        right_lace = pygame.Rect(s_w*0.52, s_h*0.9, s_w*0.11, s_h*0.02)
        pygame.draw.rect(surface, color_secondary, left_lace)
        pygame.draw.rect(surface, color_secondary, right_lace)

    elif visual_style == "plate_boots":
        # Iron plate boots
        left_boot = pygame.Rect(s_w*0.34, s_h*0.86, s_w*0.16, s_h*0.12)
        right_boot = pygame.Rect(s_w*0.5, s_h*0.86, s_w*0.16, s_h*0.12)
        pygame.draw.rect(surface, color_primary, left_boot)
        pygame.draw.rect(surface, color_primary, right_boot)
        # Add toe caps (rounded)
        left_toe = pygame.Rect(s_w*0.34, s_h*0.94, s_w*0.08, s_h*0.04)
        right_toe = pygame.Rect(s_w*0.58, s_h*0.94, s_w*0.08, s_h*0.04)
        pygame.draw.ellipse(surface, color_primary, left_toe)
        pygame.draw.ellipse(surface, color_primary, right_toe)
        # Add ankle guards
        left_ankle = pygame.Rect(s_w*0.33, s_h*0.84, s_w*0.18, s_h*0.06)
        right_ankle = pygame.Rect(s_w*0.49, s_h*0.84, s_w*0.18, s_h*0.06)
        pygame.draw.ellipse(surface, color_primary, left_ankle)
        pygame.draw.ellipse(surface, color_primary, right_ankle)
        # Add plate segments
        pygame.draw.line(surface, color_secondary,
            (s_w*0.36, s_h*0.9), (s_w*0.48, s_h*0.9), 1)
        pygame.draw.line(surface, color_secondary,
            (s_w*0.52, s_h*0.9), (s_w*0.64, s_h*0.9), 1)


def _render_main_hand_weapon(surface: pygame.Surface, visual_style: str,
                            color_primary: Tuple[int, int, int],
                            color_secondary: Tuple[int, int, int],
                            size: Tuple[int, int], props: Dict) -> None:
    """Render main hand weapon (swords, bows, etc)."""
    s_w, s_h = size
    weapon_type = props.get("weapon_type", "sword")

    if weapon_type == "sword" and visual_style == "straight_sword":
        # Sword in right hand
        # Blade
        blade_rect = pygame.Rect(s_w*0.75, s_h*0.25, s_w*0.04, s_h*0.4)
        pygame.draw.rect(surface, color_primary, blade_rect)
        # Hilt/handle
        hilt_rect = pygame.Rect(s_w*0.74, s_h*0.65, s_w*0.06, s_h*0.08)
        pygame.draw.rect(surface, color_secondary, hilt_rect)
        # Crossguard
        guard_rect = pygame.Rect(s_w*0.72, s_h*0.63, s_w*0.1, s_h*0.02)
        pygame.draw.rect(surface, color_secondary, guard_rect)

    elif weapon_type == "bow" and visual_style == "longbow":
        # Bow in left hand (held vertically)
        # Bow stave
        bow_rect = pygame.Rect(s_w*0.22, s_h*0.2, s_w*0.03, s_h*0.5)
        pygame.draw.rect(surface, color_primary, bow_rect)
        # Bowstring
        string_rect = pygame.Rect(s_w*0.26, s_h*0.22, s_w*0.01, s_h*0.46)
        pygame.draw.rect(surface, color_secondary, string_rect)

    elif weapon_type == "axe" and visual_style == "battle_axe":
        # Double crescent battle axe in right hand - rotated 90 degrees (vertical orientation)
        # Handle positioned to align with player's hand grip (around 45% down) and extend downward
        handle_rect = pygame.Rect(s_w*0.73, s_h*0.45, s_w*0.06, s_h*0.35)
        pygame.draw.rect(surface, color_secondary, handle_rect)  # Wood handle (brown)

        # Create a complete vertical axe head shape (rotated 90 degrees from icon)
        # Axe head positioned above the handle grip point, from 15% to 45% height
        axe_head_points = [
            # Left crescent (top side when rotated)
            (s_w*0.66, s_h*0.15),           # Left top tip  
            (s_w*0.70, s_h*0.20),           # Left top curve
            (s_w*0.72, s_h*0.30),           # Left top base
            (s_w*0.73, s_h*0.40),           # Connection to handle (top left)
            
            # Connect to right side
            (s_w*0.79, s_h*0.40),           # Connection to handle (top right)
            (s_w*0.80, s_h*0.30),           # Right top base
            (s_w*0.82, s_h*0.20),           # Right top curve
            (s_w*0.86, s_h*0.15),           # Right top tip
            
            # Right crescent (bottom side when rotated)
            (s_w*0.86, s_h*0.50),           # Right bottom tip
            (s_w*0.82, s_h*0.45),           # Right bottom curve
            (s_w*0.80, s_h*0.42),           # Right bottom base
            (s_w*0.79, s_h*0.40),           # Connection to handle (bottom right)
            
            # Connect to left side
            (s_w*0.73, s_h*0.40),           # Connection to handle (bottom left)
            (s_w*0.72, s_h*0.42),           # Left bottom base
            (s_w*0.70, s_h*0.45),           # Left bottom curve
            (s_w*0.66, s_h*0.50),           # Left bottom tip
        ]
        pygame.draw.polygon(surface, color_primary, axe_head_points)  # Iron axe head (gray)

        # Wood grain on handle - darker wood color for detail
        dark_wood = (101, 67, 33)  # Dark wood color
        for i in range(7):
            y = s_h*0.48 + i * s_h*0.04
            pygame.draw.line(surface, dark_wood,
                (s_w*0.74, y), (s_w*0.78, y), 1)

        # Central binding where blades meet handle - dark iron
        binding_rect = pygame.Rect(s_w*0.74, s_h*0.54, s_w*0.02, s_h*0.06)
        dark_iron = (80, 80, 80)  # Dark iron color
        pygame.draw.rect(surface, dark_iron, binding_rect)


def _render_off_hand_equipment(surface: pygame.Surface, visual_style: str,
                              color_primary: Tuple[int, int, int],
                              color_secondary: Tuple[int, int, int],
                              size: Tuple[int, int], props: Dict) -> None:
    """Render off-hand equipment (shields)."""
    s_w, s_h = size

    if "shield" in visual_style or visual_style in ["round_shield", "kite_shield", "wooden_shield"]:
        # Shield in left hand
        if visual_style == "round_shield" or visual_style == "wooden_shield":
            # Small round shield (wooden shields are typically round)
            shield_center = (int(s_w*0.2), int(s_h*0.45))
            shield_radius = int(s_w*0.08)
            pygame.draw.circle(surface, color_primary, shield_center, shield_radius)
            # Shield boss (center) or iron bands for wooden shields
            boss_radius = int(s_w*0.03)
            pygame.draw.circle(surface, color_secondary, shield_center, boss_radius)

            # For wooden shields, add some wood grain lines
            if visual_style == "wooden_shield":
                # Add horizontal wood grain lines
                for i in range(3):
                    y_offset = shield_center[1] - shield_radius//2 + i * (shield_radius//3)
                    start_x = shield_center[0] - shield_radius//2
                    end_x = shield_center[0] + shield_radius//2
                    pygame.draw.line(surface, color_secondary, (start_x, y_offset), (end_x, y_offset), 1)

        elif visual_style == "kite_shield":
            # Larger kite-shaped shield
            shield_rect = pygame.Rect(s_w*0.12, s_h*0.35, s_w*0.12, s_h*0.25)
            pygame.draw.rect(surface, color_primary, shield_rect)
            # Shield rim
            rim_rect = pygame.Rect(s_w*0.13, s_h*0.36, s_w*0.1, s_h*0.23)
            pygame.draw.rect(surface, color_secondary, rim_rect, 2)


def get_damage_number_sprite(size: Tuple[int, int] = (24, 16)) -> pygame.Surface:
    """Generate damage number display sprite (placeholder for text rendering)."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    # This would typically be handled by text rendering in the actual implementation
    return sprite_surface


def get_slash_effect_sprite(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate slash attack effect sprite procedurally."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size
    
    # Colors
    slash_color = (255, 255, 255, 180)  # Semi-transparent white
    trail_color = (255, 255, 0, 120)    # Semi-transparent yellow
    
    # Slash arc
    import math
    center_x, center_y = s_w // 2, s_h // 2
    
    # Draw multiple lines to create slash effect
    for i in range(5):
        angle = math.radians(-45 + i * 22.5)  # Spread over 90 degrees
        start_x = center_x + math.cos(angle) * (s_w * 0.1)
        start_y = center_y + math.sin(angle) * (s_h * 0.1)
        end_x = center_x + math.cos(angle) * (s_w * 0.4)
        end_y = center_y + math.sin(angle) * (s_h * 0.4)
        
        color = slash_color if i == 2 else trail_color  # Center line brighter
        width = 3 if i == 2 else 2
        
        pygame.draw.line(sprite_surface, color, 
            (int(start_x), int(start_y)), (int(end_x), int(end_y)), width)
    
    return sprite_surface


# ============================================================================
# ITEM ICON GENERATORS
# ============================================================================
# Each item has its own unique icon generation function for UI display

def get_rusty_sword_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate rusty sword icon - metallic gray with rusty brown stripes on blade with simple hilt."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    rust_color = (184, 115, 51)         # Rusty brown
    dark_rust_color = (139, 69, 19)     # Dark rust
    handle_color = (101, 67, 33)        # Brown handle

    # Blade (vertical sword)
    blade_points = [
        (s_w*0.5, s_h*0.1),             # Tip
        (s_w*0.4, s_h*0.65),            # Left edge
        (s_w*0.6, s_h*0.65),            # Right edge
    ]
    pygame.draw.polygon(sprite_surface, rust_color, blade_points)

    # Rust streaks on blade
    pygame.draw.line(sprite_surface, dark_rust_color,
        (s_w*0.45, s_h*0.2), (s_w*0.45, s_h*0.5), 2)
    pygame.draw.circle(sprite_surface, dark_rust_color,
        (int(s_w*0.55), int(s_h*0.35)), 2)

    # Cross guard
    guard_rect = pygame.Rect(s_w*0.3, s_h*0.62, s_w*0.4, s_h*0.06)
    pygame.draw.rect(sprite_surface, rust_color, guard_rect)

    # Handle
    handle_rect = pygame.Rect(s_w*0.45, s_h*0.68, s_w*0.1, s_h*0.25)
    pygame.draw.rect(sprite_surface, handle_color, handle_rect)

    # Pommel
    pommel_center = (int(s_w*0.5), int(s_h*0.9))
    pygame.draw.circle(sprite_surface, rust_color, pommel_center, 3)

    return sprite_surface


def get_iron_sword_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate iron sword icon - metallic gray blade with detailed hilt."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    iron_color = (192, 192, 192)        # Silver/iron
    dark_iron_color = (120, 120, 120)   # Dark gray
    handle_color = (101, 67, 33)        # Brown handle

    # Blade (vertical sword)
    blade_points = [
        (s_w*0.5, s_h*0.1),             # Tip
        (s_w*0.4, s_h*0.65),            # Left edge
        (s_w*0.6, s_h*0.65),            # Right edge
    ]
    pygame.draw.polygon(sprite_surface, iron_color, blade_points)

    # Fuller (blood groove) down center of blade
    pygame.draw.line(sprite_surface, dark_iron_color,
        (s_w*0.5, s_h*0.15), (s_w*0.5, s_h*0.6), 1)

    # Cross guard
    guard_rect = pygame.Rect(s_w*0.25, s_h*0.62, s_w*0.5, s_h*0.06)
    pygame.draw.rect(sprite_surface, iron_color, guard_rect)

    # Handle with grip wrapping
    handle_rect = pygame.Rect(s_w*0.45, s_h*0.68, s_w*0.1, s_h*0.25)
    pygame.draw.rect(sprite_surface, handle_color, handle_rect)

    # Grip wrapping lines
    for y in range(3):
        y_pos = int(s_h*0.72 + y * s_h*0.06)
        pygame.draw.line(sprite_surface, dark_iron_color,
            (s_w*0.45, y_pos), (s_w*0.55, y_pos), 1)

    # Pommel
    pommel_center = (int(s_w*0.5), int(s_h*0.9))
    pygame.draw.circle(sprite_surface, iron_color, pommel_center, 4)
    pygame.draw.circle(sprite_surface, dark_iron_color, pommel_center, 2)

    return sprite_surface


def get_wooden_bow_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate wooden bow icon - curved brown bow with visible string."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    wood_color = (160, 82, 45)          # Saddle brown
    dark_wood_color = (139, 69, 19)     # Dark brown
    string_color = (245, 245, 220)      # Beige string

    # Bow stave (curved)
    # Draw as series of connected lines to create curve
    bow_points = []
    for i in range(11):  # 11 points for smooth curve
        t = i / 10.0  # 0 to 1
        # Quadratic curve
        x = s_w*0.3 + t * s_w*0.4 * (1 - abs(t - 0.5) * 2)  # Curves inward at center
        y = s_h*0.1 + t * s_h*0.8
        bow_points.append((x, y))

    # Draw bow stave
    for i in range(len(bow_points) - 1):
        pygame.draw.line(sprite_surface, wood_color, bow_points[i], bow_points[i+1], 3)

    # Bow tips (nocks)
    pygame.draw.circle(sprite_surface, dark_wood_color,
        (int(bow_points[0][0]), int(bow_points[0][1])), 2)
    pygame.draw.circle(sprite_surface, dark_wood_color,
        (int(bow_points[-1][0]), int(bow_points[-1][1])), 2)

    # Bow string
    pygame.draw.line(sprite_surface, string_color,
        bow_points[0], bow_points[-1], 1)

    # Grip area in center
    center_idx = len(bow_points) // 2
    grip_center = bow_points[center_idx]
    grip_rect = pygame.Rect(grip_center[0]-2, grip_center[1]-8, 4, 16)
    pygame.draw.rect(sprite_surface, dark_wood_color, grip_rect)

    return sprite_surface


def get_wooden_shield_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate wooden shield icon - round wooden shield with wood grain texture."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    wood_color = (160, 82, 45)          # Saddle brown
    dark_wood_color = (139, 69, 19)     # Dark brown
    metal_color = (120, 120, 120)       # Gray metal

    # Shield body (round)
    center = (s_w // 2, s_h // 2)
    radius = int(min(s_w, s_h) * 0.4)
    pygame.draw.circle(sprite_surface, wood_color, center, radius)

    # Wood grain lines
    for i in range(3):
        y_offset = -radius//3 + i * radius//3
        start_x = center[0] - radius//2
        end_x = center[0] + radius//2
        y_pos = center[1] + y_offset
        pygame.draw.line(sprite_surface, dark_wood_color,
            (start_x, y_pos), (end_x, y_pos), 1)

    # Metal rim
    pygame.draw.circle(sprite_surface, metal_color, center, radius, 2)

    # Shield boss (center metal piece)
    boss_radius = radius // 3
    pygame.draw.circle(sprite_surface, metal_color, center, boss_radius)
    pygame.draw.circle(sprite_surface, dark_wood_color, center, boss_radius//2)

    return sprite_surface


def get_iron_shield_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate iron shield icon - metallic shield with reinforced edges."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    iron_color = (192, 192, 192)        # Silver/iron
    dark_iron_color = (120, 120, 120)   # Dark gray

    # Shield body (kite shape)
    center_x, center_y = s_w // 2, s_h // 2
    shield_points = [
        (center_x, center_y - s_h*0.35),        # Top
        (center_x - s_w*0.25, center_y - s_h*0.1),  # Left
        (center_x - s_w*0.2, center_y + s_h*0.2),   # Left bottom
        (center_x, center_y + s_h*0.35),        # Bottom point
        (center_x + s_w*0.2, center_y + s_h*0.2),   # Right bottom
        (center_x + s_w*0.25, center_y - s_h*0.1),  # Right
    ]
    pygame.draw.polygon(sprite_surface, iron_color, shield_points)

    # Reinforcement lines
    for i in range(2):
        y_offset = -s_h*0.1 + i * s_h*0.2
        start_x = center_x - s_w*0.15
        end_x = center_x + s_w*0.15
        y_pos = center_y + y_offset
        pygame.draw.line(sprite_surface, dark_iron_color,
            (start_x, y_pos), (end_x, y_pos), 2)

    # Shield boss
    boss_center = (center_x, center_y - s_h*0.05)
    pygame.draw.circle(sprite_surface, dark_iron_color, boss_center, 4)

    # Border
    pygame.draw.polygon(sprite_surface, dark_iron_color, shield_points, 2)

    return sprite_surface


def get_cloth_cap_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate cloth cap icon - simple brown cap with soft fabric appearance."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    cloth_color = (139, 69, 19)         # Brown
    dark_cloth_color = (101, 67, 33)    # Dark brown

    # Cap shape (rounded top)
    cap_rect = pygame.Rect(s_w*0.25, s_h*0.2, s_w*0.5, s_h*0.4)
    pygame.draw.ellipse(sprite_surface, cloth_color, cap_rect)

    # Brim
    brim_rect = pygame.Rect(s_w*0.2, s_h*0.55, s_w*0.6, s_h*0.1)
    pygame.draw.ellipse(sprite_surface, cloth_color, brim_rect)

    # Fabric folds
    pygame.draw.line(sprite_surface, dark_cloth_color,
        (s_w*0.35, s_h*0.3), (s_w*0.35, s_h*0.5), 1)
    pygame.draw.line(sprite_surface, dark_cloth_color,
        (s_w*0.65, s_h*0.3), (s_w*0.65, s_h*0.5), 1)

    # Shadow under brim
    shadow_rect = pygame.Rect(s_w*0.25, s_h*0.6, s_w*0.5, s_h*0.05)
    pygame.draw.ellipse(sprite_surface, dark_cloth_color, shadow_rect)

    return sprite_surface


def get_cloth_shirt_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate cloth shirt icon - light gray/white shirt with fabric folds."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    cloth_color = (240, 240, 240)       # Light gray/white
    dark_cloth_color = (200, 200, 200)  # Darker gray

    # Shirt body
    shirt_rect = pygame.Rect(s_w*0.2, s_h*0.3, s_w*0.6, s_h*0.5)
    pygame.draw.rect(sprite_surface, cloth_color, shirt_rect)

    # Sleeves
    left_sleeve = pygame.Rect(s_w*0.1, s_h*0.35, s_w*0.15, s_h*0.3)
    right_sleeve = pygame.Rect(s_w*0.75, s_h*0.35, s_w*0.15, s_h*0.3)
    pygame.draw.rect(sprite_surface, cloth_color, left_sleeve)
    pygame.draw.rect(sprite_surface, cloth_color, right_sleeve)

    # Neckline
    neck_rect = pygame.Rect(s_w*0.4, s_h*0.25, s_w*0.2, s_h*0.1)
    pygame.draw.ellipse(sprite_surface, dark_cloth_color, neck_rect)

    # Fabric folds
    pygame.draw.line(sprite_surface, dark_cloth_color,
        (s_w*0.5, s_h*0.4), (s_w*0.5, s_h*0.7), 1)
    pygame.draw.line(sprite_surface, dark_cloth_color,
        (s_w*0.3, s_h*0.45), (s_w*0.7, s_h*0.45), 1)

    return sprite_surface


def get_cloth_pants_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate cloth pants icon - brown simple pants with basic stitching."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    cloth_color = (139, 69, 19)         # Brown
    dark_cloth_color = (101, 67, 33)    # Dark brown

    # Pants body (two legs)
    left_leg = pygame.Rect(s_w*0.2, s_h*0.2, s_w*0.25, s_h*0.7)
    right_leg = pygame.Rect(s_w*0.55, s_h*0.2, s_w*0.25, s_h*0.7)
    pygame.draw.rect(sprite_surface, cloth_color, left_leg)
    pygame.draw.rect(sprite_surface, cloth_color, right_leg)

    # Waistband
    waist_rect = pygame.Rect(s_w*0.2, s_h*0.15, s_w*0.6, s_h*0.1)
    pygame.draw.rect(sprite_surface, dark_cloth_color, waist_rect)

    # Seam lines
    pygame.draw.line(sprite_surface, dark_cloth_color,
        (s_w*0.325, s_h*0.25), (s_w*0.325, s_h*0.85), 1)
    pygame.draw.line(sprite_surface, dark_cloth_color,
        (s_w*0.675, s_h*0.25), (s_w*0.675, s_h*0.85), 1)

    # Crotch seam
    pygame.draw.line(sprite_surface, dark_cloth_color,
        (s_w*0.45, s_h*0.4), (s_w*0.55, s_h*0.4), 1)

    return sprite_surface


def get_simple_shoes_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate simple shoes icon - dark brown shoes with laces or straps."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    shoe_color = (101, 67, 33)          # Dark brown
    dark_shoe_color = (70, 50, 30)      # Very dark brown
    lace_color = (139, 69, 19)          # Brown laces

    # Left shoe
    left_shoe = pygame.Rect(s_w*0.1, s_h*0.5, s_w*0.35, s_h*0.3)
    pygame.draw.ellipse(sprite_surface, shoe_color, left_shoe)

    # Right shoe
    right_shoe = pygame.Rect(s_w*0.55, s_h*0.5, s_w*0.35, s_h*0.3)
    pygame.draw.ellipse(sprite_surface, shoe_color, right_shoe)

    # Shoe soles
    left_sole = pygame.Rect(s_w*0.1, s_h*0.75, s_w*0.35, s_h*0.1)
    right_sole = pygame.Rect(s_w*0.55, s_h*0.75, s_w*0.35, s_h*0.1)
    pygame.draw.ellipse(sprite_surface, dark_shoe_color, left_sole)
    pygame.draw.ellipse(sprite_surface, dark_shoe_color, right_sole)

    # Laces (simple lines)
    for i in range(3):
        y_pos = s_h*0.55 + i * s_h*0.05
        pygame.draw.line(sprite_surface, lace_color,
            (s_w*0.15, y_pos), (s_w*0.4, y_pos), 1)
        pygame.draw.line(sprite_surface, lace_color,
            (s_w*0.6, y_pos), (s_w*0.85, y_pos), 1)

    return sprite_surface


def get_leather_cap_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate leather cap icon - leather headwear with stitched seams."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    leather_color = (139, 69, 19)       # Brown
    dark_leather_color = (101, 67, 33)  # Dark brown

    # Cap shape (more structured than cloth)
    cap_rect = pygame.Rect(s_w*0.2, s_h*0.15, s_w*0.6, s_h*0.5)
    pygame.draw.ellipse(sprite_surface, leather_color, cap_rect)

    # Leather panels (stitched together)
    # Center panel
    center_panel = pygame.Rect(s_w*0.4, s_h*0.2, s_w*0.2, s_h*0.3)
    pygame.draw.ellipse(sprite_surface, dark_leather_color, center_panel, 1)

    # Side panels
    left_panel = pygame.Rect(s_w*0.25, s_h*0.25, s_w*0.2, s_h*0.25)
    right_panel = pygame.Rect(s_w*0.55, s_h*0.25, s_w*0.2, s_h*0.25)
    pygame.draw.ellipse(sprite_surface, dark_leather_color, left_panel, 1)
    pygame.draw.ellipse(sprite_surface, dark_leather_color, right_panel, 1)

    # Stitching lines
    for i in range(4):
        x_pos = s_w*0.3 + i * s_w*0.1
        pygame.draw.circle(sprite_surface, dark_leather_color,
            (int(x_pos), int(s_h*0.35)), 1)

    # Brim
    brim_rect = pygame.Rect(s_w*0.15, s_h*0.6, s_w*0.7, s_h*0.1)
    pygame.draw.ellipse(sprite_surface, leather_color, brim_rect)
    pygame.draw.ellipse(sprite_surface, dark_leather_color, brim_rect, 1)

    return sprite_surface


def get_leather_armor_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate leather armor icon - leather chest piece with buckles and straps."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    leather_color = (139, 69, 19)       # Brown leather
    dark_leather_color = (101, 67, 33)  # Dark brown
    metal_color = (120, 120, 120)       # Gray metal

    # Armor body
    armor_rect = pygame.Rect(s_w*0.2, s_h*0.2, s_w*0.6, s_h*0.6)
    pygame.draw.rect(sprite_surface, leather_color, armor_rect)

    # Shoulder straps
    left_strap = pygame.Rect(s_w*0.15, s_h*0.15, s_w*0.1, s_h*0.3)
    right_strap = pygame.Rect(s_w*0.75, s_h*0.15, s_w*0.1, s_h*0.3)
    pygame.draw.rect(sprite_surface, dark_leather_color, left_strap)
    pygame.draw.rect(sprite_surface, dark_leather_color, right_strap)

    # Center strap with buckles
    center_strap = pygame.Rect(s_w*0.45, s_h*0.3, s_w*0.1, s_h*0.4)
    pygame.draw.rect(sprite_surface, dark_leather_color, center_strap)

    # Buckles
    for i in range(2):
        y_pos = s_h*0.35 + i * s_h*0.2
        buckle_rect = pygame.Rect(s_w*0.43, y_pos, s_w*0.14, s_h*0.06)
        pygame.draw.rect(sprite_surface, metal_color, buckle_rect)
        pygame.draw.rect(sprite_surface, dark_leather_color, buckle_rect, 1)

    # Stitching around edges
    pygame.draw.rect(sprite_surface, dark_leather_color, armor_rect, 2)

    # Decorative stitching
    for i in range(3):
        y_pos = s_h*0.3 + i * s_h*0.15
        pygame.draw.line(sprite_surface, dark_leather_color,
            (s_w*0.25, y_pos), (s_w*0.75, y_pos), 1)

    return sprite_surface


def get_leather_boots_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate leather boots icon - leather footwear with detailed stitching."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    leather_color = (101, 67, 33)       # Dark brown
    dark_leather_color = (70, 50, 30)   # Very dark brown

    # Left boot
    left_boot = pygame.Rect(s_w*0.05, s_h*0.4, s_w*0.4, s_h*0.45)
    pygame.draw.ellipse(sprite_surface, leather_color, left_boot)

    # Right boot
    right_boot = pygame.Rect(s_w*0.55, s_h*0.4, s_w*0.4, s_h*0.45)
    pygame.draw.ellipse(sprite_surface, leather_color, right_boot)

    # Boot shafts (higher than shoes)
    left_shaft = pygame.Rect(s_w*0.1, s_h*0.2, s_w*0.3, s_h*0.3)
    right_shaft = pygame.Rect(s_w*0.6, s_h*0.2, s_w*0.3, s_h*0.3)
    pygame.draw.rect(sprite_surface, leather_color, left_shaft)
    pygame.draw.rect(sprite_surface, leather_color, right_shaft)

    # Stitching on shafts
    pygame.draw.line(sprite_surface, dark_leather_color,
        (s_w*0.12, s_h*0.25), (s_w*0.12, s_h*0.45), 1)
    pygame.draw.line(sprite_surface, dark_leather_color,
        (s_w*0.62, s_h*0.25), (s_w*0.62, s_h*0.45), 1)

    # Boot soles
    left_sole = pygame.Rect(s_w*0.05, s_h*0.8, s_w*0.4, s_h*0.1)
    right_sole = pygame.Rect(s_w*0.55, s_h*0.8, s_w*0.4, s_h*0.1)
    pygame.draw.ellipse(sprite_surface, dark_leather_color, left_sole)
    pygame.draw.ellipse(sprite_surface, dark_leather_color, right_sole)

    # Lacing
    for i in range(3):
        y_pos = s_h*0.25 + i * s_h*0.08
        pygame.draw.line(sprite_surface, dark_leather_color,
            (s_w*0.15, y_pos), (s_w*0.35, y_pos), 1)
        pygame.draw.line(sprite_surface, dark_leather_color,
            (s_w*0.65, y_pos), (s_w*0.85, y_pos), 1)

    return sprite_surface


def get_leather_pants_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate leather pants icon - leather leg armor with stitched seams."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    leather_color = (139, 69, 19)       # Brown leather
    dark_leather_color = (101, 67, 33)  # Dark brown
    stitch_color = (160, 82, 45)        # Lighter brown stitching

    # Left leg
    left_leg_rect = pygame.Rect(s_w*0.2, s_h*0.25, s_w*0.25, s_h*0.7)
    pygame.draw.rect(sprite_surface, leather_color, left_leg_rect)
    pygame.draw.rect(sprite_surface, dark_leather_color, left_leg_rect, 1)

    # Right leg
    right_leg_rect = pygame.Rect(s_w*0.55, s_h*0.25, s_w*0.25, s_h*0.7)
    pygame.draw.rect(sprite_surface, leather_color, right_leg_rect)
    pygame.draw.rect(sprite_surface, dark_leather_color, right_leg_rect, 1)

    # Waistband
    waist_rect = pygame.Rect(s_w*0.2, s_h*0.25, s_w*0.6, s_h*0.08)
    pygame.draw.rect(sprite_surface, dark_leather_color, waist_rect)

    # Stitching details on legs
    for i in range(4):
        y_pos = s_h*0.35 + i * s_h*0.12
        # Left leg stitching
        pygame.draw.line(sprite_surface, stitch_color,
            (s_w*0.22, y_pos), (s_w*0.43, y_pos), 1)
        # Right leg stitching
        pygame.draw.line(sprite_surface, stitch_color,
            (s_w*0.57, y_pos), (s_w*0.78, y_pos), 1)

    # Knee reinforcement patches
    left_knee = pygame.Rect(s_w*0.24, s_h*0.55, s_w*0.17, s_h*0.12)
    right_knee = pygame.Rect(s_w*0.59, s_h*0.55, s_w*0.17, s_h*0.12)
    pygame.draw.rect(sprite_surface, dark_leather_color, left_knee)
    pygame.draw.rect(sprite_surface, dark_leather_color, right_knee)

    return sprite_surface


def get_iron_helmet_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate iron helmet icon - metallic helmet with visor and rivets."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    iron_color = (120, 120, 120)        # Iron gray
    dark_iron_color = (80, 80, 80)      # Dark gray

    # Helmet dome
    helmet_rect = pygame.Rect(s_w*0.2, s_h*0.1, s_w*0.6, s_h*0.5)
    pygame.draw.ellipse(sprite_surface, iron_color, helmet_rect)

    # Visor/face guard
    visor_rect = pygame.Rect(s_w*0.25, s_h*0.45, s_w*0.5, s_h*0.2)
    pygame.draw.rect(sprite_surface, dark_iron_color, visor_rect)

    # Eye slits
    pygame.draw.line(sprite_surface, (0, 0, 0),
        (s_w*0.3, s_h*0.5), (s_w*0.4, s_h*0.5), 1)
    pygame.draw.line(sprite_surface, (0, 0, 0),
        (s_w*0.6, s_h*0.5), (s_w*0.7, s_h*0.5), 1)

    # Neck guard
    neck_guard = pygame.Rect(s_w*0.3, s_h*0.6, s_w*0.4, s_h*0.15)
    pygame.draw.rect(sprite_surface, iron_color, neck_guard)

    # Rivets
    rivet_positions = [
        (s_w*0.3, s_h*0.25), (s_w*0.7, s_h*0.25),
        (s_w*0.25, s_h*0.4), (s_w*0.75, s_h*0.4)
    ]
    for pos in rivet_positions:
        pygame.draw.circle(sprite_surface, dark_iron_color,
            (int(pos[0]), int(pos[1])), 2)

    # Helmet crest/ridge
    pygame.draw.line(sprite_surface, dark_iron_color,
        (s_w*0.5, s_h*0.15), (s_w*0.5, s_h*0.4), 2)

    return sprite_surface


def get_iron_greaves_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate iron greaves icon - metallic leg armor with articulated plates."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    iron_color = (120, 120, 120)        # Iron gray
    dark_iron_color = (80, 80, 80)      # Dark gray

    # Left greave
    left_greave = pygame.Rect(s_w*0.15, s_h*0.2, s_w*0.25, s_h*0.6)
    pygame.draw.rect(sprite_surface, iron_color, left_greave)

    # Right greave
    right_greave = pygame.Rect(s_w*0.6, s_h*0.2, s_w*0.25, s_h*0.6)
    pygame.draw.rect(sprite_surface, iron_color, right_greave)

    # Articulated plates (horizontal segments)
    for i in range(4):
        y_pos = s_h*0.3 + i * s_h*0.12
        # Left greave plates
        pygame.draw.line(sprite_surface, dark_iron_color,
            (s_w*0.15, y_pos), (s_w*0.4, y_pos), 1)
        # Right greave plates
        pygame.draw.line(sprite_surface, dark_iron_color,
            (s_w*0.6, y_pos), (s_w*0.85, y_pos), 1)

    # Knee guards (wider sections)
    left_knee = pygame.Rect(s_w*0.1, s_h*0.15, s_w*0.35, s_h*0.15)
    right_knee = pygame.Rect(s_w*0.55, s_h*0.15, s_w*0.35, s_h*0.15)
    pygame.draw.ellipse(sprite_surface, iron_color, left_knee)
    pygame.draw.ellipse(sprite_surface, iron_color, right_knee)

    # Knee guard details
    pygame.draw.ellipse(sprite_surface, dark_iron_color, left_knee, 2)
    pygame.draw.ellipse(sprite_surface, dark_iron_color, right_knee, 2)

    # Straps/buckles
    for i in range(2):
        y_pos = s_h*0.4 + i * s_h*0.2
        # Left strap
        strap_rect = pygame.Rect(s_w*0.4, y_pos, s_w*0.05, s_h*0.05)
        pygame.draw.rect(sprite_surface, dark_iron_color, strap_rect)
        # Right strap
        strap_rect = pygame.Rect(s_w*0.55, y_pos, s_w*0.05, s_h*0.05)
        pygame.draw.rect(sprite_surface, dark_iron_color, strap_rect)

    return sprite_surface


def get_iron_breastplate_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate iron breastplate icon - metallic chest armor with plates and rivets."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    iron_color = (120, 120, 120)        # Iron gray
    dark_iron_color = (80, 80, 80)      # Dark gray
    shine_color = (180, 180, 180)       # Light gray shine

    # Main chest plate
    chest_rect = pygame.Rect(s_w*0.2, s_h*0.2, s_w*0.6, s_h*0.6)
    pygame.draw.rect(sprite_surface, iron_color, chest_rect)

    # Shoulder guards
    left_shoulder = pygame.Rect(s_w*0.05, s_h*0.15, s_w*0.25, s_h*0.3)
    right_shoulder = pygame.Rect(s_w*0.7, s_h*0.15, s_w*0.25, s_h*0.3)
    pygame.draw.ellipse(sprite_surface, iron_color, left_shoulder)
    pygame.draw.ellipse(sprite_surface, iron_color, right_shoulder)

    # Central ridge/spine
    pygame.draw.line(sprite_surface, dark_iron_color,
        (s_w*0.5, s_h*0.2), (s_w*0.5, s_h*0.8), 2)

    # Horizontal plate segments
    for i in range(3):
        y_pos = s_h*0.35 + i * s_h*0.15
        pygame.draw.line(sprite_surface, dark_iron_color,
            (s_w*0.25, y_pos), (s_w*0.75, y_pos), 1)

    # Rivets
    rivet_positions = [
        (s_w*0.3, s_h*0.3), (s_w*0.7, s_h*0.3),
        (s_w*0.3, s_h*0.5), (s_w*0.7, s_h*0.5),
        (s_w*0.3, s_h*0.7), (s_w*0.7, s_h*0.7)
    ]
    for pos in rivet_positions:
        pygame.draw.circle(sprite_surface, dark_iron_color,
            (int(pos[0]), int(pos[1])), 2)

    # Shine on shoulder guards
    pygame.draw.ellipse(sprite_surface, shine_color,
        (s_w*0.08, s_h*0.18, s_w*0.15, s_h*0.15))
    pygame.draw.ellipse(sprite_surface, shine_color,
        (s_w*0.77, s_h*0.18, s_w*0.15, s_h*0.15))

    return sprite_surface


def get_iron_boots_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate iron boots icon - metallic boots with articulated plates."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    iron_color = (120, 120, 120)        # Iron gray
    dark_iron_color = (80, 80, 80)      # Dark gray
    shine_color = (180, 180, 180)       # Light gray shine

    # Left boot
    left_boot_body = pygame.Rect(s_w*0.1, s_h*0.4, s_w*0.3, s_h*0.4)
    pygame.draw.rect(sprite_surface, iron_color, left_boot_body)

    # Left boot toe (rounded)
    left_toe = pygame.Rect(s_w*0.05, s_h*0.65, s_w*0.2, s_h*0.2)
    pygame.draw.ellipse(sprite_surface, iron_color, left_toe)

    # Right boot
    right_boot_body = pygame.Rect(s_w*0.6, s_h*0.4, s_w*0.3, s_h*0.4)
    pygame.draw.rect(sprite_surface, iron_color, right_boot_body)

    # Right boot toe (rounded)
    right_toe = pygame.Rect(s_w*0.75, s_h*0.65, s_w*0.2, s_h*0.2)
    pygame.draw.ellipse(sprite_surface, iron_color, right_toe)

    # Ankle guards
    left_ankle = pygame.Rect(s_w*0.08, s_h*0.3, s_w*0.34, s_h*0.2)
    right_ankle = pygame.Rect(s_w*0.58, s_h*0.3, s_w*0.34, s_h*0.2)
    pygame.draw.ellipse(sprite_surface, iron_color, left_ankle)
    pygame.draw.ellipse(sprite_surface, iron_color, right_ankle)

    # Plate segments on boots
    for i in range(2):
        y_pos = s_h*0.5 + i * s_h*0.15
        # Left boot plates
        pygame.draw.line(sprite_surface, dark_iron_color,
            (s_w*0.1, y_pos), (s_w*0.4, y_pos), 1)
        # Right boot plates
        pygame.draw.line(sprite_surface, dark_iron_color,
            (s_w*0.6, y_pos), (s_w*0.9, y_pos), 1)

    # Buckles/straps
    for i in range(2):
        y_pos = s_h*0.45 + i * s_h*0.15
        # Left boot buckle
        buckle_rect = pygame.Rect(s_w*0.35, y_pos, s_w*0.05, s_h*0.05)
        pygame.draw.rect(sprite_surface, dark_iron_color, buckle_rect)
        # Right boot buckle
        buckle_rect = pygame.Rect(s_w*0.6, y_pos, s_w*0.05, s_h*0.05)
        pygame.draw.rect(sprite_surface, dark_iron_color, buckle_rect)

    # Shine on toe caps
    pygame.draw.ellipse(sprite_surface, shine_color,
        (s_w*0.08, s_h*0.68, s_w*0.1, s_h*0.08))
    pygame.draw.ellipse(sprite_surface, shine_color,
        (s_w*0.82, s_h*0.68, s_w*0.1, s_h*0.08))

    return sprite_surface


def get_health_potion_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate health potion icon - glass bottle with red liquid and cork stopper."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    glass_color = (200, 220, 255, 180)  # Light blue glass (semi-transparent)
    liquid_color = (220, 20, 60)        # Crimson red
    cork_color = (139, 69, 19)          # Brown cork

    # Bottle body
    bottle_rect = pygame.Rect(s_w*0.3, s_h*0.3, s_w*0.4, s_h*0.5)
    pygame.draw.rect(sprite_surface, glass_color, bottle_rect)

    # Bottle neck
    neck_rect = pygame.Rect(s_w*0.4, s_h*0.2, s_w*0.2, s_h*0.15)
    pygame.draw.rect(sprite_surface, glass_color, neck_rect)

    # Liquid inside
    liquid_rect = pygame.Rect(s_w*0.32, s_h*0.4, s_w*0.36, s_h*0.35)
    pygame.draw.rect(sprite_surface, liquid_color, liquid_rect)

    # Cork stopper
    cork_rect = pygame.Rect(s_w*0.38, s_h*0.15, s_w*0.24, s_h*0.1)
    pygame.draw.rect(sprite_surface, cork_color, cork_rect)

    # Bottle highlights
    pygame.draw.line(sprite_surface, (255, 255, 255, 100),
        (s_w*0.32, s_h*0.32), (s_w*0.32, s_h*0.75), 2)

    # Label area
    label_rect = pygame.Rect(s_w*0.35, s_h*0.5, s_w*0.3, s_h*0.15)
    pygame.draw.rect(sprite_surface, (255, 255, 255, 150), label_rect)

    return sprite_surface


def get_mana_potion_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate mana potion icon - glass bottle with blue liquid and mystical glow."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    glass_color = (200, 220, 255, 180)  # Light blue glass (semi-transparent)
    liquid_color = (30, 144, 255)       # Dodger blue
    cork_color = (139, 69, 19)          # Brown cork
    glow_color = (173, 216, 230, 100)   # Light blue glow

    # Mystical glow around bottle
    glow_rect = pygame.Rect(s_w*0.25, s_h*0.25, s_w*0.5, s_h*0.6)
    pygame.draw.ellipse(sprite_surface, glow_color, glow_rect)

    # Bottle body
    bottle_rect = pygame.Rect(s_w*0.3, s_h*0.3, s_w*0.4, s_h*0.5)
    pygame.draw.rect(sprite_surface, glass_color, bottle_rect)

    # Bottle neck
    neck_rect = pygame.Rect(s_w*0.4, s_h*0.2, s_w*0.2, s_h*0.15)
    pygame.draw.rect(sprite_surface, glass_color, neck_rect)

    # Liquid inside
    liquid_rect = pygame.Rect(s_w*0.32, s_h*0.4, s_w*0.36, s_h*0.35)
    pygame.draw.rect(sprite_surface, liquid_color, liquid_rect)

    # Cork stopper
    cork_rect = pygame.Rect(s_w*0.38, s_h*0.15, s_w*0.24, s_h*0.1)
    pygame.draw.rect(sprite_surface, cork_color, cork_rect)

    # Magical sparkles
    sparkle_positions = [
        (s_w*0.4, s_h*0.45), (s_w*0.6, s_h*0.55), (s_w*0.5, s_h*0.65)
    ]
    for pos in sparkle_positions:
        pygame.draw.circle(sprite_surface, (255, 255, 255),
            (int(pos[0]), int(pos[1])), 1)

    # Bottle highlights
    pygame.draw.line(sprite_surface, (255, 255, 255, 100),
        (s_w*0.32, s_h*0.32), (s_w*0.32, s_h*0.75), 2)

    return sprite_surface


def get_bread_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate bread icon - loaf shape with golden-brown crust and scoring."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    crust_color = (210, 180, 140)       # Tan/golden brown
    dark_crust_color = (160, 130, 90)   # Darker brown

    # Bread loaf (oval shape)
    bread_rect = pygame.Rect(s_w*0.1, s_h*0.3, s_w*0.8, s_h*0.4)
    pygame.draw.ellipse(sprite_surface, crust_color, bread_rect)

    # Scoring marks on top (diagonal cuts)
    for i in range(3):
        x_start = s_w*0.2 + i * s_w*0.2
        x_end = x_start + s_w*0.15
        y_pos = s_h*0.4
        pygame.draw.line(sprite_surface, dark_crust_color,
            (x_start, y_pos), (x_end, y_pos), 2)

    # Crust texture (small dots)
    import random
    random.seed(42)  # Consistent pattern
    for _ in range(8):
        x = s_w*0.15 + random.random() * s_w*0.7
        y = s_h*0.35 + random.random() * s_h*0.3
        pygame.draw.circle(sprite_surface, dark_crust_color,
            (int(x), int(y)), 1)

    # Bottom shadow
    shadow_rect = pygame.Rect(s_w*0.15, s_h*0.65, s_w*0.7, s_h*0.1)
    pygame.draw.ellipse(sprite_surface, dark_crust_color, shadow_rect)

    return sprite_surface


def get_ale_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate ale icon - mug with frothy beer."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    mug_color = (139, 69, 19)           # Brown mug
    ale_color = (218, 165, 32)          # Golden ale
    foam_color = (255, 248, 220)        # Cream foam

    # Mug body (trapezoid shape)
    mug_points = [
        (s_w*0.2, s_h*0.8),             # Bottom left
        (s_w*0.8, s_h*0.8),             # Bottom right
        (s_w*0.7, s_h*0.3),             # Top right
        (s_w*0.3, s_h*0.3),             # Top left
    ]
    pygame.draw.polygon(sprite_surface, mug_color, mug_points)

    # Ale inside mug
    ale_points = [
        (s_w*0.25, s_h*0.75),           # Bottom left
        (s_w*0.75, s_h*0.75),           # Bottom right
        (s_w*0.65, s_h*0.4),            # Top right
        (s_w*0.35, s_h*0.4),            # Top left
    ]
    pygame.draw.polygon(sprite_surface, ale_color, ale_points)

    # Foam on top
    foam_rect = pygame.Rect(s_w*0.35, s_h*0.3, s_w*0.3, s_h*0.15)
    pygame.draw.ellipse(sprite_surface, foam_color, foam_rect)

    # Mug handle
    handle_rect = pygame.Rect(s_w*0.75, s_h*0.45, s_w*0.15, s_h*0.25)
    pygame.draw.ellipse(sprite_surface, mug_color, handle_rect, 3)

    return sprite_surface


def get_cheese_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate cheese icon - wedge of yellow cheese with holes."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    cheese_color = (255, 215, 0)        # Golden yellow
    dark_cheese_color = (218, 165, 32)  # Darker yellow

    # Cheese wedge (triangle)
    cheese_points = [
        (s_w*0.2, s_h*0.7),             # Bottom left
        (s_w*0.8, s_h*0.7),             # Bottom right
        (s_w*0.5, s_h*0.2),             # Top point
    ]
    pygame.draw.polygon(sprite_surface, cheese_color, cheese_points)

    # Cheese holes (circles)
    hole_positions = [
        (s_w*0.4, s_h*0.5),
        (s_w*0.6, s_h*0.55),
        (s_w*0.5, s_h*0.4),
    ]
    for pos in hole_positions:
        pygame.draw.circle(sprite_surface, dark_cheese_color, pos, int(s_w*0.04))

    # Cheese rind (darker edge)
    pygame.draw.polygon(sprite_surface, dark_cheese_color, cheese_points, 2)

    return sprite_surface


def get_hot_meal_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate hot meal icon - plate with food and steam."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    plate_color = (245, 245, 245)       # White plate
    food_color = (139, 69, 19)          # Brown food
    steam_color = (200, 200, 200, 128)  # Light gray steam

    # Plate (ellipse)
    plate_rect = pygame.Rect(s_w*0.1, s_h*0.5, s_w*0.8, s_h*0.4)
    pygame.draw.ellipse(sprite_surface, plate_color, plate_rect)

    # Food on plate (multiple small shapes)
    food_rects = [
        pygame.Rect(s_w*0.25, s_h*0.55, s_w*0.2, s_h*0.15),
        pygame.Rect(s_w*0.5, s_h*0.6, s_w*0.15, s_h*0.1),
        pygame.Rect(s_w*0.35, s_h*0.65, s_w*0.25, s_h*0.1),
    ]
    for rect in food_rects:
        pygame.draw.ellipse(sprite_surface, food_color, rect)

    # Steam lines (wavy lines going up)
    steam_surface = pygame.Surface(size, pygame.SRCALPHA)
    for i in range(3):
        x = s_w*0.3 + i * s_w*0.2
        for y in range(int(s_h*0.2), int(s_h*0.5), 4):
            offset = 2 if (y // 4) % 2 else -2
            pygame.draw.circle(steam_surface, steam_color, (int(x + offset), y), 1)

    sprite_surface.blit(steam_surface, (0, 0))

    return sprite_surface


def get_room_key_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate room key icon - ornate key with decorative head."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    key_color = (218, 165, 32)          # Golden brass
    dark_key_color = (184, 134, 11)     # Darker brass

    # Key shaft (vertical rectangle)
    shaft_rect = pygame.Rect(s_w*0.45, s_h*0.3, s_w*0.1, s_h*0.5)
    pygame.draw.rect(sprite_surface, key_color, shaft_rect)

    # Key head (circle)
    head_center = (int(s_w*0.5), int(s_h*0.25))
    head_radius = int(s_w*0.15)
    pygame.draw.circle(sprite_surface, key_color, head_center, head_radius)
    pygame.draw.circle(sprite_surface, dark_key_color, head_center, head_radius, 2)

    # Key teeth (small rectangles)
    teeth_rects = [
        pygame.Rect(s_w*0.55, s_h*0.65, s_w*0.15, s_h*0.05),
        pygame.Rect(s_w*0.55, s_h*0.75, s_w*0.1, s_h*0.05),
    ]
    for rect in teeth_rects:
        pygame.draw.rect(sprite_surface, key_color, rect)

    return sprite_surface


def get_gold_coin_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate gold coin icon - circular with golden color and shine."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    gold_color = (255, 215, 0)          # Gold
    dark_gold_color = (218, 165, 32)    # Dark goldenrod
    shine_color = (255, 255, 224)       # Light yellow

    # Coin body
    center = (s_w // 2, s_h // 2)
    radius = int(min(s_w, s_h) * 0.4)
    pygame.draw.circle(sprite_surface, gold_color, center, radius)

    # Coin rim
    pygame.draw.circle(sprite_surface, dark_gold_color, center, radius, 2)

    # Inner design (simple cross pattern)
    inner_radius = radius // 2
    pygame.draw.circle(sprite_surface, dark_gold_color, center, inner_radius, 1)

    # Cross pattern
    pygame.draw.line(sprite_surface, dark_gold_color,
        (center[0] - inner_radius//2, center[1]),
        (center[0] + inner_radius//2, center[1]), 2)
    pygame.draw.line(sprite_surface, dark_gold_color,
        (center[0], center[1] - inner_radius//2),
        (center[0], center[1] + inner_radius//2), 2)

    # Shine highlight
    shine_center = (center[0] - radius//3, center[1] - radius//3)
    pygame.draw.circle(sprite_surface, shine_color, shine_center, radius//4)

    return sprite_surface


def get_ruby_gem_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate ruby gem icon - red crystalline gem with faceted surface."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    ruby_color = (220, 20, 60)          # Crimson
    dark_ruby_color = (139, 0, 0)       # Dark red
    shine_color = (255, 182, 193)       # Light pink

    # Gem shape (diamond/octagon)
    center_x, center_y = s_w // 2, s_h // 2
    gem_points = [
        (center_x, center_y - s_h*0.3),        # Top
        (center_x + s_w*0.2, center_y - s_h*0.1),  # Top right
        (center_x + s_w*0.3, center_y + s_h*0.1),  # Bottom right
        (center_x, center_y + s_h*0.3),        # Bottom
        (center_x - s_w*0.3, center_y + s_h*0.1),  # Bottom left
        (center_x - s_w*0.2, center_y - s_h*0.1),  # Top left
    ]
    pygame.draw.polygon(sprite_surface, ruby_color, gem_points)

    # Facet lines
    pygame.draw.line(sprite_surface, dark_ruby_color,
        (center_x, center_y - s_h*0.3), (center_x, center_y + s_h*0.3), 1)
    pygame.draw.line(sprite_surface, dark_ruby_color,
        (center_x - s_w*0.2, center_y - s_h*0.1),
        (center_x + s_w*0.2, center_y + s_h*0.1), 1)
    pygame.draw.line(sprite_surface, dark_ruby_color,
        (center_x + s_w*0.2, center_y - s_h*0.1),
        (center_x - s_w*0.2, center_y + s_h*0.1), 1)

    # Gem outline
    pygame.draw.polygon(sprite_surface, dark_ruby_color, gem_points, 2)

    # Shine highlight
    shine_center = (center_x - s_w*0.1, center_y - s_h*0.1)
    pygame.draw.circle(sprite_surface, shine_color, shine_center, 3)

    return sprite_surface


def get_ancient_key_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate ancient key icon - ornate key with decorative head and aged metal."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    gold_color = (184, 134, 11)         # Dark gold
    dark_gold_color = (139, 69, 19)     # Bronze

    # Key head (ornate circular design)
    head_center = (int(s_w*0.3), int(s_h*0.3))
    head_radius = int(s_w*0.15)
    pygame.draw.circle(sprite_surface, gold_color, head_center, head_radius)

    # Inner circle design
    inner_radius = head_radius // 2
    pygame.draw.circle(sprite_surface, dark_gold_color, head_center, inner_radius, 1)

    # Decorative spokes
    for i in range(4):
        angle = i * 90  # 0, 90, 180, 270 degrees
        import math
        end_x = head_center[0] + math.cos(math.radians(angle)) * inner_radius
        end_y = head_center[1] + math.sin(math.radians(angle)) * inner_radius
        pygame.draw.line(sprite_surface, dark_gold_color,
            head_center, (int(end_x), int(end_y)), 1)

    # Key shaft
    shaft_rect = pygame.Rect(s_w*0.4, s_h*0.27, s_w*0.4, s_h*0.06)
    pygame.draw.rect(sprite_surface, gold_color, shaft_rect)

    # Key teeth
    teeth_points = [
        (s_w*0.8, s_h*0.27),   # Top of teeth
        (s_w*0.9, s_h*0.27),   # Tooth 1 top
        (s_w*0.9, s_h*0.29),   # Tooth 1 bottom
        (s_w*0.85, s_h*0.29),  # Gap
        (s_w*0.85, s_h*0.31),  # Tooth 2 top
        (s_w*0.9, s_h*0.31),   # Tooth 2 bottom
        (s_w*0.9, s_h*0.33),   # End
        (s_w*0.8, s_h*0.33),   # Back to shaft
    ]
    pygame.draw.polygon(sprite_surface, gold_color, teeth_points)

    # Key outline for definition
    pygame.draw.circle(sprite_surface, dark_gold_color, head_center, head_radius, 1)
    pygame.draw.rect(sprite_surface, dark_gold_color, shaft_rect, 1)
    pygame.draw.polygon(sprite_surface, dark_gold_color, teeth_points, 1)

    return sprite_surface


# ============================================================================
# ITEM ICON GENERATORS
# ============================================================================
# Each item has its own unique icon generation function for UI display

def get_rusty_sword_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate rusty sword icon - metallic gray with rusty brown stripes on blade with simple hilt."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    rust_color = (184, 115, 51)         # Rusty brown
    dark_rust_color = (139, 69, 19)     # Dark rust
    handle_color = (101, 67, 33)        # Brown handle

    # Blade (vertical sword)
    blade_points = [
        (s_w*0.5, s_h*0.1),             # Tip
        (s_w*0.4, s_h*0.65),            # Left edge
        (s_w*0.6, s_h*0.65),            # Right edge
    ]
    pygame.draw.polygon(sprite_surface, rust_color, blade_points)

    # Rust streaks on blade
    pygame.draw.line(sprite_surface, dark_rust_color,
        (s_w*0.45, s_h*0.2), (s_w*0.45, s_h*0.5), 2)
    pygame.draw.circle(sprite_surface, dark_rust_color,
        (int(s_w*0.55), int(s_h*0.35)), 2)

    # Cross guard
    guard_rect = pygame.Rect(s_w*0.3, s_h*0.62, s_w*0.4, s_h*0.06)
    pygame.draw.rect(sprite_surface, rust_color, guard_rect)

    # Handle
    handle_rect = pygame.Rect(s_w*0.45, s_h*0.68, s_w*0.1, s_h*0.25)
    pygame.draw.rect(sprite_surface, handle_color, handle_rect)

    # Pommel
    pommel_center = (int(s_w*0.5), int(s_h*0.9))
    pygame.draw.circle(sprite_surface, rust_color, pommel_center, 3)

    return sprite_surface


def get_iron_sword_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate iron sword icon - metallic gray blade with detailed hilt."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    iron_color = (192, 192, 192)        # Silver/iron
    dark_iron_color = (120, 120, 120)   # Dark gray
    handle_color = (101, 67, 33)        # Brown handle

    # Blade (vertical sword)
    blade_points = [
        (s_w*0.5, s_h*0.1),             # Tip
        (s_w*0.4, s_h*0.65),            # Left edge
        (s_w*0.6, s_h*0.65),            # Right edge
    ]
    pygame.draw.polygon(sprite_surface, iron_color, blade_points)

    # Fuller (blood groove) down center of blade
    pygame.draw.line(sprite_surface, dark_iron_color,
        (s_w*0.5, s_h*0.15), (s_w*0.5, s_h*0.6), 1)

    # Cross guard
    guard_rect = pygame.Rect(s_w*0.25, s_h*0.62, s_w*0.5, s_h*0.06)
    pygame.draw.rect(sprite_surface, iron_color, guard_rect)

    # Handle with grip wrapping
    handle_rect = pygame.Rect(s_w*0.45, s_h*0.68, s_w*0.1, s_h*0.25)
    pygame.draw.rect(sprite_surface, handle_color, handle_rect)

    # Grip wrapping lines
    for y in range(3):
        y_pos = int(s_h*0.72 + y * s_h*0.06)
        pygame.draw.line(sprite_surface, dark_iron_color,
            (s_w*0.45, y_pos), (s_w*0.55, y_pos), 1)

    # Pommel
    pommel_center = (int(s_w*0.5), int(s_h*0.9))
    pygame.draw.circle(sprite_surface, iron_color, pommel_center, 4)
    pygame.draw.circle(sprite_surface, dark_iron_color, pommel_center, 2)

    return sprite_surface


def get_wooden_bow_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate wooden bow icon - curved brown bow with visible string."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    wood_color = (160, 82, 45)          # Saddle brown
    dark_wood_color = (139, 69, 19)     # Dark brown
    string_color = (245, 245, 220)      # Beige string

    # Bow stave (curved)
    # Draw as series of connected lines to create curve
    bow_points = []
    for i in range(11):  # 11 points for smooth curve
        t = i / 10.0  # 0 to 1
        # Quadratic curve
        x = s_w*0.3 + t * s_w*0.4 * (1 - abs(t - 0.5) * 2)  # Curves inward at center
        y = s_h*0.1 + t * s_h*0.8
        bow_points.append((x, y))

    # Draw bow stave
    for i in range(len(bow_points) - 1):
        pygame.draw.line(sprite_surface, wood_color, bow_points[i], bow_points[i+1], 3)

    # Bow tips (nocks)
    pygame.draw.circle(sprite_surface, dark_wood_color,
        (int(bow_points[0][0]), int(bow_points[0][1])), 2)
    pygame.draw.circle(sprite_surface, dark_wood_color,
        (int(bow_points[-1][0]), int(bow_points[-1][1])), 2)

    # Bow string
    pygame.draw.line(sprite_surface, string_color,
        bow_points[0], bow_points[-1], 1)

    # Grip area in center
    center_idx = len(bow_points) // 2
    grip_center = bow_points[center_idx]
    grip_rect = pygame.Rect(grip_center[0]-2, grip_center[1]-8, 4, 16)
    pygame.draw.rect(sprite_surface, dark_wood_color, grip_rect)

    return sprite_surface


def get_steel_dagger_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate steel dagger icon - sharp steel blade with leather handle."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    steel_color = (180, 180, 180)       # Steel grey
    dark_steel_color = (120, 120, 120)  # Dark steel
    handle_color = (139, 69, 19)        # Brown leather handle

    # Blade (diamond/dagger shape)
    blade_points = [
        (s_w*0.5, s_h*0.1),             # Tip
        (s_w*0.4, s_h*0.6),             # Left edge
        (s_w*0.6, s_h*0.6),             # Right edge
    ]
    pygame.draw.polygon(sprite_surface, steel_color, blade_points)

    # Fuller (blood groove) down center
    pygame.draw.line(sprite_surface, dark_steel_color,
        (s_w*0.5, s_h*0.15), (s_w*0.5, s_h*0.55), 1)

    # Cross guard (small)
    guard_rect = pygame.Rect(s_w*0.35, s_h*0.58, s_w*0.3, s_h*0.04)
    pygame.draw.rect(sprite_surface, steel_color, guard_rect)

    # Handle (wrapped leather)
    handle_rect = pygame.Rect(s_w*0.45, s_h*0.62, s_w*0.1, s_h*0.25)
    pygame.draw.rect(sprite_surface, handle_color, handle_rect)

    # Handle wrapping lines
    for i in range(3):
        y = s_h*0.65 + i * s_h*0.06
        pygame.draw.line(sprite_surface, dark_steel_color,
            (s_w*0.45, y), (s_w*0.55, y), 1)

    # Pommel
    pommel_center = (int(s_w*0.5), int(s_h*0.9))
    pygame.draw.circle(sprite_surface, steel_color, pommel_center, 3)

    return sprite_surface


def get_battle_axe_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate battle axe icon - double crescent axe with iron heads and wooden handle."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    iron_color = (120, 120, 120)        # Iron axe head
    dark_iron_color = (80, 80, 80)      # Dark iron
    wood_color = (139, 69, 19)          # Wood handle
    dark_wood_color = (101, 67, 33)     # Dark wood for grain

    # Handle (vertical shaft) - positioned similarly to player sprite
    handle_rect = pygame.Rect(s_w*0.45, s_h*0.45, s_w*0.1, s_h*0.45)
    pygame.draw.rect(sprite_surface, wood_color, handle_rect)

    # Wood grain lines - positioned on the handle
    for i in range(6):
        y = s_h*0.48 + i * s_h*0.06
        pygame.draw.line(sprite_surface, dark_wood_color,
            (s_w*0.46, y), (s_w*0.54, y), 1)

    # Create a complete axe head shape - rotated 90 degrees (vertical orientation)
    # Axe head positioned from 10% to 45% height to match player sprite proportions
    axe_head_points = [
        # Left crescent (top side when rotated)
        (s_w*0.35, s_h*0.1),           # Left top tip
        (s_w*0.38, s_h*0.15),          # Left top curve
        (s_w*0.42, s_h*0.25),          # Left top base
        (s_w*0.45, s_h*0.35),          # Connection to handle (left top)
        
        # Connect to right on top side
        (s_w*0.55, s_h*0.35),          # Connection to handle (right top)
        (s_w*0.58, s_h*0.25),          # Right top base
        (s_w*0.62, s_h*0.15),          # Right top curve
        (s_w*0.65, s_h*0.1),           # Right top tip
        
        # Right crescent (bottom side when rotated)
        (s_w*0.65, s_h*0.45),          # Right bottom tip
        (s_w*0.62, s_h*0.40),          # Right bottom curve
        (s_w*0.58, s_h*0.37),          # Right bottom base
        (s_w*0.55, s_h*0.35),          # Connection to handle (right bottom)
        
        # Connect to left on bottom side
        (s_w*0.45, s_h*0.35),          # Connection to handle (left bottom)
        (s_w*0.42, s_h*0.37),          # Left bottom base
        (s_w*0.38, s_h*0.40),          # Left bottom curve
        (s_w*0.35, s_h*0.45),          # Left bottom tip
    ]
    pygame.draw.polygon(sprite_surface, iron_color, axe_head_points)

    # Top crescent edge highlights
    pygame.draw.line(sprite_surface, dark_iron_color,
        (s_w*0.35, s_h*0.08), (s_w*0.40, s_h*0.15), 2)
    pygame.draw.line(sprite_surface, dark_iron_color,
        (s_w*0.40, s_h*0.15), (s_w*0.60, s_h*0.15), 1)
    pygame.draw.line(sprite_surface, dark_iron_color,
        (s_w*0.60, s_h*0.15), (s_w*0.65, s_h*0.08), 2)

    # Bottom crescent edge highlights
    pygame.draw.line(sprite_surface, dark_iron_color,
        (s_w*0.35, s_h*0.47), (s_w*0.40, s_h*0.40), 2)
    pygame.draw.line(sprite_surface, dark_iron_color,
        (s_w*0.40, s_h*0.40), (s_w*0.60, s_h*0.40), 1)
    pygame.draw.line(sprite_surface, dark_iron_color,
        (s_w*0.60, s_h*0.40), (s_w*0.65, s_h*0.47), 2)

    # Central socket/binding where blades meet handle
    socket_rect = pygame.Rect(s_w*0.42, s_h*0.32, s_w*0.16, s_h*0.06)
    pygame.draw.rect(sprite_surface, dark_iron_color, socket_rect)

    return sprite_surface


def get_meat_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate raw meat icon - red meat with marbling and texture."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    meat_color = (139, 69, 19)          # Dark red meat
    dark_meat_color = (101, 67, 33)     # Darker meat
    fat_color = (255, 228, 196)         # Fat marbling
    blood_color = (139, 0, 0)           # Dark red blood

    # Main meat chunk (irregular shape)
    meat_points = [
        (s_w*0.2, s_h*0.3),
        (s_w*0.8, s_h*0.25),
        (s_w*0.85, s_h*0.6),
        (s_w*0.7, s_h*0.8),
        (s_w*0.3, s_h*0.75),
        (s_w*0.15, s_h*0.5),
    ]
    pygame.draw.polygon(sprite_surface, meat_color, meat_points)

    # Fat marbling streaks
    pygame.draw.line(sprite_surface, fat_color,
        (s_w*0.3, s_h*0.4), (s_w*0.6, s_h*0.45), 2)
    pygame.draw.line(sprite_surface, fat_color,
        (s_w*0.4, s_h*0.6), (s_w*0.7, s_h*0.65), 2)

    # Blood spots
    pygame.draw.circle(sprite_surface, blood_color,
        (int(s_w*0.35), int(s_h*0.5)), 2)
    pygame.draw.circle(sprite_surface, blood_color,
        (int(s_w*0.6), int(s_h*0.6)), 1)

    # Meat texture (darker areas)
    pygame.draw.circle(sprite_surface, dark_meat_color,
        (int(s_w*0.5), int(s_h*0.4)), 3)
    pygame.draw.circle(sprite_surface, dark_meat_color,
        (int(s_w*0.4), int(s_h*0.65)), 2)

    # Outline for definition
    pygame.draw.polygon(sprite_surface, dark_meat_color, meat_points, 1)

    return sprite_surface


def get_leather_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate leather icon - brown hide with texture and natural edges."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    leather_color = (160, 82, 45)       # Saddle brown
    dark_leather_color = (101, 67, 33)  # Dark brown
    light_leather_color = (210, 180, 140) # Tan

    # Main leather hide (irregular animal hide shape)
    hide_points = [
        (s_w*0.15, s_h*0.2),
        (s_w*0.4, s_h*0.1),
        (s_w*0.7, s_h*0.15),
        (s_w*0.85, s_h*0.35),
        (s_w*0.8, s_h*0.6),
        (s_w*0.9, s_h*0.8),
        (s_w*0.6, s_h*0.85),
        (s_w*0.3, s_h*0.8),
        (s_w*0.1, s_h*0.6),
        (s_w*0.05, s_h*0.4),
    ]
    pygame.draw.polygon(sprite_surface, leather_color, hide_points)

    # Leather texture lines (natural grain)
    pygame.draw.line(sprite_surface, dark_leather_color,
        (s_w*0.2, s_h*0.3), (s_w*0.6, s_h*0.35), 1)
    pygame.draw.line(sprite_surface, dark_leather_color,
        (s_w*0.3, s_h*0.5), (s_w*0.7, s_h*0.55), 1)
    pygame.draw.line(sprite_surface, dark_leather_color,
        (s_w*0.25, s_h*0.7), (s_w*0.5, s_h*0.72), 1)

    # Light patches (natural variation)
    pygame.draw.circle(sprite_surface, light_leather_color,
        (int(s_w*0.4), int(s_h*0.4)), 4)
    pygame.draw.circle(sprite_surface, light_leather_color,
        (int(s_w*0.6), int(s_h*0.6)), 3)

    # Dark spots (natural markings)
    pygame.draw.circle(sprite_surface, dark_leather_color,
        (int(s_w*0.3), int(s_h*0.6)), 2)
    pygame.draw.circle(sprite_surface, dark_leather_color,
        (int(s_w*0.7), int(s_h*0.4)), 1)

    # Hide outline
    pygame.draw.polygon(sprite_surface, dark_leather_color, hide_points, 1)

    return sprite_surface


def get_spider_sac_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate spider sac icon - silk-filled sac with web-like texture."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    sac_color = (245, 245, 220)         # Beige/off-white silk
    dark_sac_color = (220, 220, 190)    # Darker beige
    web_color = (200, 200, 170)         # Silk web color
    shadow_color = (180, 180, 150)      # Shadow

    # Main sac body (oval shape)
    sac_rect = pygame.Rect(s_w*0.2, s_h*0.25, s_w*0.6, s_h*0.5)
    pygame.draw.ellipse(sprite_surface, sac_color, sac_rect)
    pygame.draw.ellipse(sprite_surface, dark_sac_color, sac_rect, 2)

    # Sac opening/tie at top
    opening_rect = pygame.Rect(s_w*0.35, s_h*0.15, s_w*0.3, s_h*0.15)
    pygame.draw.ellipse(sprite_surface, dark_sac_color, opening_rect)

    # Silk web pattern inside sac
    center_x, center_y = s_w*0.5, s_h*0.5

    # Radial web lines
    for i in range(6):
        angle = i * 60  # 60 degrees apart
        import math
        end_x = center_x + math.cos(math.radians(angle)) * s_w*0.15
        end_y = center_y + math.sin(math.radians(angle)) * s_h*0.12
        pygame.draw.line(sprite_surface, web_color,
            (center_x, center_y), (end_x, end_y), 1)

    # Concentric web circles
    for radius in [s_w*0.08, s_w*0.12]:
        pygame.draw.circle(sprite_surface, web_color,
            (int(center_x), int(center_y)), int(radius), 1)

    # Small silk strands hanging from opening
    for i in range(3):
        x_pos = s_w*0.4 + i * s_w*0.1
        pygame.draw.line(sprite_surface, web_color,
            (x_pos, s_h*0.25), (x_pos, s_h*0.35), 1)

    # Subtle shadow/depth
    shadow_rect = pygame.Rect(s_w*0.22, s_h*0.27, s_w*0.56, s_h*0.46)
    pygame.draw.ellipse(sprite_surface, shadow_color, shadow_rect, 1)

    return sprite_surface


def get_leather_pants_sprite(size: Tuple[int, int] = (24, 24)) -> pygame.Surface:
    """Generate leather pants sprite for world/inventory display."""
    return get_leather_pants_icon(size)


def get_spider_sac_sprite(size: Tuple[int, int] = (16, 16)) -> pygame.Surface:
    """Generate spider sac sprite for world/inventory display."""
    return get_spider_sac_icon(size)
