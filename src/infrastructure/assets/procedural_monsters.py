"""
Procedural Monster Asset Generation

This module contains functions to procedurally generate monster sprites using Pygame.
This is the ONLY place in the codebase where monster visual assets are created.
"""

import pygame
from typing import <PERSON><PERSON>


def get_green_drake_sprite(size: Tuple[int, int] = (64, 64)) -> pygame.Surface:
    """
    Generate a Green Drake sprite procedurally.
    
    This is the detailed implementation from the architectural blueprint.
    Creates a side-view dragon with wings, claws, and proper proportions.
    """
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size
    
    # Color palette
    body_color = (34, 139, 34)      # Forest Green
    wing_color_far = (0, 80, 0)     # Darker Green for far wing
    wing_color_near = (0, 100, 0)   # Slightly lighter for near wing
    underbelly_color = (152, 251, 152)  # Pale Green
    eye_color = (255, 255, 0)       # Yellow
    claw_color = (60, 60, 60)       # Dark Gray

    # Body (main ellipse, side view)
    body_rect = pygame.Rect(s_w*0.2, s_h*0.35, s_w*0.6, s_h*0.35)
    pygame.draw.ellipse(sprite_surface, body_color, body_rect)
    pygame.draw.ellipse(sprite_surface, underbelly_color, 
        (body_rect.left + s_w*0.05, body_rect.centery + s_h*0.05, 
         body_rect.width*0.9, body_rect.height*0.4))

    # Tail (extending from left of body)
    tail_points = [
        (body_rect.left + s_w*0.05, body_rect.centery),
        (body_rect.left - s_w*0.2, body_rect.centery - s_h*0.05),
        (body_rect.left - s_w*0.3, body_rect.centery + s_h*0.05),
        (body_rect.left - s_w*0.2, body_rect.centery + s_h*0.15),
    ]
    pygame.draw.polygon(sprite_surface, body_color, tail_points)

    # Legs (two visible on the near side)
    leg_y_start = body_rect.bottom - s_h*0.05
    # Near front leg
    pygame.draw.rect(sprite_surface, body_color, 
        (body_rect.centerx + s_w*0.1, leg_y_start, s_w*0.08, s_h*0.2))
    pygame.draw.polygon(sprite_surface, claw_color, [
        (body_rect.centerx + s_w*0.1, leg_y_start + s_h*0.2), 
        (body_rect.centerx + s_w*0.18, leg_y_start + s_h*0.2), 
        (body_rect.centerx + s_w*0.14, leg_y_start + s_h*0.25)
    ])
    # Near back leg
    pygame.draw.rect(sprite_surface, body_color, 
        (body_rect.centerx - s_w*0.15, leg_y_start, s_w*0.08, s_h*0.2))
    pygame.draw.polygon(sprite_surface, claw_color, [
        (body_rect.centerx - s_w*0.15, leg_y_start + s_h*0.2), 
        (body_rect.centerx - s_w*0.07, leg_y_start + s_h*0.2), 
        (body_rect.centerx - s_w*0.11, leg_y_start + s_h*0.25)
    ])

    # Far Wing (drawn first, partially obscured)
    fw_p1 = (body_rect.centerx - s_w*0.05, body_rect.top + s_h*0.05)  # Shoulder
    fw_p2 = (fw_p1[0] - s_w*0.2, fw_p1[1] - s_h*0.25)  # Elbow/Mid
    fw_p3 = (fw_p2[0] + s_w*0.1, fw_p2[1] - s_h*0.15)  # Tip
    fw_p4 = (fw_p1[0] + s_w*0.15, body_rect.centery - s_h*0.05)  # Lower connection
    pygame.draw.polygon(sprite_surface, wing_color_far, [fw_p1, fw_p2, fw_p3, fw_p4])

    # Head and Neck (extending from right of body)
    neck_base_x, neck_base_y = body_rect.right - s_w*0.05, body_rect.centery - s_h*0.05
    neck_mid_x, neck_mid_y = neck_base_x + s_w*0.1, neck_base_y - s_h*0.1
    head_x, head_y = neck_mid_x + s_w*0.1, neck_mid_y - s_h*0.05
    
    pygame.draw.line(sprite_surface, body_color, (neck_base_x, neck_base_y), 
                    (neck_mid_x, neck_mid_y), int(s_w*0.1))  # Neck
    pygame.draw.circle(sprite_surface, body_color, (int(head_x), int(head_y)), 
                      int(s_w*0.12))  # Head main
    pygame.draw.polygon(sprite_surface, body_color, [
        (head_x, head_y - s_h*0.05), 
        (head_x + s_w*0.15, head_y), 
        (head_x, head_y + s_h*0.05)
    ])  # Snout
    pygame.draw.circle(sprite_surface, eye_color, 
        (int(head_x + s_w*0.03), int(head_y - s_h*0.02)), int(s_w*0.03))  # Eye

    # Near Wing (drawn last, on top)
    nw_p1 = (body_rect.centerx + s_w*0.05, body_rect.top)  # Shoulder
    nw_p2 = (nw_p1[0] - s_w*0.15, nw_p1[1] - s_h*0.3)   # Elbow/Mid (higher)
    nw_p3 = (nw_p2[0] + s_w*0.2, nw_p2[1] - s_h*0.1)    # Tip (sweeping back)
    nw_p4 = (nw_p1[0] + s_w*0.2, body_rect.centery)     # Lower connection
    pygame.draw.polygon(sprite_surface, wing_color_near, [nw_p1, nw_p2, nw_p3, nw_p4])
    
    return sprite_surface


def get_goblin_grunt_sprite(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a detailed Goblin Grunt sprite procedurally."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size
    
    # Enhanced color palette
    skin_color = (80, 130, 80)      # Vibrant green skin
    dark_skin = (60, 100, 60)       # Darker green for shadows
    armor_color = (101, 67, 33)     # Brown leather armor
    armor_metal = (105, 105, 105)   # Metal studs and buckles
    weapon_handle = (139, 69, 19)   # Brown weapon handle
    blade_color = (192, 192, 192)   # Silver blade
    blade_edge = (220, 220, 220)    # Brighter blade edge
    eye_color = (255, 50, 50)       # Glowing red eyes
    teeth_color = (255, 255, 200)   # Yellowed teeth
    belt_color = (80, 50, 30)       # Dark brown belt
    
    # Main body (more muscular, battle-ready stance)
    body_rect = pygame.Rect(s_w*0.22, s_h*0.38, s_w*0.45, s_h*0.4)
    pygame.draw.ellipse(sprite_surface, skin_color, body_rect)
    
    # Add muscle definition with darker skin tone
    muscle_rect = pygame.Rect(body_rect.left + s_w*0.02, body_rect.top + s_h*0.02, 
                             body_rect.width - s_w*0.04, body_rect.height*0.6)
    pygame.draw.ellipse(sprite_surface, dark_skin, muscle_rect)
    
    # Leather armor vest with metal studs
    armor_rect = pygame.Rect(s_w*0.25, s_h*0.42, s_w*0.38, s_h*0.3)
    pygame.draw.rect(sprite_surface, armor_color, armor_rect)
    
    # Metal studs on armor
    stud_positions = [
        (armor_rect.left + s_w*0.05, armor_rect.top + s_h*0.08),
        (armor_rect.right - s_w*0.08, armor_rect.top + s_h*0.08),
        (armor_rect.centerx, armor_rect.centery)
    ]
    for stud_x, stud_y in stud_positions:
        pygame.draw.circle(sprite_surface, armor_metal, (int(stud_x), int(stud_y)), 2)
    
    # Belt with buckle
    belt_rect = pygame.Rect(s_w*0.24, s_h*0.62, s_w*0.4, s_h*0.06)
    pygame.draw.rect(sprite_surface, belt_color, belt_rect)
    buckle_rect = pygame.Rect(belt_rect.centerx - s_w*0.03, belt_rect.centery - s_h*0.02, s_w*0.06, s_h*0.04)
    pygame.draw.rect(sprite_surface, armor_metal, buckle_rect)
    
    # Head (larger, more detailed)
    head_x, head_y = body_rect.centerx, body_rect.top - s_h*0.12
    pygame.draw.circle(sprite_surface, skin_color, (int(head_x), int(head_y)), int(s_w*0.16))
    
    # Head shadow/definition
    pygame.draw.circle(sprite_surface, dark_skin, (int(head_x + s_w*0.02), int(head_y + s_h*0.02)), int(s_w*0.14))
    
    # Large pointed ears with inner detail
    ear_outer = [
        (head_x - s_w*0.12, head_y - s_h*0.04),
        (head_x - s_w*0.22, head_y - s_h*0.16),
        (head_x - s_w*0.06, head_y - s_h*0.08)
    ]
    ear_inner = [
        (head_x - s_w*0.1, head_y - s_h*0.05),
        (head_x - s_w*0.18, head_y - s_h*0.13),
        (head_x - s_w*0.08, head_y - s_h*0.07)
    ]
    pygame.draw.polygon(sprite_surface, skin_color, ear_outer)
    pygame.draw.polygon(sprite_surface, dark_skin, ear_inner)
    
    # Menacing eyes with glow effect
    eye_main = (int(head_x + s_w*0.04), int(head_y - s_h*0.03))
    pygame.draw.circle(sprite_surface, (100, 0, 0), eye_main, 4)  # Dark red socket
    pygame.draw.circle(sprite_surface, eye_color, eye_main, 3)     # Glowing red
    pygame.draw.circle(sprite_surface, (255, 100, 100), eye_main, 1)  # Bright center
    
    # Snarling mouth with visible teeth
    mouth_rect = pygame.Rect(head_x - s_w*0.06, head_y + s_h*0.06, s_w*0.12, s_h*0.04)
    pygame.draw.ellipse(sprite_surface, (20, 20, 20), mouth_rect)  # Dark mouth
    
    # Sharp teeth
    tooth_positions = [
        (head_x - s_w*0.04, head_y + s_h*0.06),
        (head_x - s_w*0.01, head_y + s_h*0.06),
        (head_x + s_w*0.02, head_y + s_h*0.06)
    ]
    for tooth_x, tooth_y in tooth_positions:
        pygame.draw.polygon(sprite_surface, teeth_color, [
            (tooth_x, tooth_y), (tooth_x + s_w*0.01, tooth_y + s_h*0.03), (tooth_x - s_w*0.01, tooth_y + s_h*0.03)
        ])
    
    # Muscular arms
    # Left arm (shield arm)
    arm_left_start = (body_rect.left - s_w*0.02, body_rect.top + s_h*0.08)
    arm_left_end = (arm_left_start[0] - s_w*0.12, arm_left_start[1] + s_h*0.15)
    pygame.draw.line(sprite_surface, skin_color, arm_left_start, arm_left_end, int(s_w*0.08))
    
    # Small wooden shield
    shield_center = (int(arm_left_end[0] - s_w*0.05), int(arm_left_end[1]))
    pygame.draw.circle(sprite_surface, armor_color, shield_center, int(s_w*0.08))
    pygame.draw.circle(sprite_surface, armor_metal, shield_center, int(s_w*0.04))
    pygame.draw.circle(sprite_surface, armor_metal, shield_center, 2)
    
    # Right arm (weapon arm) - more muscular
    arm_right_start = (body_rect.right + s_w*0.02, body_rect.top + s_h*0.05)
    arm_right_mid = (arm_right_start[0] + s_w*0.08, arm_right_start[1] + s_h*0.08)
    arm_right_end = (arm_right_mid[0] + s_w*0.06, arm_right_mid[1] - s_h*0.02)
    
    pygame.draw.line(sprite_surface, skin_color, arm_right_start, arm_right_mid, int(s_w*0.09))
    pygame.draw.line(sprite_surface, skin_color, arm_right_mid, arm_right_end, int(s_w*0.07))
    
    # Detailed weapon - jagged scimitar
    weapon_handle_start = arm_right_end
    weapon_handle_end = (weapon_handle_start[0] + s_w*0.15, weapon_handle_start[1] - s_h*0.05)
    
    # Handle with wrapping
    pygame.draw.line(sprite_surface, weapon_handle, weapon_handle_start, weapon_handle_end, 4)
    # Handle wrapping details
    for i in range(3):
        wrap_y = weapon_handle_start[1] + (weapon_handle_end[1] - weapon_handle_start[1]) * (i / 3)
        pygame.draw.circle(sprite_surface, (80, 40, 20), (int(weapon_handle_start[0] + i * s_w*0.05), int(wrap_y)), 1)
    
    # Curved blade with serrated edge
    blade_start = weapon_handle_end
    blade_curve = (blade_start[0] + s_w*0.12, blade_start[1] - s_h*0.08)
    blade_tip = (blade_curve[0] + s_w*0.08, blade_curve[1] + s_h*0.02)
    
    # Main blade
    blade_points = [blade_start, blade_curve, blade_tip, 
                   (blade_start[0] + s_w*0.02, blade_start[1] + s_h*0.02)]
    pygame.draw.polygon(sprite_surface, blade_color, blade_points)
    
    # Blade edge highlight
    pygame.draw.line(sprite_surface, blade_edge, blade_start, blade_curve, 2)
    pygame.draw.line(sprite_surface, blade_edge, blade_curve, blade_tip, 2)
    
    # Serrated edge details
    for i in range(4):
        notch_x = blade_start[0] + (blade_curve[0] - blade_start[0]) * (i / 4)
        notch_y = blade_start[1] + (blade_curve[1] - blade_start[1]) * (i / 4)
        pygame.draw.circle(sprite_surface, (150, 150, 150), (int(notch_x), int(notch_y)), 1)
    
    # Crossguard
    guard_center = weapon_handle_end
    pygame.draw.line(sprite_surface, armor_metal, 
                    (guard_center[0] - s_w*0.04, guard_center[1] - s_h*0.02),
                    (guard_center[0] + s_w*0.04, guard_center[1] + s_h*0.02), 3)
    
    # Sturdy legs with knee protection
    leg_width = int(s_w*0.08)
    
    # Left leg
    left_leg_x = body_rect.centerx - s_w*0.06
    leg_top = body_rect.bottom
    leg_mid = leg_top + s_h*0.08
    leg_bottom = leg_top + s_h*0.18
    
    pygame.draw.line(sprite_surface, skin_color, (left_leg_x, leg_top), (left_leg_x, leg_mid), leg_width)
    pygame.draw.line(sprite_surface, skin_color, (left_leg_x, leg_mid), (left_leg_x - s_w*0.02, leg_bottom), leg_width-1)
    
    # Right leg  
    right_leg_x = body_rect.centerx + s_w*0.06
    pygame.draw.line(sprite_surface, skin_color, (right_leg_x, leg_top), (right_leg_x, leg_mid), leg_width)
    pygame.draw.line(sprite_surface, skin_color, (right_leg_x, leg_mid), (right_leg_x + s_w*0.02, leg_bottom), leg_width-1)
    
    # Knee guards
    pygame.draw.ellipse(sprite_surface, armor_color, (left_leg_x - s_w*0.03, leg_mid - s_h*0.02, s_w*0.06, s_h*0.04))
    pygame.draw.ellipse(sprite_surface, armor_color, (right_leg_x - s_w*0.03, leg_mid - s_h*0.02, s_w*0.06, s_h*0.04))
    
    # Heavy boots
    boot_color = (60, 40, 20)
    pygame.draw.ellipse(sprite_surface, boot_color, (left_leg_x - s_w*0.04, leg_bottom - s_h*0.03, s_w*0.08, s_h*0.06))
    pygame.draw.ellipse(sprite_surface, boot_color, (right_leg_x - s_w*0.04, leg_bottom - s_h*0.03, s_w*0.08, s_h*0.06))
    
    return sprite_surface


def get_goblin_shaman_sprite(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a detailed Goblin Shaman sprite procedurally."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size
    
    # Enhanced color palette
    skin_color = (40, 100, 40)      # Darker green skin
    dark_skin = (25, 75, 25)        # Shadow green
    robe_color = (75, 0, 130)       # Deep purple robe
    robe_trim = (138, 43, 226)      # Brighter purple trim
    staff_color = (139, 69, 19)     # Rich brown staff
    staff_metal = (192, 192, 192)   # Silver staff accents
    orb_color = (0, 255, 255)       # Cyan magical orb
    orb_glow = (100, 255, 255)      # Cyan glow
    eye_color = (255, 255, 0)       # Glowing yellow eyes
    gem_color = (148, 0, 211)       # Purple gems
    beard_color = (60, 60, 60)      # Gray beard
    
    # Flowing robes with detailed folds
    robe_main = pygame.Rect(s_w*0.18, s_h*0.32, s_w*0.55, s_h*0.45)
    pygame.draw.ellipse(sprite_surface, robe_color, robe_main)
    
    # Robe shadows for depth
    robe_shadow = pygame.Rect(robe_main.left + s_w*0.02, robe_main.centery, 
                             robe_main.width - s_w*0.02, robe_main.height//2)
    pygame.draw.ellipse(sprite_surface, (50, 0, 90), robe_shadow)
    
    # Ornate robe trim and patterns
    trim_rect = pygame.Rect(robe_main.left + s_w*0.02, robe_main.top + s_w*0.02, 
                           robe_main.width - s_w*0.04, s_h*0.08)
    pygame.draw.rect(sprite_surface, robe_trim, trim_rect)
    
    # Mystical symbols on robe
    symbol_positions = [
        (robe_main.centerx - s_w*0.08, robe_main.centery - s_h*0.05),
        (robe_main.centerx + s_w*0.08, robe_main.centery + s_h*0.02),
        (robe_main.centerx, robe_main.centery + s_h*0.08)
    ]
    for sym_x, sym_y in symbol_positions:
        # Draw mystical runes
        pygame.draw.circle(sprite_surface, gem_color, (int(sym_x), int(sym_y)), 2)
        pygame.draw.circle(sprite_surface, orb_glow, (int(sym_x), int(sym_y)), 1)
    
    # Detailed head with character
    head_x, head_y = robe_main.centerx, robe_main.top - s_h*0.08
    pygame.draw.circle(sprite_surface, skin_color, (int(head_x), int(head_y)), int(s_w*0.13))
    
    # Head shading for dimension
    pygame.draw.circle(sprite_surface, dark_skin, (int(head_x + s_w*0.02), int(head_y + s_h*0.02)), int(s_w*0.11))
    
    # Wise, pointed ears with earrings
    ear_outer = [
        (head_x - s_w*0.1, head_y - s_h*0.04),
        (head_x - s_w*0.18, head_y - s_h*0.14),
        (head_x - s_w*0.04, head_y - s_h*0.08)
    ]
    ear_inner = [
        (head_x - s_w*0.08, head_y - s_h*0.05),
        (head_x - s_w*0.15, head_y - s_h*0.11),
        (head_x - s_w*0.06, head_y - s_h*0.07)
    ]
    pygame.draw.polygon(sprite_surface, skin_color, ear_outer)
    pygame.draw.polygon(sprite_surface, dark_skin, ear_inner)
    
    # Magical earring
    pygame.draw.circle(sprite_surface, gem_color, (int(head_x - s_w*0.12), int(head_y - s_h*0.02)), 2)
    
    # Glowing, mystical eyes
    eye_main = (int(head_x + s_w*0.03), int(head_y - s_h*0.03))
    eye_secondary = (int(head_x - s_w*0.02), int(head_y - s_h*0.02))
    
    # Eye glow effects
    pygame.draw.circle(sprite_surface, (200, 200, 100), eye_main, 4)
    pygame.draw.circle(sprite_surface, eye_color, eye_main, 3)
    pygame.draw.circle(sprite_surface, (255, 255, 200), eye_main, 1)
    
    pygame.draw.circle(sprite_surface, (150, 150, 75), eye_secondary, 3)
    pygame.draw.circle(sprite_surface, eye_color, eye_secondary, 2)
    
    # Ancient wizard beard
    beard_points = [
        (head_x - s_w*0.06, head_y + s_h*0.08),
        (head_x + s_w*0.04, head_y + s_h*0.09),
        (head_x, head_y + s_h*0.18),
        (head_x - s_w*0.03, head_y + s_h*0.15)
    ]
    pygame.draw.polygon(sprite_surface, beard_color, beard_points)
    
    # Beard details
    for i in range(3):
        strand_x = head_x - s_w*0.02 + i * s_w*0.02
        strand_y = head_y + s_h*0.12 + i * s_h*0.01
        pygame.draw.line(sprite_surface, (80, 80, 80), 
                        (strand_x, strand_y), (strand_x, strand_y + s_h*0.04), 1)
    
    # Ornate magical staff
    staff_x = robe_main.right + s_w*0.03
    staff_bottom = robe_main.bottom + s_h*0.08
    staff_top = head_y - s_h*0.12
    
    # Staff shaft with details
    pygame.draw.line(sprite_surface, staff_color, (staff_x, staff_bottom), (staff_x, staff_top), 4)
    
    # Staff rings/bindings
    for ring_y in [staff_top + s_h*0.08, staff_top + s_h*0.16, staff_top + s_h*0.24]:
        pygame.draw.circle(sprite_surface, staff_metal, (int(staff_x), int(ring_y)), 2)
    
    # Elaborate staff head
    staff_head_rect = pygame.Rect(staff_x - s_w*0.03, staff_top - s_h*0.06, s_w*0.06, s_h*0.08)
    pygame.draw.ellipse(sprite_surface, staff_metal, staff_head_rect)
    
    # Multiple magical orbs with energy effects
    main_orb_pos = (int(staff_x), int(staff_top - s_h*0.02))
    
    # Orb glow layers (multiple for magical effect)
    pygame.draw.circle(sprite_surface, (50, 200, 200), main_orb_pos, 6)
    pygame.draw.circle(sprite_surface, orb_glow, main_orb_pos, 5)
    pygame.draw.circle(sprite_surface, orb_color, main_orb_pos, 4)
    pygame.draw.circle(sprite_surface, (200, 255, 255), main_orb_pos, 2)
    pygame.draw.circle(sprite_surface, (255, 255, 255), main_orb_pos, 1)
    
    # Smaller energy orbs orbiting the main one
    import math
    for i in range(3):
        angle = i * 2.1  # Offset angles
        orbit_x = main_orb_pos[0] + int(s_w*0.08 * math.cos(angle))
        orbit_y = main_orb_pos[1] + int(s_h*0.06 * math.sin(angle))
        pygame.draw.circle(sprite_surface, orb_glow, (orbit_x, orbit_y), 2)
        pygame.draw.circle(sprite_surface, orb_color, (orbit_x, orbit_y), 1)
    
    # Mystical energy emanating from staff
    energy_lines = [
        ((staff_x - s_w*0.05, staff_top + s_h*0.02), (staff_x - s_w*0.08, staff_top - s_h*0.04)),
        ((staff_x + s_w*0.05, staff_top + s_h*0.03), (staff_x + s_w*0.08, staff_top - s_h*0.02)),
        ((staff_x, staff_top + s_h*0.06), (staff_x + s_w*0.02, staff_top + s_h*0.12))
    ]
    for (start_x, start_y), (end_x, end_y) in energy_lines:
        pygame.draw.line(sprite_surface, orb_glow, (start_x, start_y), (end_x, end_y), 1)
    
    # Magical hood with star pattern
    hood_points = [
        (head_x - s_w*0.15, head_y - s_h*0.18),
        (head_x + s_w*0.12, head_y - s_h*0.15),
        (head_x + s_w*0.08, head_y - s_h*0.05),
        (head_x - s_w*0.12, head_y - s_h*0.08)
    ]
    pygame.draw.polygon(sprite_surface, robe_color, hood_points)
    
    # Star emblem on hood
    star_center = (int(head_x - s_w*0.02), int(head_y - s_h*0.12))
    star_points = []
    for i in range(5):
        angle = i * 1.257  # 2*pi/5 for 5-pointed star
        outer_x = star_center[0] + int(s_w*0.025 * math.cos(angle))
        outer_y = star_center[1] + int(s_h*0.025 * math.sin(angle))
        star_points.append((outer_x, outer_y))
    pygame.draw.polygon(sprite_surface, gem_color, star_points)
    
    # Arcane left hand gesture (visible from robe)
    hand_pos = (robe_main.left - s_w*0.02, robe_main.centery - s_h*0.05)
    pygame.draw.circle(sprite_surface, skin_color, hand_pos, 3)
    
    # Magical energy emanating from hand
    pygame.draw.circle(sprite_surface, orb_glow, hand_pos, 5)
    pygame.draw.circle(sprite_surface, orb_color, hand_pos, 3)
    
    return sprite_surface


def get_sand_scorpion_sprite(size: Tuple[int, int] = (48, 32)) -> pygame.Surface:
    """Generate a detailed Sand Scorpion sprite procedurally."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size
    
    # Enhanced color palette
    body_color = (194, 154, 108)    # Sandy brown base
    segment_color = (170, 130, 85)  # Darker brown for segments
    claw_color = (160, 120, 80)     # Claw color
    claw_inner = (140, 100, 60)     # Inner claw detail
    stinger_color = (80, 40, 20)    # Dark brown stinger
    poison_color = (120, 80, 140)   # Purple poison tip
    eye_color = (255, 50, 50)       # Bright red eyes
    leg_color = (180, 140, 95)      # Leg color
    shell_highlight = (220, 180, 130) # Shell highlights
    shadow_color = (150, 110, 70)   # Shadow color
    
    # Main cephalothorax (front body section)
    ceph_rect = pygame.Rect(s_w*0.15, s_h*0.35, s_w*0.28, s_h*0.35)
    pygame.draw.ellipse(sprite_surface, body_color, ceph_rect)
    
    # Cephalothorax shell pattern
    shell_rect = pygame.Rect(ceph_rect.left + s_w*0.02, ceph_rect.top + s_h*0.02, 
                            ceph_rect.width - s_w*0.04, ceph_rect.height - s_h*0.04)
    pygame.draw.ellipse(sprite_surface, shell_highlight, shell_rect)
    
    # Central ridge on cephalothorax
    ridge_rect = pygame.Rect(ceph_rect.centerx - s_w*0.01, ceph_rect.top + s_h*0.03, 
                            s_w*0.02, ceph_rect.height - s_h*0.06)
    pygame.draw.ellipse(sprite_surface, shadow_color, ridge_rect)
    
    # Detailed abdomen segments (metasoma)
    segments = []
    for i in range(5):
        seg_x = ceph_rect.right + i * s_w*0.05
        seg_y = ceph_rect.centery - s_h*0.08 + i * s_h*0.015  # Slight curve upward
        seg_width = s_w*0.06 - i * s_w*0.005  # Tapering segments
        seg_height = s_h*0.16 - i * s_h*0.01
        
        seg_rect = pygame.Rect(seg_x, seg_y, seg_width, seg_height)
        segments.append(seg_rect)
        
        # Draw segment with shading
        pygame.draw.ellipse(sprite_surface, segment_color, seg_rect)
        
        # Segment highlights
        highlight_rect = pygame.Rect(seg_rect.left + 1, seg_rect.top + 1, 
                                   seg_rect.width - 2, seg_rect.height//2)
        pygame.draw.ellipse(sprite_surface, body_color, highlight_rect)
        
        # Segmentation lines
        if i > 0:
            pygame.draw.line(sprite_surface, shadow_color, 
                           (seg_rect.left, seg_rect.top + s_h*0.02), 
                           (seg_rect.left, seg_rect.bottom - s_h*0.02), 1)
    
    # Massive, detailed claws (pedipalps)
    # Left claw
    left_claw_base = (ceph_rect.left - s_w*0.02, ceph_rect.centery - s_h*0.08)
    left_claw_joint = (left_claw_base[0] - s_w*0.08, left_claw_base[1] - s_h*0.05)
    left_claw_tip = (left_claw_joint[0] - s_w*0.1, left_claw_joint[1] - s_h*0.08)
    
    # Claw segments
    pygame.draw.line(sprite_surface, claw_color, left_claw_base, left_claw_joint, 6)
    pygame.draw.line(sprite_surface, claw_color, left_claw_joint, left_claw_tip, 5)
    
    # Claw pincers
    pincer_points = [
        left_claw_tip,
        (left_claw_tip[0] - s_w*0.06, left_claw_tip[1] - s_h*0.04),
        (left_claw_tip[0] - s_w*0.04, left_claw_tip[1] + s_h*0.02),
        (left_claw_tip[0] - s_w*0.02, left_claw_tip[1] + s_h*0.06)
    ]
    pygame.draw.polygon(sprite_surface, claw_color, pincer_points)
    pygame.draw.polygon(sprite_surface, claw_inner, [
        (pincer_points[0][0] + 1, pincer_points[0][1] + 1),
        (pincer_points[1][0] + 1, pincer_points[1][1] + 1),
        (pincer_points[2][0], pincer_points[2][1]),
        (pincer_points[3][0], pincer_points[3][1] - 1)
    ])
    
    # Right claw (mirror)
    right_claw_base = (ceph_rect.left - s_w*0.02, ceph_rect.centery + s_h*0.08)
    right_claw_joint = (right_claw_base[0] - s_w*0.08, right_claw_base[1] + s_h*0.05)
    right_claw_tip = (right_claw_joint[0] - s_w*0.1, right_claw_joint[1] + s_h*0.08)
    
    pygame.draw.line(sprite_surface, claw_color, right_claw_base, right_claw_joint, 6)
    pygame.draw.line(sprite_surface, claw_color, right_claw_joint, right_claw_tip, 5)
    
    pincer_points_r = [
        right_claw_tip,
        (right_claw_tip[0] - s_w*0.06, right_claw_tip[1] + s_h*0.04),
        (right_claw_tip[0] - s_w*0.04, right_claw_tip[1] - s_h*0.02),
        (right_claw_tip[0] - s_w*0.02, right_claw_tip[1] - s_h*0.06)
    ]
    pygame.draw.polygon(sprite_surface, claw_color, pincer_points_r)
    pygame.draw.polygon(sprite_surface, claw_inner, [
        (pincer_points_r[0][0] + 1, pincer_points_r[0][1] - 1),
        (pincer_points_r[1][0] + 1, pincer_points_r[1][1] - 1),
        (pincer_points_r[2][0], pincer_points_r[2][1]),
        (pincer_points_r[3][0], pincer_points_r[3][1] + 1)
    ])
    
    # Eight segmented legs (4 per side, more realistic)
    leg_positions = [
        # Left side legs
        (ceph_rect.left + s_w*0.05, ceph_rect.top + s_h*0.08, -1),
        (ceph_rect.left + s_w*0.12, ceph_rect.top + s_h*0.12, -1),
        (ceph_rect.left + s_w*0.18, ceph_rect.bottom - s_h*0.12, -1),
        (ceph_rect.left + s_w*0.25, ceph_rect.bottom - s_h*0.08, -1),
        # Right side legs
        (ceph_rect.left + s_w*0.05, ceph_rect.top + s_h*0.08, 1),
        (ceph_rect.left + s_w*0.12, ceph_rect.top + s_h*0.12, 1),
        (ceph_rect.left + s_w*0.18, ceph_rect.bottom - s_h*0.12, 1),
        (ceph_rect.left + s_w*0.25, ceph_rect.bottom - s_h*0.08, 1)
    ]
    
    for leg_x, leg_y, side in leg_positions:
        # Leg segments with joints
        joint1 = (leg_x + side * s_w*0.06, leg_y + side * s_h*0.08)
        joint2 = (joint1[0] + side * s_w*0.05, joint1[1] + s_h*0.06)
        leg_end = (joint2[0] + side * s_w*0.03, joint2[1] + s_h*0.04)
        
        # Draw leg segments
        pygame.draw.line(sprite_surface, leg_color, (leg_x, leg_y), joint1, 3)
        pygame.draw.line(sprite_surface, leg_color, joint1, joint2, 2)
        pygame.draw.line(sprite_surface, leg_color, joint2, leg_end, 2)
        
        # Joint spheres
        pygame.draw.circle(sprite_surface, claw_color, (int(joint1[0]), int(joint1[1])), 2)
        pygame.draw.circle(sprite_surface, claw_color, (int(joint2[0]), int(joint2[1])), 1)
        
        # Sharp leg tip
        pygame.draw.circle(sprite_surface, stinger_color, (int(leg_end[0]), int(leg_end[1])), 1)
    
    # Menacing tail with venomous stinger
    if segments:
        tail_start = (segments[-1].right, segments[-1].centery)
        tail_curve = (tail_start[0] + s_w*0.08, tail_start[1] - s_h*0.18)
        tail_end = (tail_curve[0] + s_w*0.04, tail_curve[1] - s_h*0.08)
        
        # Tail segments
        pygame.draw.line(sprite_surface, segment_color, tail_start, tail_curve, 5)
        pygame.draw.line(sprite_surface, segment_color, tail_curve, tail_end, 4)
        
        # Bulbous stinger base
        stinger_base_rect = pygame.Rect(tail_end[0] - s_w*0.015, tail_end[1] - s_h*0.02, 
                                       s_w*0.03, s_h*0.04)
        pygame.draw.ellipse(sprite_surface, stinger_color, stinger_base_rect)
        
        # Sharp stinger point
        stinger_tip = (tail_end[0] + s_w*0.025, tail_end[1] - s_h*0.015)
        stinger_points = [
            tail_end,
            stinger_tip,
            (tail_end[0] + s_w*0.02, tail_end[1] + s_h*0.01)
        ]
        pygame.draw.polygon(sprite_surface, stinger_color, stinger_points)
        
        # Poison drop at tip
        pygame.draw.circle(sprite_surface, poison_color, 
                          (int(stinger_tip[0]), int(stinger_tip[1])), 2)
        pygame.draw.circle(sprite_surface, (160, 120, 180), 
                          (int(stinger_tip[0]), int(stinger_tip[1])), 1)
    
    # Multiple compound eyes
    eye_positions = [
        (ceph_rect.left + s_w*0.04, ceph_rect.top + s_h*0.06),
        (ceph_rect.left + s_w*0.08, ceph_rect.top + s_h*0.04),
        (ceph_rect.left + s_w*0.12, ceph_rect.top + s_h*0.07)
    ]
    
    for eye_x, eye_y in eye_positions:
        # Eye with menacing glow
        pygame.draw.circle(sprite_surface, (100, 25, 25), (int(eye_x), int(eye_y)), 3)
        pygame.draw.circle(sprite_surface, eye_color, (int(eye_x), int(eye_y)), 2)
        pygame.draw.circle(sprite_surface, (255, 100, 100), (int(eye_x), int(eye_y)), 1)
    
    # Chelicerae (feeding pincers near mouth)
    mouth_pos = (ceph_rect.left + s_w*0.02, ceph_rect.centery)
    chel_points = [
        mouth_pos,
        (mouth_pos[0] - s_w*0.025, mouth_pos[1] - s_h*0.02),
        (mouth_pos[0] - s_w*0.015, mouth_pos[1] + s_h*0.02)
    ]
    pygame.draw.polygon(sprite_surface, stinger_color, chel_points)
    
    return sprite_surface


def get_horse_sprite(size: Tuple[int, int] = (48, 40)) -> pygame.Surface:
    """Generate a detailed Wild Horse sprite procedurally."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size
    
    # Enhanced color palette
    body_color = (150, 90, 40)      # Rich chestnut brown
    darker_brown = (120, 70, 30)    # Darker brown for shadows
    lighter_brown = (180, 120, 60)  # Lighter brown highlights
    mane_color = (80, 50, 20)       # Dark brown mane
    mane_highlight = (110, 70, 30)  # Mane highlights
    leg_color = (100, 60, 25)       # Dark brown legs
    hoof_color = (60, 45, 25)       # Dark hooves
    eye_color = (40, 25, 15)        # Dark brown eyes
    eye_highlight = (255, 255, 255) # White eye highlight
    nostril_color = (80, 50, 30)    # Dark nostrils
    marking_color = (200, 160, 120) # Light markings
    
    # Powerful, athletic body with proper horse proportions
    body_rect = pygame.Rect(s_w*0.15, s_h*0.32, s_w*0.58, s_h*0.35)
    pygame.draw.ellipse(sprite_surface, body_color, body_rect)
    
    # Body muscle definition and shading
    muscle_rect = pygame.Rect(body_rect.left + 1, body_rect.top + 2, body_rect.width - 2, body_rect.height*0.8)
    pygame.draw.ellipse(sprite_surface, darker_brown, muscle_rect)
    
    # Chest and shoulder definition
    chest_rect = pygame.Rect(body_rect.left - s_w*0.03, body_rect.top + s_h*0.02, s_w*0.25, s_h*0.25)
    pygame.draw.ellipse(sprite_surface, body_color, chest_rect)
    pygame.draw.ellipse(sprite_surface, darker_brown, (chest_rect.left + 1, chest_rect.top + 1, 
                                                      chest_rect.width - 2, chest_rect.height - 2))
    
    # Distinctive horse head shape
    head_x, head_y = body_rect.left - s_w*0.15, body_rect.top - s_h*0.08
    head_rect = pygame.Rect(head_x, head_y, s_w*0.25, s_h*0.28)
    pygame.draw.ellipse(sprite_surface, body_color, head_rect)
    
    # Head shading
    head_shadow = pygame.Rect(head_x + 1, head_y + 2, head_rect.width - 2, head_rect.height - 3)
    pygame.draw.ellipse(sprite_surface, darker_brown, head_shadow)
    
    # Elongated horse muzzle/snout
    muzzle_rect = pygame.Rect(head_x - s_w*0.08, head_y + s_h*0.12, s_w*0.12, s_h*0.1)
    pygame.draw.ellipse(sprite_surface, body_color, muzzle_rect)
    pygame.draw.ellipse(sprite_surface, darker_brown, (muzzle_rect.left + 1, muzzle_rect.top + 1, 
                                                      muzzle_rect.width - 2, muzzle_rect.height - 2))
    
    # Graceful curved neck
    neck_points = [
        (body_rect.left, body_rect.top),
        (head_x + s_w*0.18, head_y + s_h*0.22),
        (head_x + s_w*0.15, head_y + s_h*0.15),
        (body_rect.left + s_w*0.08, body_rect.top + s_h*0.05)
    ]
    pygame.draw.polygon(sprite_surface, body_color, neck_points)
    pygame.draw.polygon(sprite_surface, darker_brown, [(x+1, y+1) for x, y in neck_points[:-1]] + [neck_points[-1]])
    
    # Alert, pointed ears
    ear_left = [
        (head_x + s_w*0.08, head_y + s_h*0.02),
        (head_x + s_w*0.06, head_y - s_h*0.06),
        (head_x + s_w*0.12, head_y - s_h*0.02)
    ]
    ear_right = [
        (head_x + s_w*0.16, head_y + s_h*0.02),
        (head_x + s_w*0.2, head_y - s_h*0.06),
        (head_x + s_w*0.14, head_y - s_h*0.02)
    ]
    
    pygame.draw.polygon(sprite_surface, body_color, ear_left)
    pygame.draw.polygon(sprite_surface, body_color, ear_right)
    pygame.draw.polygon(sprite_surface, darker_brown, [(x+0.5, y+0.5) for x, y in ear_left])
    pygame.draw.polygon(sprite_surface, darker_brown, [(x+0.5, y+0.5) for x, y in ear_right])
    
    # Flowing, dynamic mane
    mane_base_x = head_x + s_w*0.12
    mane_segments = [
        # (x_offset, y_offset, length, angle)
        (0, -s_h*0.02, s_h*0.15, -0.3),
        (s_w*0.02, -s_h*0.01, s_h*0.18, -0.2),
        (s_w*0.04, 0, s_h*0.16, -0.1),
        (s_w*0.06, s_h*0.01, s_h*0.14, 0),
        (s_w*0.08, s_h*0.02, s_h*0.12, 0.1),
        (s_w*0.1, s_h*0.03, s_h*0.1, 0.2)
    ]
    
    for x_off, y_off, length, angle in mane_segments:
        mane_start = (mane_base_x + x_off, head_y + y_off)
        mane_end = (mane_start[0] + length*angle*0.8, mane_start[1] - length*0.9)
        
        # Main mane strand
        pygame.draw.line(sprite_surface, mane_color, mane_start, mane_end, 3)
        # Mane highlight
        highlight_end = (mane_end[0] - 1, mane_end[1] + 1)
        pygame.draw.line(sprite_surface, mane_highlight, mane_start, highlight_end, 2)
    
    # Intelligent, alert eyes
    eye_left = (int(head_x + s_w*0.1), int(head_y + s_h*0.06))
    eye_right = (int(head_x + s_w*0.18), int(head_y + s_h*0.06))
    
    # Eye details
    pygame.draw.ellipse(sprite_surface, (255, 255, 255), (eye_left[0] - 3, eye_left[1] - 2, 6, 4))
    pygame.draw.ellipse(sprite_surface, (255, 255, 255), (eye_right[0] - 3, eye_right[1] - 2, 6, 4))
    pygame.draw.circle(sprite_surface, eye_color, eye_left, 2)
    pygame.draw.circle(sprite_surface, eye_color, eye_right, 2)
    pygame.draw.circle(sprite_surface, eye_highlight, (eye_left[0] - 1, eye_left[1] - 1), 1)
    pygame.draw.circle(sprite_surface, eye_highlight, (eye_right[0] - 1, eye_right[1] - 1), 1)
    
    # Detailed nostrils
    nostril_left = (int(muzzle_rect.left + s_w*0.02), int(muzzle_rect.top + s_h*0.03))
    nostril_right = (int(muzzle_rect.left + s_w*0.06), int(muzzle_rect.top + s_h*0.03))
    
    pygame.draw.ellipse(sprite_surface, nostril_color, (nostril_left[0] - 1, nostril_left[1] - 1, 3, 2))
    pygame.draw.ellipse(sprite_surface, nostril_color, (nostril_right[0] - 1, nostril_right[1] - 1, 3, 2))
    
    # Facial marking (white blaze)
    blaze_points = [
        (head_x + s_w*0.12, head_y + s_h*0.02),
        (head_x + s_w*0.14, head_y + s_h*0.02),
        (muzzle_rect.centerx + s_w*0.01, muzzle_rect.top + s_h*0.02),
        (muzzle_rect.centerx - s_w*0.01, muzzle_rect.top + s_h*0.02)
    ]
    pygame.draw.polygon(sprite_surface, marking_color, blaze_points)
    
    # Powerful legs with proper horse anatomy
    leg_thickness = 4
    leg_data = [
        # (x_offset, stance_adj, is_front_leg)
        (0.08, -s_w*0.01, True),   # Front left
        (0.25, s_w*0.01, True),    # Front right
        (0.55, -s_w*0.015, False), # Back left
        (0.72, s_w*0.015, False)   # Back right
    ]
    
    for leg_offset, stance_adj, is_front in leg_data:
        leg_x = body_rect.left + body_rect.width * leg_offset
        leg_top = body_rect.bottom - s_h*0.02
        
        if is_front:
            # Front legs (straighter)
            leg_bottom = leg_top + s_h*0.28
            pygame.draw.line(sprite_surface, leg_color, (leg_x, leg_top), (leg_x + stance_adj, leg_bottom), leg_thickness)
        else:
            # Back legs (more angled for power)
            knee_y = leg_top + s_h*0.15
            leg_bottom = knee_y + s_h*0.18
            # Upper leg (thigh)
            pygame.draw.line(sprite_surface, leg_color, (leg_x, leg_top), (leg_x + stance_adj*0.5, knee_y), leg_thickness + 1)
            # Lower leg
            pygame.draw.line(sprite_surface, leg_color, (leg_x + stance_adj*0.5, knee_y), (leg_x + stance_adj, leg_bottom), leg_thickness)
            # Knee joint
            pygame.draw.circle(sprite_surface, darker_brown, (int(leg_x + stance_adj*0.5), int(knee_y)), 2)
        
        # Hooves
        hoof_center = (leg_x + stance_adj, leg_bottom if is_front else leg_bottom)
        hoof_rect = pygame.Rect(hoof_center[0] - s_w*0.015, hoof_center[1] - s_h*0.02, s_w*0.03, s_h*0.04)
        pygame.draw.ellipse(sprite_surface, hoof_color, hoof_rect)
        
        # Hoof detail
        pygame.draw.line(sprite_surface, (40, 30, 15), 
                        (hoof_rect.centerx, hoof_rect.top + 2),
                        (hoof_rect.centerx, hoof_rect.bottom - 2), 1)
    
    # Majestic flowing tail
    tail_base = (body_rect.right - s_w*0.02, body_rect.centery + s_h*0.05)
    
    # Tail segments for flowing effect
    tail_segments = [
        (tail_base[0] + s_w*0.06, tail_base[1] + s_h*0.08),
        (tail_base[0] + s_w*0.12, tail_base[1] + s_h*0.15),
        (tail_base[0] + s_w*0.16, tail_base[1] + s_h*0.18),
        (tail_base[0] + s_w*0.18, tail_base[1] + s_h*0.22)
    ]
    
    # Draw flowing tail
    current_pos = tail_base
    for i, segment_end in enumerate(tail_segments):
        thickness = 5 - i
        pygame.draw.line(sprite_surface, mane_color, current_pos, segment_end, thickness)
        # Tail highlights
        if i < 2:
            pygame.draw.line(sprite_surface, mane_highlight, current_pos, segment_end, thickness - 1)
        current_pos = segment_end
    
    # Tail wisps at the end
    tail_end = tail_segments[-1]
    wisp_directions = [
        (s_w*0.02, s_h*0.03),
        (s_w*0.04, s_h*0.02),
        (s_w*0.01, s_h*0.04)
    ]
    
    for wisp_dx, wisp_dy in wisp_directions:
        wisp_end = (tail_end[0] + wisp_dx, tail_end[1] + wisp_dy)
        pygame.draw.line(sprite_surface, mane_color, tail_end, wisp_end, 2)
    
    return sprite_surface


def get_cow_sprite(size: Tuple[int, int] = (48, 40)) -> pygame.Surface:
    """Generate a detailed Cow sprite procedurally."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size
    
    # Enhanced color palette
    body_color = (255, 255, 255)    # Pure white base
    spot_color = (20, 20, 20)       # Deep black spots
    udder_color = (255, 182, 193)   # Pink udder
    eye_color = (40, 40, 40)        # Dark gray eyes
    nose_color = (255, 192, 203)    # Pink nose
    inner_ear = (255, 220, 220)     # Light pink inner ear
    hoof_color = (60, 60, 60)       # Dark gray hooves
    horn_color = (240, 230, 210)    # Cream colored horns
    shadow_color = (240, 240, 240)  # Light gray for shadows
    
    # Main body (more realistic cow proportions)
    body_rect = pygame.Rect(s_w*0.12, s_h*0.28, s_w*0.65, s_h*0.42)
    pygame.draw.ellipse(sprite_surface, body_color, body_rect)
    
    # Add subtle body shading for dimension
    shadow_rect = pygame.Rect(body_rect.left, body_rect.centery, body_rect.width, body_rect.height//2)
    pygame.draw.ellipse(sprite_surface, shadow_color, shadow_rect)
    
    # Detailed head with proper cow proportions
    head_x, head_y = body_rect.left - s_w*0.12, body_rect.top + s_h*0.02
    head_rect = pygame.Rect(head_x, head_y, s_w*0.28, s_h*0.25)
    pygame.draw.ellipse(sprite_surface, body_color, head_rect)
    
    # Head shadow
    head_shadow = pygame.Rect(head_x + s_w*0.02, head_y + s_h*0.02, s_w*0.26, s_h*0.23)
    pygame.draw.ellipse(sprite_surface, shadow_color, head_shadow)
    
    # Small horns
    horn_left = [
        (head_x + s_w*0.08, head_y - s_h*0.02),
        (head_x + s_w*0.06, head_y - s_h*0.08),
        (head_x + s_w*0.1, head_y - s_h*0.06)
    ]
    horn_right = [
        (head_x + s_w*0.18, head_y - s_h*0.02),
        (head_x + s_w*0.2, head_y - s_h*0.08),
        (head_x + s_w*0.16, head_y - s_h*0.06)
    ]
    pygame.draw.polygon(sprite_surface, horn_color, horn_left)
    pygame.draw.polygon(sprite_surface, horn_color, horn_right)
    
    # Detailed ears
    ear_outer = pygame.Rect(head_x + s_w*0.02, head_y + s_h*0.02, s_w*0.08, s_h*0.12)
    ear_inner = pygame.Rect(head_x + s_w*0.03, head_y + s_h*0.04, s_w*0.06, s_h*0.08)
    pygame.draw.ellipse(sprite_surface, body_color, ear_outer)
    pygame.draw.ellipse(sprite_surface, inner_ear, ear_inner)
    
    # Realistic cow spots pattern
    spot_data = [
        # (x_offset, y_offset, width, height)
        (body_rect.centerx - s_w*0.15, body_rect.top + s_h*0.03, s_w*0.12, s_h*0.08),
        (body_rect.centerx + s_w*0.08, body_rect.top + s_h*0.12, s_w*0.15, s_h*0.1),
        (body_rect.left + s_w*0.05, body_rect.centery + s_h*0.02, s_w*0.1, s_h*0.06),
        (body_rect.right - s_w*0.18, body_rect.bottom - s_h*0.12, s_w*0.14, s_h*0.08),
        (head_x + s_w*0.12, head_y + s_h*0.08, s_w*0.08, s_h*0.06)
    ]
    
    for spot_x, spot_y, spot_w, spot_h in spot_data:
        spot_rect = pygame.Rect(spot_x, spot_y, spot_w, spot_h)
        pygame.draw.ellipse(sprite_surface, spot_color, spot_rect)
        # Add spot edges for more organic look
        edge_rect = pygame.Rect(spot_x + 1, spot_y + 1, spot_w - 2, spot_h - 2)
        pygame.draw.ellipse(sprite_surface, (40, 40, 40), edge_rect)
    
    # Expressive eyes
    eye_left = (int(head_x + s_w*0.08), int(head_y + s_h*0.1))
    eye_right = (int(head_x + s_w*0.18), int(head_y + s_h*0.1))
    
    # Eye whites
    pygame.draw.ellipse(sprite_surface, (255, 255, 255), (eye_left[0] - 3, eye_left[1] - 2, 6, 4))
    pygame.draw.ellipse(sprite_surface, (255, 255, 255), (eye_right[0] - 3, eye_right[1] - 2, 6, 4))
    
    # Eye pupils
    pygame.draw.circle(sprite_surface, eye_color, eye_left, 2)
    pygame.draw.circle(sprite_surface, eye_color, eye_right, 2)
    
    # Eye highlights
    pygame.draw.circle(sprite_surface, (255, 255, 255), (eye_left[0] - 1, eye_left[1] - 1), 1)
    pygame.draw.circle(sprite_surface, (255, 255, 255), (eye_right[0] - 1, eye_right[1] - 1), 1)
    
    # Detailed snout and nose
    snout_rect = pygame.Rect(head_x - s_w*0.06, head_y + s_h*0.12, s_w*0.1, s_h*0.08)
    pygame.draw.ellipse(sprite_surface, body_color, snout_rect)
    pygame.draw.ellipse(sprite_surface, shadow_color, (snout_rect.left + 1, snout_rect.top + 1, snout_rect.width - 2, snout_rect.height - 2))
    
    # Nose
    nose_rect = pygame.Rect(snout_rect.centerx - s_w*0.025, snout_rect.top + s_h*0.02, s_w*0.05, s_h*0.04)
    pygame.draw.ellipse(sprite_surface, nose_color, nose_rect)
    
    # Nostrils
    nostril_left = (int(nose_rect.centerx - s_w*0.01), int(nose_rect.centery))
    nostril_right = (int(nose_rect.centerx + s_w*0.01), int(nose_rect.centery))
    pygame.draw.circle(sprite_surface, (200, 150, 150), nostril_left, 1)
    pygame.draw.circle(sprite_surface, (200, 150, 150), nostril_right, 1)
    
    # Mouth line
    mouth_start = (snout_rect.centerx - s_w*0.02, snout_rect.bottom - s_h*0.02)
    mouth_end = (snout_rect.centerx + s_w*0.02, snout_rect.bottom - s_h*0.02)
    pygame.draw.line(sprite_surface, (180, 180, 180), mouth_start, mouth_end, 1)
    
    # Detailed udder
    udder_main = pygame.Rect(body_rect.centerx - s_w*0.08, body_rect.bottom - s_h*0.1, s_w*0.16, s_h*0.12)
    pygame.draw.ellipse(sprite_surface, udder_color, udder_main)
    
    # Udder teats
    teat_positions = [
        (udder_main.centerx - s_w*0.04, udder_main.bottom - s_h*0.02),
        (udder_main.centerx + s_w*0.04, udder_main.bottom - s_h*0.02),
        (udder_main.centerx - s_w*0.02, udder_main.bottom),
        (udder_main.centerx + s_w*0.02, udder_main.bottom)
    ]
    
    for teat_x, teat_y in teat_positions:
        pygame.draw.ellipse(sprite_surface, (255, 160, 160), (teat_x - s_w*0.01, teat_y - s_h*0.015, s_w*0.02, s_h*0.03))
    
    # Sturdy legs with proper cow proportions
    leg_thickness = int(s_w*0.045)
    leg_data = [
        # (x_offset_from_body, stance_adjustment)
        (0.15, -s_w*0.01),  # Front left
        (0.35, s_w*0.01),   # Front right  
        (0.55, -s_w*0.015), # Back left
        (0.75, s_w*0.015)   # Back right
    ]
    
    for leg_offset, stance_adj in leg_data:
        leg_x = body_rect.left + body_rect.width * leg_offset
        leg_top = body_rect.bottom - s_h*0.02
        leg_bottom = leg_top + s_h*0.22
        
        # Upper leg (thicker)
        upper_end = leg_top + s_h*0.12
        pygame.draw.line(sprite_surface, body_color, (leg_x, leg_top), (leg_x + stance_adj, upper_end), leg_thickness)
        
        # Lower leg (slightly thinner)
        pygame.draw.line(sprite_surface, body_color, (leg_x + stance_adj, upper_end), (leg_x + stance_adj, leg_bottom), leg_thickness - 1)
        
        # Knee joint
        pygame.draw.circle(sprite_surface, shadow_color, (int(leg_x + stance_adj), int(upper_end)), leg_thickness // 2)
        
        # Hoof
        hoof_rect = pygame.Rect(leg_x + stance_adj - s_w*0.025, leg_bottom - s_h*0.025, s_w*0.05, s_h*0.05)
        pygame.draw.ellipse(sprite_surface, hoof_color, hoof_rect)
        
        # Hoof split
        pygame.draw.line(sprite_surface, (40, 40, 40), 
                        (hoof_rect.centerx, hoof_rect.top + 2), 
                        (hoof_rect.centerx, hoof_rect.bottom - 2), 1)
    
    # Tail with tuft
    tail_start = (body_rect.right - s_w*0.02, body_rect.centery + s_h*0.08)
    tail_mid = (tail_start[0] + s_w*0.08, tail_start[1] + s_h*0.12)
    tail_end = (tail_mid[0] + s_w*0.04, tail_mid[1] + s_h*0.06)
    
    # Tail body
    pygame.draw.line(sprite_surface, spot_color, tail_start, tail_mid, 3)
    pygame.draw.line(sprite_surface, spot_color, tail_mid, tail_end, 2)
    
    # Tail tuft
    tuft_center = tail_end
    for i in range(5):
        angle_offset = (i - 2) * 0.4
        tuft_end = (tuft_center[0] + s_w*0.03 * (1 + angle_offset*0.2), 
                   tuft_center[1] + s_h*0.04 * (1 + abs(angle_offset)*0.3))
        pygame.draw.line(sprite_surface, spot_color, tuft_center, tuft_end, 1)
    
    return sprite_surface


def get_chicken_sprite(size: Tuple[int, int] = (24, 24)) -> pygame.Surface:
    """Generate a detailed Chicken sprite procedurally."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size
    
    # Enhanced color palette
    body_color = (250, 250, 250)    # Pure white feathers
    shadow_color = (230, 230, 230)  # Light gray shadows
    comb_color = (220, 20, 20)      # Deep red comb
    wattle_color = (200, 40, 40)    # Red wattles
    beak_color = (255, 200, 100)    # Golden orange beak
    eye_color = (60, 60, 60)        # Dark gray eyes
    eye_highlight = (255, 255, 255) # White eye highlight
    leg_color = (255, 180, 80)      # Golden orange legs
    claw_color = (180, 140, 60)     # Darker claws
    wing_detail = (240, 240, 240)   # Wing feather detail
    tail_color = (245, 245, 245)    # Tail feathers
    
    # Plump, rounded body with realistic chicken proportions
    body_rect = pygame.Rect(s_w*0.18, s_h*0.35, s_w*0.55, s_h*0.42)
    pygame.draw.ellipse(sprite_surface, body_color, body_rect)
    
    # Body shadow for dimension
    shadow_rect = pygame.Rect(body_rect.left + 1, body_rect.top + 2, body_rect.width - 2, body_rect.height - 3)
    pygame.draw.ellipse(sprite_surface, shadow_color, shadow_rect)
    
    # Rounded breast area
    breast_rect = pygame.Rect(body_rect.left + s_w*0.02, body_rect.centery, body_rect.width - s_w*0.04, body_rect.height*0.55)
    pygame.draw.ellipse(sprite_surface, body_color, breast_rect)
    
    # Detailed head with proper chicken proportions  
    head_x, head_y = body_rect.left - s_w*0.08, body_rect.top - s_h*0.05
    head_radius = int(s_w*0.14)
    pygame.draw.circle(sprite_surface, body_color, (int(head_x), int(head_y)), head_radius)
    
    # Head shadow
    pygame.draw.circle(sprite_surface, shadow_color, (int(head_x + 1), int(head_y + 1)), head_radius - 1)
    
    # Detailed multi-pointed comb
    comb_base_y = head_y - head_radius*0.8
    comb_points = [
        (head_x - s_w*0.03, head_y - s_h*0.06),
        (head_x - s_w*0.02, comb_base_y - s_h*0.06),
        (head_x - s_w*0.005, comb_base_y - s_h*0.08),
        (head_x + s_w*0.005, comb_base_y - s_h*0.08),
        (head_x + s_w*0.02, comb_base_y - s_h*0.06),
        (head_x + s_w*0.03, head_y - s_h*0.06),
        (head_x, head_y - s_h*0.05)
    ]
    pygame.draw.polygon(sprite_surface, comb_color, comb_points)
    
    # Comb shading
    comb_shadow = [(x+0.5, y+0.5) for x, y in comb_points[:-1]]
    comb_shadow.append(comb_points[-1])
    pygame.draw.polygon(sprite_surface, (180, 15, 15), comb_shadow)
    
    # Wattles (hanging from beneath beak)
    wattle_left = [
        (head_x - s_w*0.06, head_y + s_h*0.04),
        (head_x - s_w*0.08, head_y + s_h*0.08),
        (head_x - s_w*0.04, head_y + s_h*0.06)
    ]
    wattle_right = [
        (head_x - s_w*0.02, head_y + s_h*0.04),
        (head_x - s_w*0.04, head_y + s_h*0.08),
        (head_x, head_y + s_h*0.06)
    ]
    pygame.draw.polygon(sprite_surface, wattle_color, wattle_left)
    pygame.draw.polygon(sprite_surface, wattle_color, wattle_right)
    
    # Detailed curved beak
    beak_points = [
        (head_x - s_w*0.12, head_y - s_h*0.01),
        (head_x - s_w*0.16, head_y + s_h*0.01),
        (head_x - s_w*0.14, head_y + s_h*0.03),
        (head_x - s_w*0.1, head_y + s_h*0.02)
    ]
    pygame.draw.polygon(sprite_surface, beak_color, beak_points)
    
    # Beak highlight
    pygame.draw.line(sprite_surface, (255, 220, 150), 
                    (head_x - s_w*0.13, head_y), (head_x - s_w*0.15, head_y + s_h*0.005), 1)
    
    # Alert, expressive eye
    eye_center = (int(head_x - s_w*0.025), int(head_y - s_h*0.025))
    pygame.draw.circle(sprite_surface, (255, 255, 255), eye_center, 3)  # Eye white
    pygame.draw.circle(sprite_surface, eye_color, eye_center, 2)         # Eye pupil
    pygame.draw.circle(sprite_surface, eye_highlight, (eye_center[0] - 1, eye_center[1] - 1), 1)  # Highlight
    
    # Detailed wing with feather patterns
    wing_main = pygame.Rect(body_rect.left + s_w*0.02, body_rect.top + s_h*0.02, s_w*0.3, s_h*0.25)
    pygame.draw.ellipse(sprite_surface, wing_detail, wing_main)
    
    # Wing feather details (layered effect)
    for i in range(3):
        feather_rect = pygame.Rect(wing_main.left + i*s_w*0.04, wing_main.top + i*s_h*0.02, 
                                  wing_main.width - i*s_w*0.05, wing_main.height - i*s_h*0.03)
        pygame.draw.ellipse(sprite_surface, shadow_color, feather_rect, 1)
    
    # Primary flight feathers
    feather_tips = [
        (wing_main.right - s_w*0.02, wing_main.bottom - s_h*0.01),
        (wing_main.right, wing_main.bottom - s_h*0.04),
        (wing_main.right - s_w*0.01, wing_main.bottom - s_h*0.08)
    ]
    for tip_x, tip_y in feather_tips:
        pygame.draw.line(sprite_surface, (200, 200, 200), 
                        (tip_x - s_w*0.03, tip_y), (tip_x, tip_y), 1)
    
    # Tail feathers (fanned upward)
    tail_base = (body_rect.right - s_w*0.01, body_rect.top + s_h*0.08)
    tail_feathers = [
        (tail_base[0] + s_w*0.08, tail_base[1] - s_h*0.12),
        (tail_base[0] + s_w*0.1, tail_base[1] - s_h*0.08),
        (tail_base[0] + s_w*0.09, tail_base[1] - s_h*0.04),
        (tail_base[0] + s_w*0.06, tail_base[1]),
    ]
    
    for i, (feather_x, feather_y) in enumerate(tail_feathers):
        # Individual tail feathers
        feather_points = [
            tail_base,
            (feather_x - s_w*0.01, feather_y),
            (feather_x, feather_y - s_h*0.02),
            (feather_x + s_w*0.01, feather_y)
        ]
        pygame.draw.polygon(sprite_surface, tail_color, feather_points)
        # Feather spine
        pygame.draw.line(sprite_surface, shadow_color, tail_base, (feather_x, feather_y), 1)
    
    # Sturdy legs with realistic chicken proportions
    leg_thickness = 2
    for i, leg_offset in enumerate([0.35, 0.65]):
        leg_x = body_rect.left + body_rect.width * leg_offset
        leg_top = body_rect.bottom - s_h*0.02
        # Upper leg (thigh, hidden in body)
        mid_leg = leg_top + s_h*0.1
        # Lower leg (visible)
        leg_bottom = mid_leg + s_h*0.18
        
        # Visible leg section
        pygame.draw.line(sprite_surface, leg_color, (leg_x, mid_leg), (leg_x, leg_bottom), leg_thickness)
        
        # Detailed chicken feet with three forward toes and one back toe
        foot_center = (leg_x, leg_bottom)
        
        # Forward toes
        toe_angles = [-0.4, 0, 0.4]  # Left, center, right toes
        for angle in toe_angles:
            toe_end = (foot_center[0] + s_w*0.06*angle, foot_center[1] + s_h*0.06)
            pygame.draw.line(sprite_surface, leg_color, foot_center, toe_end, 1)
            # Claw at toe end
            claw_tip = (toe_end[0] + s_w*0.01*angle, toe_end[1] + s_h*0.02)
            pygame.draw.line(sprite_surface, claw_color, toe_end, claw_tip, 1)
        
        # Back toe (smaller)
        back_toe = (foot_center[0] - s_w*0.03, foot_center[1] + s_h*0.02)
        pygame.draw.line(sprite_surface, leg_color, foot_center, back_toe, 1)
        pygame.draw.line(sprite_surface, claw_color, back_toe, 
                        (back_toe[0] - s_w*0.01, back_toe[1] + s_h*0.015), 1)
    
    return sprite_surface


def get_deer_sprite(size: Tuple[int, int] = (40, 48)) -> pygame.Surface:
    """Generate a detailed Deer sprite procedurally."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size
    
    # Enhanced color palette
    body_color = (160, 100, 60)     # Rich reddish-brown
    darker_brown = (120, 75, 45)    # Darker brown for shadows
    belly_color = (220, 190, 160)   # Light cream underbelly
    antler_color = (140, 110, 80)   # Bone-colored antlers
    antler_tips = (180, 150, 120)   # Lighter antler tips
    eye_color = (40, 20, 10)        # Deep brown eyes
    eye_highlight = (255, 255, 255) # White eye highlight
    nose_color = (80, 60, 40)       # Dark brown nose
    inner_ear = (200, 160, 130)     # Pink inner ear
    hoof_color = (60, 45, 30)       # Dark hooves
    tail_tip = (250, 250, 250)      # White tail underside
    
    # Elegant, athletic body with proper deer proportions
    body_rect = pygame.Rect(s_w*0.15, s_h*0.32, s_w*0.55, s_h*0.28)
    pygame.draw.ellipse(sprite_surface, body_color, body_rect)
    
    # Body muscle definition and shadows
    muscle_rect = pygame.Rect(body_rect.left + s_w*0.02, body_rect.top + s_h*0.01, 
                             body_rect.width - s_w*0.04, body_rect.height*0.8)
    pygame.draw.ellipse(sprite_surface, darker_brown, muscle_rect)
    
    # Prominent chest area
    chest_rect = pygame.Rect(body_rect.left - s_w*0.03, body_rect.top + s_h*0.02, s_w*0.25, s_h*0.2)
    pygame.draw.ellipse(sprite_surface, body_color, chest_rect)
    
    # Light underbelly marking
    belly_rect = pygame.Rect(body_rect.left + s_w*0.05, body_rect.centery + s_h*0.02, 
                            body_rect.width*0.7, body_rect.height*0.6)
    pygame.draw.ellipse(sprite_surface, belly_color, belly_rect)
    
    # Graceful neck and head
    neck_start = (body_rect.left - s_w*0.02, body_rect.top + s_h*0.05)
    neck_end = (body_rect.left - s_w*0.12, body_rect.top - s_h*0.08)
    pygame.draw.line(sprite_surface, body_color, neck_start, neck_end, int(s_w*0.08))
    
    # Detailed head with proper deer shape
    head_x, head_y = neck_end[0], neck_end[1]
    head_rect = pygame.Rect(head_x - s_w*0.08, head_y - s_h*0.06, s_w*0.22, s_h*0.18)
    pygame.draw.ellipse(sprite_surface, body_color, head_rect)
    
    # Head shading
    head_shadow = pygame.Rect(head_rect.left + 1, head_rect.top + 1, head_rect.width - 2, head_rect.height - 2)
    pygame.draw.ellipse(sprite_surface, darker_brown, head_shadow)
    
    # Elongated snout/muzzle
    muzzle_rect = pygame.Rect(head_x - s_w*0.15, head_y + s_h*0.02, s_w*0.1, s_h*0.08)
    pygame.draw.ellipse(sprite_surface, body_color, muzzle_rect)
    pygame.draw.ellipse(sprite_surface, darker_brown, (muzzle_rect.left + 1, muzzle_rect.top + 1, 
                                                      muzzle_rect.width - 2, muzzle_rect.height - 2))
    
    # Realistic deer ears
    ear_outer_left = [
        (head_x - s_w*0.02, head_y - s_h*0.04),
        (head_x - s_w*0.08, head_y - s_h*0.12),
        (head_x + s_w*0.02, head_y - s_h*0.08)
    ]
    ear_outer_right = [
        (head_x + s_w*0.08, head_y - s_h*0.04),
        (head_x + s_w*0.14, head_y - s_h*0.12),
        (head_x + s_w*0.04, head_y - s_h*0.08)
    ]
    ear_inner_left = [
        (head_x - s_w*0.01, head_y - s_h*0.05),
        (head_x - s_w*0.06, head_y - s_h*0.1),
        (head_x + s_w*0.01, head_y - s_h*0.075)
    ]
    ear_inner_right = [
        (head_x + s_w*0.07, head_y - s_h*0.05),
        (head_x + s_w*0.12, head_y - s_h*0.1),
        (head_x + s_w*0.05, head_y - s_h*0.075)
    ]
    
    pygame.draw.polygon(sprite_surface, body_color, ear_outer_left)
    pygame.draw.polygon(sprite_surface, body_color, ear_outer_right)
    pygame.draw.polygon(sprite_surface, inner_ear, ear_inner_left)
    pygame.draw.polygon(sprite_surface, inner_ear, ear_inner_right)
    
    # Magnificent branched antlers (male deer)
    antler_base_left = (head_x + s_w*0.02, head_y - s_h*0.06)
    antler_base_right = (head_x + s_w*0.1, head_y - s_h*0.06)
    
    # Left antler main beam
    main_beam_left = (antler_base_left[0] - s_w*0.08, antler_base_left[1] - s_h*0.2)
    pygame.draw.line(sprite_surface, antler_color, antler_base_left, main_beam_left, 3)
    
    # Left antler tines (branches)
    tine_positions_left = [
        # (from_point, to_point, thickness)
        ((antler_base_left[0] - s_w*0.03, antler_base_left[1] - s_h*0.08), 
         (antler_base_left[0] - s_w*0.12, antler_base_left[1] - s_h*0.15), 2),
        ((antler_base_left[0] - s_w*0.05, antler_base_left[1] - s_h*0.13), 
         (antler_base_left[0] - s_w*0.15, antler_base_left[1] - s_h*0.22), 2),
        ((antler_base_left[0] - s_w*0.07, antler_base_left[1] - s_h*0.18), 
         (antler_base_left[0] - s_w*0.18, antler_base_left[1] - s_h*0.28), 1)
    ]
    
    for start_pos, end_pos, thickness in tine_positions_left:
        pygame.draw.line(sprite_surface, antler_color, start_pos, end_pos, thickness)
        # Antler tips
        pygame.draw.circle(sprite_surface, antler_tips, (int(end_pos[0]), int(end_pos[1])), 1)
    
    # Right antler main beam
    main_beam_right = (antler_base_right[0] + s_w*0.08, antler_base_right[1] - s_h*0.18)
    pygame.draw.line(sprite_surface, antler_color, antler_base_right, main_beam_right, 3)
    
    # Right antler tines
    tine_positions_right = [
        ((antler_base_right[0] + s_w*0.03, antler_base_right[1] - s_h*0.08), 
         (antler_base_right[0] + s_w*0.12, antler_base_right[1] - s_h*0.15), 2),
        ((antler_base_right[0] + s_w*0.05, antler_base_right[1] - s_h*0.13), 
         (antler_base_right[0] + s_w*0.15, antler_base_right[1] - s_h*0.22), 2),
        ((antler_base_right[0] + s_w*0.07, antler_base_right[1] - s_h*0.16), 
         (antler_base_right[0] + s_w*0.18, antler_base_right[1] - s_h*0.25), 1)
    ]
    
    for start_pos, end_pos, thickness in tine_positions_right:
        pygame.draw.line(sprite_surface, antler_color, start_pos, end_pos, thickness)
        pygame.draw.circle(sprite_surface, antler_tips, (int(end_pos[0]), int(end_pos[1])), 1)
    
    # Alert, intelligent eyes
    eye_left = (int(head_x + s_w*0.02), int(head_y - s_h*0.02))
    eye_right = (int(head_x + s_w*0.08), int(head_y - s_h*0.02))
    
    # Eye whites and pupils
    pygame.draw.ellipse(sprite_surface, (255, 255, 255), (eye_left[0] - 2, eye_left[1] - 1, 4, 3))
    pygame.draw.ellipse(sprite_surface, (255, 255, 255), (eye_right[0] - 2, eye_right[1] - 1, 4, 3))
    pygame.draw.circle(sprite_surface, eye_color, eye_left, 2)
    pygame.draw.circle(sprite_surface, eye_color, eye_right, 2)
    pygame.draw.circle(sprite_surface, eye_highlight, (eye_left[0] - 1, eye_left[1] - 1), 1)
    pygame.draw.circle(sprite_surface, eye_highlight, (eye_right[0] - 1, eye_right[1] - 1), 1)
    
    # Detailed nose
    nose_center = (int(muzzle_rect.left + s_w*0.02), int(muzzle_rect.top + s_h*0.02))
    pygame.draw.ellipse(sprite_surface, nose_color, (nose_center[0] - 2, nose_center[1] - 1, 4, 3))
    # Nostrils
    pygame.draw.circle(sprite_surface, (40, 30, 20), (nose_center[0] - 1, nose_center[1]), 1)
    pygame.draw.circle(sprite_surface, (40, 30, 20), (nose_center[0] + 1, nose_center[1]), 1)
    
    # Long, elegant legs with proper deer anatomy
    leg_thickness = 3
    leg_data = [
        # (x_offset, forward_stance)
        (0.1, -s_w*0.01),   # Front left
        (0.25, s_w*0.01),   # Front right
        (0.65, -s_w*0.02),  # Back left  
        (0.85, s_w*0.02)    # Back right
    ]
    
    for leg_offset, stance_adj in leg_data:
        leg_x = body_rect.left + body_rect.width * leg_offset
        leg_top = body_rect.bottom - s_h*0.02
        # Upper leg (muscular thigh)
        knee_y = leg_top + s_h*0.15
        # Lower leg (shin)
        leg_bottom = knee_y + s_h*0.25
        
        # Draw upper leg (thicker)
        pygame.draw.line(sprite_surface, body_color, (leg_x, leg_top), (leg_x + stance_adj*0.5, knee_y), leg_thickness + 1)
        
        # Draw lower leg (thinner, more angled)
        pygame.draw.line(sprite_surface, body_color, (leg_x + stance_adj*0.5, knee_y), (leg_x + stance_adj, leg_bottom), leg_thickness)
        
        # Knee joint detail
        pygame.draw.circle(sprite_surface, darker_brown, (int(leg_x + stance_adj*0.5), int(knee_y)), 2)
        
        # Hoof (cloven)
        hoof_center = (leg_x + stance_adj, leg_bottom)
        pygame.draw.ellipse(sprite_surface, hoof_color, (hoof_center[0] - s_w*0.015, hoof_center[1] - s_h*0.015, s_w*0.03, s_h*0.03))
        # Cloven split
        pygame.draw.line(sprite_surface, (40, 30, 20), 
                        (hoof_center[0], hoof_center[1] - s_h*0.01),
                        (hoof_center[0], hoof_center[1] + s_h*0.01), 1)
    
    # Distinctive white-tipped tail
    tail_base = (body_rect.right - s_w*0.01, body_rect.centery + s_h*0.08)
    tail_end = (tail_base[0] + s_w*0.08, tail_base[1] + s_h*0.1)
    
    # Tail body (brown on top)
    pygame.draw.line(sprite_surface, body_color, tail_base, tail_end, 3)
    # White underside
    tail_white_end = (tail_end[0] - s_w*0.01, tail_end[1] + s_h*0.02)
    pygame.draw.line(sprite_surface, tail_tip, (tail_base[0], tail_base[1] + s_h*0.01), tail_white_end, 2)
    
    return sprite_surface


def get_pig_sprite(size: Tuple[int, int] = (40, 32)) -> pygame.Surface:
    """Generate a detailed Pig sprite procedurally."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size
    
    # Enhanced color palette
    body_color = (255, 182, 193)    # Light pink
    darker_pink = (230, 160, 175)   # Darker pink for shadows
    snout_color = (255, 105, 140)   # Hot pink snout
    snout_highlight = (255, 200, 220) # Light pink highlight
    eye_color = (60, 40, 40)        # Dark brown eyes
    eye_highlight = (255, 255, 255) # White highlight
    ear_inner = (255, 150, 170)     # Inner ear pink
    leg_color = (240, 150, 165)     # Slightly darker leg pink
    hoof_color = (80, 60, 60)       # Dark gray hooves
    tail_color = (250, 170, 180)    # Tail pink
    nostril_color = (200, 100, 120) # Dark nostril pink
    
    # Round, plump body with realistic pig proportions
    body_rect = pygame.Rect(s_w*0.12, s_h*0.25, s_w*0.65, s_h*0.45)
    pygame.draw.ellipse(sprite_surface, body_color, body_rect)
    
    # Body shading for rounded appearance
    shadow_rect = pygame.Rect(body_rect.left + 1, body_rect.top + 2, body_rect.width - 2, body_rect.height - 3)
    pygame.draw.ellipse(sprite_surface, darker_pink, shadow_rect)
    
    # Belly area (more pronounced)
    belly_rect = pygame.Rect(body_rect.left + s_w*0.02, body_rect.centery, body_rect.width - s_w*0.04, body_rect.height*0.6)
    pygame.draw.ellipse(sprite_surface, body_color, belly_rect)
    
    # Head with proper pig shape
    head_x, head_y = body_rect.left - s_w*0.08, body_rect.top + s_h*0.08
    head_rect = pygame.Rect(head_x, head_y, s_w*0.28, s_h*0.22)
    pygame.draw.ellipse(sprite_surface, body_color, head_rect)
    
    # Head shading
    head_shadow = pygame.Rect(head_x + 1, head_y + 1, head_rect.width - 2, head_rect.height - 2)
    pygame.draw.ellipse(sprite_surface, darker_pink, head_shadow)
    
    # Distinctive pig snout (prominent and wide)
    snout_rect = pygame.Rect(head_x - s_w*0.1, head_y + s_h*0.06, s_w*0.15, s_h*0.12)
    pygame.draw.ellipse(sprite_surface, snout_color, snout_rect)
    
    # Snout highlight and shading
    snout_highlight_rect = pygame.Rect(snout_rect.left + 1, snout_rect.top + 1, snout_rect.width - 2, snout_rect.height*0.6)
    pygame.draw.ellipse(sprite_surface, snout_highlight, snout_highlight_rect)
    
    # Snout rim (darker edge)
    pygame.draw.ellipse(sprite_surface, darker_pink, snout_rect, 1)
    
    # Large nostrils (characteristic pig feature)
    nostril_left = (int(snout_rect.centerx - s_w*0.025), int(snout_rect.centery + s_h*0.01))
    nostril_right = (int(snout_rect.centerx + s_w*0.025), int(snout_rect.centery + s_h*0.01))
    
    pygame.draw.ellipse(sprite_surface, nostril_color, (nostril_left[0] - 2, nostril_left[1] - 1, 4, 3))
    pygame.draw.ellipse(sprite_surface, nostril_color, (nostril_right[0] - 2, nostril_right[1] - 1, 4, 3))
    
    # Nostril depth
    pygame.draw.circle(sprite_surface, (150, 80, 100), nostril_left, 1)
    pygame.draw.circle(sprite_surface, (150, 80, 100), nostril_right, 1)
    
    # Small, intelligent eyes
    eye_left = (int(head_x + s_w*0.06), int(head_y + s_h*0.04))
    eye_right = (int(head_x + s_w*0.18), int(head_y + s_h*0.04))
    
    # Eye whites and pupils
    pygame.draw.ellipse(sprite_surface, (255, 255, 255), (eye_left[0] - 2, eye_left[1] - 1, 4, 3))
    pygame.draw.ellipse(sprite_surface, (255, 255, 255), (eye_right[0] - 2, eye_right[1] - 1, 4, 3))
    pygame.draw.circle(sprite_surface, eye_color, eye_left, 2)
    pygame.draw.circle(sprite_surface, eye_color, eye_right, 2)
    pygame.draw.circle(sprite_surface, eye_highlight, (eye_left[0] - 1, eye_left[1] - 1), 1)
    pygame.draw.circle(sprite_surface, eye_highlight, (eye_right[0] - 1, eye_right[1] - 1), 1)
    
    # Triangular pig ears (characteristic pointed shape)
    ear_left_outer = [
        (head_x + s_w*0.04, head_y + s_h*0.01),
        (head_x + s_w*0.01, head_y - s_h*0.08),
        (head_x + s_w*0.12, head_y + s_h*0.02)
    ]
    ear_right_outer = [
        (head_x + s_w*0.16, head_y + s_h*0.01),
        (head_x + s_w*0.27, head_y - s_h*0.08),
        (head_x + s_w*0.15, head_y + s_h*0.02)
    ]
    ear_left_inner = [
        (head_x + s_w*0.05, head_y + s_h*0.005),
        (head_x + s_w*0.03, head_y - s_h*0.06),
        (head_x + s_w*0.1, head_y + s_h*0.015)
    ]
    ear_right_inner = [
        (head_x + s_w*0.17, head_y + s_h*0.005),
        (head_x + s_w*0.25, head_y - s_h*0.06),
        (head_x + s_w*0.16, head_y + s_h*0.015)
    ]
    
    pygame.draw.polygon(sprite_surface, body_color, ear_left_outer)
    pygame.draw.polygon(sprite_surface, body_color, ear_right_outer)
    pygame.draw.polygon(sprite_surface, ear_inner, ear_left_inner)
    pygame.draw.polygon(sprite_surface, ear_inner, ear_right_inner)
    
    # Sturdy pig legs with realistic proportions
    leg_thickness = 3
    leg_data = [
        # (x_offset, stance_adjustment)
        (0.15, -s_w*0.005),  # Front left
        (0.35, s_w*0.005),   # Front right
        (0.6, -s_w*0.01),    # Back left
        (0.8, s_w*0.01)      # Back right
    ]
    
    for leg_offset, stance_adj in leg_data:
        leg_x = body_rect.left + body_rect.width * leg_offset
        leg_top = body_rect.bottom - s_h*0.02
        leg_bottom = leg_top + s_h*0.22
        
        # Draw leg with slight stance variation
        pygame.draw.line(sprite_surface, leg_color, (leg_x, leg_top), (leg_x + stance_adj, leg_bottom), leg_thickness)
        
        # Pig hoof (cloven)
        hoof_center = (leg_x + stance_adj, leg_bottom)
        hoof_rect = pygame.Rect(hoof_center[0] - s_w*0.02, hoof_center[1] - s_h*0.015, s_w*0.04, s_h*0.03)
        pygame.draw.ellipse(sprite_surface, hoof_color, hoof_rect)
        
        # Cloven split line
        pygame.draw.line(sprite_surface, (60, 45, 45), 
                        (hoof_center[0], hoof_rect.top + 1),
                        (hoof_center[0], hoof_rect.bottom - 1), 1)
    
    # Characteristic curly pig tail
    tail_start = (body_rect.right - s_w*0.01, body_rect.centery - s_h*0.02)
    
    # Create curly tail using multiple curve segments
    tail_segments = [
        (tail_start[0] + s_w*0.04, tail_start[1] - s_h*0.04),  # Up
        (tail_start[0] + s_w*0.08, tail_start[1] - s_h*0.02),  # Right
        (tail_start[0] + s_w*0.06, tail_start[1] + s_h*0.02),  # Down
        (tail_start[0] + s_w*0.04, tail_start[1] + s_h*0.04),  # Left
        (tail_start[0] + s_w*0.07, tail_start[1] + s_h*0.01)   # Final curve
    ]
    
    # Draw tail segments
    current_pos = tail_start
    for segment_end in tail_segments:
        pygame.draw.line(sprite_surface, tail_color, current_pos, segment_end, 2)
        current_pos = segment_end
    
    # Add small tail curl at the end
    pygame.draw.circle(sprite_surface, tail_color, (int(current_pos[0]), int(current_pos[1])), 1)
    
    # Small mouth detail
    mouth_center = (snout_rect.centerx, snout_rect.bottom - s_h*0.02)
    pygame.draw.line(sprite_surface, darker_pink, 
                    (mouth_center[0] - s_w*0.01, mouth_center[1]),
                    (mouth_center[0] + s_w*0.01, mouth_center[1]), 1)
    
    return sprite_surface


def get_dog_sprite(size: Tuple[int, int] = (36, 28)) -> pygame.Surface:
    """Generate a detailed Stray Dog sprite procedurally."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Enhanced color palette  
    body_color = (139, 69, 19)      # Rich brown base
    darker_brown = (100, 50, 15)    # Darker brown for shadows
    lighter_brown = (170, 100, 50)  # Lighter brown highlights
    ear_color = (120, 60, 20)       # Dark brown ears
    nose_color = (40, 25, 15)       # Dark brown/black nose
    eye_color = (60, 40, 25)        # Dark brown eyes
    eye_highlight = (255, 255, 255) # White eye highlight
    tongue_color = (255, 120, 140)  # Pink tongue
    tongue_highlight = (255, 160, 180) # Light pink highlight
    teeth_color = (255, 255, 240)   # Off-white teeth
    collar_color = (139, 0, 0)      # Dark red collar
    collar_metal = (180, 180, 180)  # Silver collar buckle
    paw_pad = (80, 50, 30)          # Dark paw pads

    # Muscular dog body with realistic proportions
    body_rect = pygame.Rect(s_w*0.16, s_h*0.32, s_w*0.55, s_h*0.35)
    pygame.draw.ellipse(sprite_surface, body_color, body_rect)
    
    # Body muscle definition
    muscle_rect = pygame.Rect(body_rect.left + 1, body_rect.top + 1, body_rect.width - 2, body_rect.height*0.8)
    pygame.draw.ellipse(sprite_surface, darker_brown, muscle_rect)
    
    # Chest area
    chest_rect = pygame.Rect(body_rect.left - s_w*0.02, body_rect.top + s_h*0.02, s_w*0.2, s_h*0.25)
    pygame.draw.ellipse(sprite_surface, body_color, chest_rect)
    
    # Detailed head with proper dog proportions
    head_x, head_y = body_rect.left - s_w*0.12, body_rect.top + s_h*0.02
    head_rect = pygame.Rect(head_x, head_y, s_w*0.28, s_h*0.22)
    pygame.draw.ellipse(sprite_surface, body_color, head_rect)
    
    # Head shading
    head_shadow = pygame.Rect(head_x + 1, head_y + 1, head_rect.width - 2, head_rect.height - 2)
    pygame.draw.ellipse(sprite_surface, darker_brown, head_shadow)
    
    # Dog snout/muzzle (elongated)
    snout_rect = pygame.Rect(head_x - s_w*0.08, head_y + s_h*0.08, s_w*0.12, s_h*0.1)
    pygame.draw.ellipse(sprite_surface, body_color, snout_rect)
    pygame.draw.ellipse(sprite_surface, darker_brown, (snout_rect.left + 1, snout_rect.top + 1, 
                                                      snout_rect.width - 2, snout_rect.height - 2))
    
    # Dog nose (wet and detailed)
    nose_center = (int(snout_rect.left + s_w*0.02), int(snout_rect.top + s_h*0.025))
    nose_rect = pygame.Rect(nose_center[0] - 2, nose_center[1] - 2, 5, 4)
    pygame.draw.ellipse(sprite_surface, nose_color, nose_rect)
    
    # Nose highlight (wet look)
    pygame.draw.circle(sprite_surface, (100, 80, 60), (nose_center[0] - 1, nose_center[1] - 1), 1)
    
    # Nostrils
    pygame.draw.circle(sprite_surface, (20, 15, 10), (nose_center[0] - 1, nose_center[1]), 1)
    pygame.draw.circle(sprite_surface, (20, 15, 10), (nose_center[0] + 1, nose_center[1]), 1)
    
    # Open mouth with panting tongue
    mouth_rect = pygame.Rect(snout_rect.left, snout_rect.bottom - s_h*0.02, snout_rect.width, s_h*0.04)
    pygame.draw.ellipse(sprite_surface, (40, 25, 15), mouth_rect)
    
    # Visible teeth
    tooth_positions = [
        (mouth_rect.left + s_w*0.02, mouth_rect.top + 1),
        (mouth_rect.left + s_w*0.04, mouth_rect.top + 1),
        (mouth_rect.right - s_w*0.02, mouth_rect.top + 1)
    ]
    for tooth_x, tooth_y in tooth_positions:
        pygame.draw.line(sprite_surface, teeth_color, (tooth_x, tooth_y), (tooth_x, tooth_y + 2), 1)
    
    # Hanging tongue (characteristic of friendly/tired dog)
    tongue_points = [
        (snout_rect.centerx - s_w*0.01, mouth_rect.bottom),
        (snout_rect.centerx - s_w*0.025, mouth_rect.bottom + s_h*0.08),
        (snout_rect.centerx + s_w*0.025, mouth_rect.bottom + s_h*0.06),
        (snout_rect.centerx + s_w*0.01, mouth_rect.bottom)
    ]
    pygame.draw.polygon(sprite_surface, tongue_color, tongue_points)
    
    # Tongue highlight
    tongue_highlight_points = [
        (snout_rect.centerx - s_w*0.005, mouth_rect.bottom + s_h*0.01),
        (snout_rect.centerx - s_w*0.015, mouth_rect.bottom + s_h*0.05),
        (snout_rect.centerx + s_w*0.005, mouth_rect.bottom + s_h*0.03)
    ]
    pygame.draw.polygon(sprite_surface, tongue_highlight, tongue_highlight_points)
    
    # Expressive dog eyes
    eye_left = (int(head_x + s_w*0.08), int(head_y + s_h*0.05))
    eye_right = (int(head_x + s_w*0.18), int(head_y + s_h*0.05))
    
    # Eye whites and pupils
    pygame.draw.ellipse(sprite_surface, (255, 255, 255), (eye_left[0] - 3, eye_left[1] - 2, 6, 4))
    pygame.draw.ellipse(sprite_surface, (255, 255, 255), (eye_right[0] - 3, eye_right[1] - 2, 6, 4))
    pygame.draw.circle(sprite_surface, eye_color, eye_left, 2)
    pygame.draw.circle(sprite_surface, eye_color, eye_right, 2)
    pygame.draw.circle(sprite_surface, eye_highlight, (eye_left[0] - 1, eye_left[1] - 1), 1)
    pygame.draw.circle(sprite_surface, eye_highlight, (eye_right[0] - 1, eye_right[1] - 1), 1)
    
    # Floppy dog ears (hanging down)
    ear_left_outer = [
        (head_x + s_w*0.02, head_y + s_h*0.01),
        (head_x - s_w*0.02, head_y - s_h*0.02),
        (head_x - s_w*0.01, head_y + s_h*0.12),
        (head_x + s_w*0.06, head_y + s_h*0.08)
    ]
    ear_right_outer = [
        (head_x + s_w*0.22, head_y + s_h*0.01),
        (head_x + s_w*0.26, head_y - s_h*0.02),
        (head_x + s_w*0.25, head_y + s_h*0.12),
        (head_x + s_w*0.18, head_y + s_h*0.08)
    ]
    
    pygame.draw.polygon(sprite_surface, ear_color, ear_left_outer)
    pygame.draw.polygon(sprite_surface, ear_color, ear_right_outer)
    
    # Ear inner details
    ear_left_inner = [
        (head_x + s_w*0.025, head_y + s_h*0.02),
        (head_x + s_w*0.005, head_y + s_h*0.01),
        (head_x + s_w*0.01, head_y + s_h*0.09),
        (head_x + s_w*0.04, head_y + s_h*0.07)
    ]
    pygame.draw.polygon(sprite_surface, darker_brown, ear_left_inner)
    
    # Simple collar
    collar_rect = pygame.Rect(head_x + s_w*0.05, head_y + s_h*0.15, s_w*0.18, s_h*0.04)
    pygame.draw.rect(sprite_surface, collar_color, collar_rect)
    
    # Collar buckle
    buckle_rect = pygame.Rect(collar_rect.right - s_w*0.03, collar_rect.centery - s_h*0.015, s_w*0.025, s_h*0.03)
    pygame.draw.rect(sprite_surface, collar_metal, buckle_rect)
    
    # Sturdy legs with realistic dog anatomy
    leg_thickness = 3
    leg_data = [
        # (x_offset, stance_adjustment)
        (0.1, -s_w*0.005),   # Front left
        (0.3, s_w*0.005),    # Front right
        (0.6, -s_w*0.01),    # Back left
        (0.8, s_w*0.01)      # Back right
    ]
    
    for leg_offset, stance_adj in leg_data:
        leg_x = body_rect.left + body_rect.width * leg_offset
        leg_top = body_rect.bottom - s_h*0.02
        leg_bottom = leg_top + s_h*0.25
        
        # Draw leg with muscle definition
        pygame.draw.line(sprite_surface, body_color, (leg_x, leg_top), (leg_x + stance_adj, leg_bottom), leg_thickness)
        
        # Paw detail
        paw_center = (leg_x + stance_adj, leg_bottom)
        pygame.draw.circle(sprite_surface, body_color, paw_center, 3)
        
        # Paw pads
        pygame.draw.circle(sprite_surface, paw_pad, paw_center, 2)
        
        # Claws
        for claw_angle in [-0.3, 0, 0.3]:
            claw_end = (paw_center[0] + s_w*0.015*claw_angle, paw_center[1] + s_h*0.02)
            pygame.draw.line(sprite_surface, (120, 100, 80), paw_center, claw_end, 1)
    
    # Wagging tail (curved upward - happy dog)
    tail_start = (body_rect.right - s_w*0.01, body_rect.centery)
    tail_mid = (tail_start[0] + s_w*0.08, tail_start[1] - s_h*0.08)
    tail_end = (tail_mid[0] + s_w*0.06, tail_mid[1] - s_h*0.02)
    
    # Tail body (thick and bushy)
    pygame.draw.line(sprite_surface, body_color, tail_start, tail_mid, 4)
    pygame.draw.line(sprite_surface, body_color, tail_mid, tail_end, 3)
    
    # Tail tip
    pygame.draw.circle(sprite_surface, lighter_brown, (int(tail_end[0]), int(tail_end[1])), 2)
    
    return sprite_surface


def get_ice_wolf_sprite(size: Tuple[int, int] = (48, 36)) -> pygame.Surface:
    """Generate a detailed Ice Wolf sprite procedurally."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Enhanced color palette
    body_color = (200, 220, 255)    # Icy blue-white base
    fur_primary = (180, 200, 240)   # Primary fur color
    fur_shadow = (160, 180, 220)    # Darker fur for shadows
    underbelly = (230, 240, 255)    # Light underbelly
    eye_color = (100, 150, 255)     # Piercing ice blue eyes
    eye_glow = (150, 200, 255)      # Eye glow effect
    nose_color = (50, 50, 80)       # Dark blue-gray nose
    frost_color = (240, 248, 255)   # Frost effects
    ice_crystal = (200, 230, 255)   # Ice crystal details
    breath_color = (220, 235, 255)  # Visible breath
    claw_color = (180, 190, 210)    # Ice-colored claws
    inner_ear = (200, 210, 230)     # Inner ear color
    
    # Muscular, predatory body (arctic wolf proportions)
    body_rect = pygame.Rect(s_w*0.18, s_w*0.32, s_w*0.52, s_h*0.32)
    pygame.draw.ellipse(sprite_surface, body_color, body_rect)
    
    # Body muscle definition and thick winter coat
    muscle_rect = pygame.Rect(body_rect.left + s_w*0.02, body_rect.top + s_h*0.02, 
                             body_rect.width - s_w*0.04, body_rect.height*0.7)
    pygame.draw.ellipse(sprite_surface, fur_primary, muscle_rect)
    
    # Thick underbelly fur
    belly_rect = pygame.Rect(body_rect.left + s_w*0.04, body_rect.centery + s_h*0.02, 
                            body_rect.width - s_w*0.08, body_rect.height*0.4)
    pygame.draw.ellipse(sprite_surface, underbelly, belly_rect)
    
    # Detailed head with arctic wolf features
    head_x, head_y = body_rect.left - s_w*0.1, body_rect.top - s_h*0.05
    head_rect = pygame.Rect(head_x, head_y, s_w*0.32, s_h*0.28)
    pygame.draw.ellipse(sprite_surface, body_color, head_rect)
    
    # Head fur detail and shading
    head_fur = pygame.Rect(head_rect.left + s_w*0.01, head_rect.top + s_h*0.01, 
                          head_rect.width - s_w*0.02, head_rect.height - s_h*0.02)
    pygame.draw.ellipse(sprite_surface, fur_primary, head_fur)
    
    # Elongated snout (arctic wolf characteristic)
    snout_x, snout_y = head_x - s_w*0.08, head_y + s_h*0.1
    snout_width, snout_height = s_w*0.14, s_h*0.12
    snout_rect = pygame.Rect(snout_x, snout_y, snout_width, snout_height)
    pygame.draw.ellipse(sprite_surface, body_color, snout_rect)
    
    # Snout fur and highlighting
    snout_highlight = pygame.Rect(snout_x + s_w*0.01, snout_y + s_h*0.01, 
                                 snout_width - s_w*0.02, snout_height*0.6)
    pygame.draw.ellipse(sprite_surface, fur_primary, snout_highlight)
    
    # Prominent black nose with frost around it
    nose_center = (int(snout_x - s_w*0.02), int(snout_y + snout_height*0.4))
    pygame.draw.ellipse(sprite_surface, nose_color, 
                       (nose_center[0] - s_w*0.02, nose_center[1] - s_h*0.015, 
                        s_w*0.04, s_h*0.03))
    
    # Frost around nose
    pygame.draw.circle(sprite_surface, frost_color, nose_center, 3)
    pygame.draw.circle(sprite_surface, ice_crystal, nose_center, 2)
    
    # Nostrils with visible breath
    pygame.draw.circle(sprite_surface, (30, 30, 60), 
                      (nose_center[0] - s_w*0.008, nose_center[1]), 1)
    pygame.draw.circle(sprite_surface, (30, 30, 60), 
                      (nose_center[0] + s_w*0.008, nose_center[1]), 1)
    
    # Visible breath cloud
    breath_points = [
        (nose_center[0] - s_w*0.04, nose_center[1] - s_h*0.02),
        (nose_center[0] - s_w*0.08, nose_center[1] - s_h*0.04),
        (nose_center[0] - s_w*0.06, nose_center[1] + s_h*0.01),
        (nose_center[0] - s_w*0.02, nose_center[1] + s_h*0.02)
    ]
    pygame.draw.polygon(sprite_surface, breath_color, breath_points)
    
    # Intense, predatory eyes with ice-blue glow
    eye_left = (int(head_x + s_w*0.08), int(head_y + s_h*0.08))
    eye_right = (int(head_x + s_w*0.18), int(head_y + s_h*0.07))
    
    # Eye glow effects for mystical ice wolf appearance
    pygame.draw.circle(sprite_surface, eye_glow, eye_left, 5)
    pygame.draw.circle(sprite_surface, eye_color, eye_left, 4)
    pygame.draw.circle(sprite_surface, (150, 200, 255), eye_left, 3)
    pygame.draw.circle(sprite_surface, (200, 230, 255), eye_left, 2)
    pygame.draw.circle(sprite_surface, (255, 255, 255), eye_left, 1)
    
    pygame.draw.circle(sprite_surface, eye_glow, eye_right, 4)
    pygame.draw.circle(sprite_surface, eye_color, eye_right, 3)
    pygame.draw.circle(sprite_surface, (200, 230, 255), eye_right, 2)
    pygame.draw.circle(sprite_surface, (255, 255, 255), eye_right, 1)
    
    # Thick, pointed ears (adapted for cold)
    ear_left_outer = [
        (head_x + s_w*0.05, head_y + s_h*0.02),
        (head_x + s_w*0.02, head_y - s_h*0.1),
        (head_x + s_w*0.12, head_y + s_h*0.02)
    ]
    ear_left_inner = [
        (head_x + s_w*0.06, head_y + s_h*0.02),
        (head_x + s_w*0.04, head_y - s_h*0.07),
        (head_x + s_w*0.1, head_y + s_h*0.02)
    ]
    
    ear_right_outer = [
        (head_x + s_w*0.18, head_y + s_h*0.02),
        (head_x + s_w*0.15, head_y - s_h*0.08),
        (head_x + s_w*0.25, head_y + s_h*0.02)
    ]
    ear_right_inner = [
        (head_x + s_w*0.19, head_y + s_h*0.02),
        (head_x + s_w*0.17, head_y - s_h*0.06),
        (head_x + s_w*0.23, head_y + s_h*0.02)
    ]
    
    pygame.draw.polygon(sprite_surface, fur_shadow, ear_left_outer)
    pygame.draw.polygon(sprite_surface, inner_ear, ear_left_inner)
    pygame.draw.polygon(sprite_surface, fur_shadow, ear_right_outer)
    pygame.draw.polygon(sprite_surface, inner_ear, ear_right_inner)
    
    # Frost patterns on fur (magical ice wolf markings)
    frost_markings = [
        # Shoulder frost stripe
        (body_rect.left + s_w*0.05, body_rect.top + s_h*0.03, s_w*0.08, s_h*0.04),
        # Back ice pattern
        (body_rect.centerx - s_w*0.03, body_rect.top, s_w*0.06, body_rect.height*0.3),
        # Hip frost marking
        (body_rect.right - s_w*0.12, body_rect.centery - s_h*0.02, s_w*0.1, s_h*0.04),
        # Head frost stripe
        (head_x + s_w*0.08, head_y + s_h*0.15, s_w*0.12, s_h*0.03)
    ]
    
    for mark_x, mark_y, mark_w, mark_h in frost_markings:
        pygame.draw.ellipse(sprite_surface, frost_color, (mark_x, mark_y, mark_w, mark_h))
        # Add ice crystal details
        pygame.draw.ellipse(sprite_surface, ice_crystal, 
                           (mark_x + mark_w*0.2, mark_y + mark_h*0.2, 
                            mark_w*0.6, mark_h*0.6))
    
    # Ice crystal formations on body
    crystal_positions = [
        (body_rect.centerx - s_w*0.03, body_rect.top + s_h*0.06),
        (body_rect.right - s_w*0.08, body_rect.centery),
        (head_x + s_w*0.12, head_y + s_h*0.18)
    ]
    
    for crystal_x, crystal_y in crystal_positions:
        # Draw crystalline formations
        crystal_points = [
            (crystal_x, crystal_y - s_h*0.02),
            (crystal_x + s_w*0.015, crystal_y),
            (crystal_x, crystal_y + s_h*0.02),
            (crystal_x - s_w*0.015, crystal_y)
        ]
        pygame.draw.polygon(sprite_surface, ice_crystal, crystal_points)
        pygame.draw.polygon(sprite_surface, frost_color, [
            (crystal_x, crystal_y - s_h*0.015),
            (crystal_x + s_w*0.01, crystal_y),
            (crystal_x, crystal_y + s_h*0.015),
            (crystal_x - s_w*0.01, crystal_y)
        ])
    
    # Powerful legs adapted for snow travel
    leg_width = int(s_w*0.05)
    leg_positions = [
        (body_rect.left + s_w*0.08, 0.1, -s_w*0.01),  # Front left
        (body_rect.left + s_w*0.18, 0.3, s_w*0.01),   # Front right
        (body_rect.right - s_w*0.18, 0.6, -s_w*0.015), # Back left
        (body_rect.right - s_w*0.08, 0.8, s_w*0.015)   # Back right
    ]
    
    for leg_x, leg_offset, stance_adj in leg_positions:
        leg_top = body_rect.bottom - s_h*0.02
        leg_mid = leg_top + s_h*0.12
        leg_bottom = leg_top + s_h*0.22
        
        # Upper leg (thick, muscular)
        pygame.draw.line(sprite_surface, body_color, 
                        (leg_x, leg_top), (leg_x + stance_adj, leg_mid), leg_width)
        # Lower leg
        pygame.draw.line(sprite_surface, body_color, 
                        (leg_x + stance_adj, leg_mid), (leg_x + stance_adj, leg_bottom), leg_width - 1)
        
        # Knee joint with fur
        pygame.draw.circle(sprite_surface, fur_shadow, 
                          (int(leg_x + stance_adj), int(leg_mid)), leg_width // 2)
        
        # Large paws adapted for snow
        paw_center = (int(leg_x + stance_adj), int(leg_bottom))
        pygame.draw.ellipse(sprite_surface, fur_primary, 
                           (paw_center[0] - s_w*0.025, paw_center[1] - s_h*0.02, 
                            s_w*0.05, s_h*0.04))
        
        # Ice claws
        for claw_offset in [-s_w*0.015, 0, s_w*0.015]:
            claw_pos = (paw_center[0] + claw_offset, paw_center[1] + s_h*0.015)
            pygame.draw.line(sprite_surface, claw_color, 
                           claw_pos, (claw_pos[0], claw_pos[1] + s_h*0.02), 2)
            # Ice claw highlights
            pygame.draw.line(sprite_surface, ice_crystal, 
                           claw_pos, (claw_pos[0], claw_pos[1] + s_h*0.015), 1)
    
    # Magnificent bushy tail with frost
    tail_base = (body_rect.right - s_w*0.02, body_rect.centery + s_h*0.05)
    tail_mid = (tail_base[0] + s_w*0.12, tail_base[1] + s_h*0.02)
    tail_tip = (tail_mid[0] + s_w*0.08, tail_mid[1] - s_h*0.1)
    
    # Tail body (very thick and bushy)
    pygame.draw.line(sprite_surface, body_color, tail_base, tail_mid, 8)
    pygame.draw.line(sprite_surface, body_color, tail_mid, tail_tip, 7)
    
    # Tail fur layers
    pygame.draw.line(sprite_surface, fur_primary, tail_base, tail_mid, 6)
    pygame.draw.line(sprite_surface, fur_primary, tail_mid, tail_tip, 5)
    
    # Frost on tail tip
    pygame.draw.circle(sprite_surface, frost_color, (int(tail_tip[0]), int(tail_tip[1])), 5)
    pygame.draw.circle(sprite_surface, ice_crystal, (int(tail_tip[0]), int(tail_tip[1])), 3)
    pygame.draw.circle(sprite_surface, (255, 255, 255), (int(tail_tip[0]), int(tail_tip[1])), 1)
    
    return sprite_surface


def get_forest_troll_sprite(size: Tuple[int, int] = (64, 80)) -> pygame.Surface:
    """Generate a detailed Forest Troll sprite procedurally."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Enhanced color palette
    skin_color = (70, 90, 45)       # Dark olive-green skin
    dark_skin = (50, 70, 30)        # Darker shadow green
    bark_skin = (85, 65, 35)        # Bark-like skin patches
    moss_color = (45, 120, 45)      # Vibrant moss green
    moss_dark = (30, 90, 30)        # Darker moss
    eye_color = (255, 40, 40)       # Glowing red eyes
    eye_glow = (255, 100, 100)      # Red glow effect
    tusk_color = (255, 255, 200)    # Yellowed tusks
    claw_color = (140, 100, 60)     # Dark brown claws
    bark_color = (120, 85, 50)      # Rich bark brown
    rock_color = (90, 90, 90)       # Stone-like gray
    lichen_color = (100, 140, 60)   # Yellowish lichen
    scar_color = (120, 50, 50)      # Dark red scars
    
    # Massive, intimidating body (hunched, brutish posture)
    body_rect = pygame.Rect(s_w*0.12, s_h*0.28, s_w*0.65, s_h*0.48)
    pygame.draw.ellipse(sprite_surface, skin_color, body_rect)
    
    # Body shadow and muscle definition
    shadow_rect = pygame.Rect(body_rect.left + 2, body_rect.top + 3, body_rect.width - 4, body_rect.height - 5)
    pygame.draw.ellipse(sprite_surface, dark_skin, shadow_rect)
    
    # Hunched back/shoulders
    hunch_rect = pygame.Rect(body_rect.left + s_w*0.1, body_rect.top - s_h*0.05, body_rect.width*0.6, s_h*0.15)
    pygame.draw.ellipse(sprite_surface, skin_color, hunch_rect)
    pygame.draw.ellipse(sprite_surface, dark_skin, (hunch_rect.left + 1, hunch_rect.top + 2, 
                                                   hunch_rect.width - 2, hunch_rect.height - 3))
    
    # Bark-like skin patches for forest creature appearance
    bark_patches = [
        (body_rect.left + s_w*0.05, body_rect.top + s_h*0.08, s_w*0.15, s_h*0.12),
        (body_rect.centerx + s_w*0.05, body_rect.centery, s_w*0.12, s_h*0.1),
        (body_rect.right - s_w*0.18, body_rect.bottom - s_h*0.15, s_w*0.14, s_h*0.08)
    ]
    
    for patch_x, patch_y, patch_w, patch_h in bark_patches:
        patch_rect = pygame.Rect(patch_x, patch_y, patch_w, patch_h)
        pygame.draw.ellipse(sprite_surface, bark_skin, patch_rect)
        # Bark texture lines
        for i in range(3):
            line_y = patch_y + (i + 1) * patch_h / 4
            pygame.draw.line(sprite_surface, (bark_skin[0] - 20, bark_skin[1] - 20, bark_skin[2] - 10), 
                           (patch_x + 2, line_y), (patch_x + patch_w - 2, line_y), 1)
    
    # Extensive moss coverage
    moss_patches = [
        (body_rect.left + s_w*0.02, body_rect.top + s_h*0.12, s_w*0.18, s_h*0.1),
        (body_rect.centerx - s_w*0.05, body_rect.centery - s_h*0.05, s_w*0.2, s_h*0.12),
        (body_rect.right - s_w*0.15, body_rect.bottom - s_h*0.08, s_w*0.12, s_h*0.06),
        (body_rect.left + s_w*0.2, body_rect.bottom - s_h*0.2, s_w*0.1, s_h*0.08)
    ]
    
    for moss_x, moss_y, moss_w, moss_h in moss_patches:
        # Main moss patches
        moss_rect = pygame.Rect(moss_x, moss_y, moss_w, moss_h)
        pygame.draw.ellipse(sprite_surface, moss_color, moss_rect)
        pygame.draw.ellipse(sprite_surface, moss_dark, (moss_x + 1, moss_y + 1, moss_w - 2, moss_h - 2))
        
        # Moss texture details
        for i in range(int(moss_w / 4)):
            spot_x = moss_x + i * 4 + (i % 2)
            spot_y = moss_y + (i % 3) * 3
            pygame.draw.circle(sprite_surface, lichen_color, (int(spot_x), int(spot_y)), 1)
    
    # Massive, brutish head
    head_x, head_y = body_rect.centerx, body_rect.top - s_h*0.18
    head_radius = int(s_w*0.2)
    pygame.draw.circle(sprite_surface, skin_color, (int(head_x), int(head_y)), head_radius)
    
    # Head shadow and dimension
    pygame.draw.circle(sprite_surface, dark_skin, (int(head_x + 2), int(head_y + 2)), head_radius - 2)
    
    # Prominent, brutish brow ridge
    brow_rect = pygame.Rect(head_x - s_w*0.12, head_y - s_h*0.08, s_w*0.24, s_h*0.06)
    pygame.draw.ellipse(sprite_surface, bark_skin, brow_rect)
    
    # Small, menacing eyes deeply set under brow
    eye_left = (int(head_x - s_w*0.06), int(head_y - s_h*0.04))
    eye_right = (int(head_x + s_w*0.06), int(head_y - s_h*0.04))
    
    # Glowing red eyes with menacing effect
    pygame.draw.circle(sprite_surface, eye_glow, eye_left, 5)
    pygame.draw.circle(sprite_surface, eye_color, eye_left, 4)
    pygame.draw.circle(sprite_surface, (255, 150, 150), eye_left, 2)
    pygame.draw.circle(sprite_surface, (255, 255, 100), eye_left, 1)
    
    pygame.draw.circle(sprite_surface, eye_glow, eye_right, 5)
    pygame.draw.circle(sprite_surface, eye_color, eye_right, 4)
    pygame.draw.circle(sprite_surface, (255, 150, 150), eye_right, 2)
    pygame.draw.circle(sprite_surface, (255, 255, 100), eye_right, 1)
    
    # Large, gaping mouth with visible interior
    mouth_rect = pygame.Rect(head_x - s_w*0.1, head_y + s_h*0.04, s_w*0.2, s_h*0.08)
    pygame.draw.ellipse(sprite_surface, (20, 10, 10), mouth_rect)
    pygame.draw.ellipse(sprite_surface, (40, 20, 20), (mouth_rect.left + 1, mouth_rect.top + 1, 
                                                       mouth_rect.width - 2, mouth_rect.height - 2))
    
    # Massive curved tusks
    tusk_left_points = [
        (head_x - s_w*0.05, head_y + s_h*0.04),
        (head_x - s_w*0.08, head_y + s_h*0.14),
        (head_x - s_w*0.02, head_y + s_h*0.1)
    ]
    tusk_right_points = [
        (head_x + s_w*0.05, head_y + s_h*0.04),
        (head_x + s_w*0.08, head_y + s_h*0.14),
        (head_x + s_w*0.02, head_y + s_h*0.1)
    ]
    
    pygame.draw.polygon(sprite_surface, tusk_color, tusk_left_points)
    pygame.draw.polygon(sprite_surface, tusk_color, tusk_right_points)
    
    # Tusk details and aging
    pygame.draw.line(sprite_surface, (200, 200, 150), tusk_left_points[0], tusk_left_points[1], 2)
    pygame.draw.line(sprite_surface, (200, 200, 150), tusk_right_points[0], tusk_right_points[1], 2)
    
    # Battle scars on face
    scar_positions = [
        ((head_x - s_w*0.02, head_y - s_h*0.02), (head_x + s_w*0.04, head_y + s_h*0.02)),
        ((head_x - s_w*0.08, head_y + s_h*0.01), (head_x - s_w*0.04, head_y + s_h*0.03))
    ]
    
    for scar_start, scar_end in scar_positions:
        pygame.draw.line(sprite_surface, scar_color, scar_start, scar_end, 2)
        pygame.draw.line(sprite_surface, (160, 80, 80), scar_start, scar_end, 1)
    
    # Massive, powerful arms with tree-like proportions
    # Left arm (raised menacingly)
    arm_left_start = (body_rect.left + s_w*0.05, body_rect.top + s_h*0.1)
    arm_left_mid = (arm_left_start[0] - s_w*0.15, arm_left_start[1] + s_h*0.15)
    arm_left_end = (arm_left_mid[0] - s_w*0.08, arm_left_mid[1] - s_h*0.1)
    
    # Upper arm
    pygame.draw.line(sprite_surface, skin_color, arm_left_start, arm_left_mid, int(s_w*0.14))
    # Forearm
    pygame.draw.line(sprite_surface, skin_color, arm_left_mid, arm_left_end, int(s_w*0.12))
    
    # Elbow joint detail
    pygame.draw.circle(sprite_surface, dark_skin, (int(arm_left_mid[0]), int(arm_left_mid[1])), int(s_w*0.04))
    
    # Left hand with massive claws
    hand_left_center = arm_left_end
    pygame.draw.circle(sprite_surface, skin_color, (int(hand_left_center[0]), int(hand_left_center[1])), int(s_w*0.09))
    pygame.draw.circle(sprite_surface, dark_skin, (int(hand_left_center[0] + 1), int(hand_left_center[1] + 1)), int(s_w*0.08))
    
    # Fearsome claws
    claw_angles = [-0.8, -0.3, 0.1, 0.5]
    for i, angle in enumerate(claw_angles):
        claw_length = s_h*0.12 + i*s_h*0.01
        claw_end = (hand_left_center[0] + claw_length*angle*0.7, hand_left_center[1] + claw_length*0.8)
        pygame.draw.line(sprite_surface, claw_color, hand_left_center, claw_end, 3)
        # Claw tips
        pygame.draw.circle(sprite_surface, (180, 140, 80), (int(claw_end[0]), int(claw_end[1])), 1)
    
    # Right arm (at side, more relaxed but still threatening)
    arm_right_start = (body_rect.right - s_w*0.05, body_rect.top + s_h*0.12)
    arm_right_mid = (arm_right_start[0] + s_w*0.12, arm_right_start[1] + s_h*0.2)
    arm_right_end = (arm_right_mid[0] + s_w*0.05, arm_right_mid[1] + s_h*0.15)
    
    pygame.draw.line(sprite_surface, skin_color, arm_right_start, arm_right_mid, int(s_w*0.14))
    pygame.draw.line(sprite_surface, skin_color, arm_right_mid, arm_right_end, int(s_w*0.12))
    
    # Right elbow
    pygame.draw.circle(sprite_surface, dark_skin, (int(arm_right_mid[0]), int(arm_right_mid[1])), int(s_w*0.04))
    
    # Right hand
    hand_right_center = arm_right_end
    pygame.draw.circle(sprite_surface, skin_color, (int(hand_right_center[0]), int(hand_right_center[1])), int(s_w*0.09))
    pygame.draw.circle(sprite_surface, dark_skin, (int(hand_right_center[0] + 1), int(hand_right_center[1] + 1)), int(s_w*0.08))
    
    # Right hand claws
    for i, angle in enumerate([0.2, 0.5, 0.8, 1.1]):
        claw_length = s_h*0.1 + i*s_h*0.008
        claw_end = (hand_right_center[0] + claw_length*0.5, hand_right_center[1] + claw_length*0.9)
        pygame.draw.line(sprite_surface, claw_color, hand_right_center, claw_end, 3)
        pygame.draw.circle(sprite_surface, (180, 140, 80), (int(claw_end[0]), int(claw_end[1])), 1)
    
    # Tree-trunk-like legs with root-like feet
    leg_data = [
        (0.2, -s_w*0.01),   # Left leg
        (0.7, s_w*0.01)     # Right leg
    ]
    
    for leg_offset, stance_adj in leg_data:
        leg_x = body_rect.left + body_rect.width * leg_offset
        leg_top = body_rect.bottom - s_h*0.05
        leg_bottom = leg_top + s_h*0.22
        
        # Massive tree-trunk legs
        pygame.draw.line(sprite_surface, bark_color, (leg_x, leg_top), (leg_x + stance_adj, leg_bottom), int(s_w*0.16))
        
        # Bark texture on legs
        for bark_line in range(3):
            line_y = leg_top + (bark_line + 1) * s_h*0.06
            pygame.draw.line(sprite_surface, (bark_color[0] - 30, bark_color[1] - 20, bark_color[2] - 15), 
                           (leg_x - s_w*0.06, line_y), (leg_x + s_w*0.06, line_y), 2)
        
        # Root-like feet spreading from legs
        foot_center = (leg_x + stance_adj, leg_bottom)
        root_directions = [
            (-s_w*0.08, s_h*0.02),   # Left root
            (0, s_h*0.06),           # Center root
            (s_w*0.08, s_h*0.02),    # Right root
            (-s_w*0.04, s_h*0.08),   # Back-left root
            (s_w*0.04, s_h*0.08)     # Back-right root
        ]
        
        for root_dx, root_dy in root_directions:
            root_end = (foot_center[0] + root_dx, foot_center[1] + root_dy)
            pygame.draw.line(sprite_surface, bark_color, foot_center, root_end, 4)
            # Root tip details
            pygame.draw.circle(sprite_surface, rock_color, (int(root_end[0]), int(root_end[1])), 2)
    
    # Moss growing on head and shoulders
    head_moss_patches = [
        (head_x - s_w*0.08, head_y - s_h*0.12, s_w*0.16, s_h*0.08),
        (body_rect.left + s_w*0.08, body_rect.top - s_h*0.02, s_w*0.12, s_h*0.06)
    ]
    
    for moss_x, moss_y, moss_w, moss_h in head_moss_patches:
        pygame.draw.ellipse(sprite_surface, moss_color, (moss_x, moss_y, moss_w, moss_h))
        pygame.draw.ellipse(sprite_surface, moss_dark, (moss_x + 1, moss_y + 1, moss_w - 2, moss_h - 2))
    
    return sprite_surface


def get_wolf_sprite(size: Tuple[int, int] = (44, 32)) -> pygame.Surface:
    """Generate a detailed Wolf sprite procedurally."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Enhanced color palette
    body_color = (105, 105, 105)    # Steel gray base
    fur_dark = (80, 80, 80)         # Darker gray fur
    fur_light = (130, 130, 130)     # Lighter gray highlights
    underbelly = (150, 150, 150)    # Light gray underbelly
    eye_color = (255, 255, 0)       # Piercing yellow eyes
    eye_glow = (255, 255, 100)      # Eye glow effect
    nose_color = (40, 40, 40)       # Dark nose
    teeth_color = (255, 255, 255)   # White fangs
    claw_color = (60, 60, 60)       # Dark claws
    inner_ear = (180, 120, 120)     # Pink inner ear
    tongue_color = (200, 100, 120)  # Pink tongue
    
    # Muscular, lean body (side profile)
    body_rect = pygame.Rect(s_w*0.18, s_h*0.32, s_w*0.52, s_h*0.32)
    pygame.draw.ellipse(sprite_surface, body_color, body_rect)
    
    # Body muscle definition and fur texture
    muscle_rect = pygame.Rect(body_rect.left + s_w*0.02, body_rect.top + s_h*0.02, 
                             body_rect.width - s_w*0.04, body_rect.height*0.7)
    pygame.draw.ellipse(sprite_surface, fur_light, muscle_rect)
    
    # Underbelly marking
    belly_rect = pygame.Rect(body_rect.left + s_w*0.04, body_rect.centery + s_h*0.02, 
                            body_rect.width - s_w*0.08, body_rect.height*0.4)
    pygame.draw.ellipse(sprite_surface, underbelly, belly_rect)
    
    # Detailed head with wolf proportions
    head_x, head_y = body_rect.left - s_w*0.08, body_rect.top - s_h*0.03
    head_rect = pygame.Rect(head_x, head_y, s_w*0.32, s_h*0.26)
    pygame.draw.ellipse(sprite_surface, body_color, head_rect)
    
    # Head fur detail
    head_fur = pygame.Rect(head_rect.left + s_w*0.01, head_rect.top + s_h*0.01, 
                          head_rect.width - s_w*0.02, head_rect.height - s_h*0.02)
    pygame.draw.ellipse(sprite_surface, fur_light, head_fur)
    
    # Elongated snout (characteristic wolf feature)
    snout_x, snout_y = head_x - s_w*0.08, head_y + s_h*0.1
    snout_width, snout_height = s_w*0.12, s_h*0.1
    snout_rect = pygame.Rect(snout_x, snout_y, snout_width, snout_height)
    pygame.draw.ellipse(sprite_surface, body_color, snout_rect)
    
    # Snout highlight
    snout_highlight = pygame.Rect(snout_x + s_w*0.01, snout_y + s_h*0.01, 
                                 snout_width - s_w*0.02, snout_height*0.6)
    pygame.draw.ellipse(sprite_surface, fur_light, snout_highlight)
    
    # Prominent black nose
    nose_center = (int(snout_x - s_w*0.02), int(snout_y + snout_height*0.4))
    pygame.draw.ellipse(sprite_surface, nose_color, 
                       (nose_center[0] - s_w*0.015, nose_center[1] - s_h*0.01, 
                        s_w*0.03, s_h*0.02))
    
    # Nostrils
    pygame.draw.circle(sprite_surface, (20, 20, 20), 
                      (nose_center[0] - s_w*0.005, nose_center[1]), 1)
    pygame.draw.circle(sprite_surface, (20, 20, 20), 
                      (nose_center[0] + s_w*0.005, nose_center[1]), 1)
    
    # Intense, predatory eyes
    eye_left = (int(head_x + s_w*0.08), int(head_y + s_h*0.07))
    eye_right = (int(head_x + s_w*0.18), int(head_y + s_h*0.06))
    
    # Eye glow effects for night predator look
    pygame.draw.circle(sprite_surface, eye_glow, eye_left, 4)
    pygame.draw.circle(sprite_surface, eye_color, eye_left, 3)
    pygame.draw.circle(sprite_surface, (255, 255, 200), eye_left, 2)
    pygame.draw.circle(sprite_surface, (0, 0, 0), eye_left, 1)  # Pupil
    
    pygame.draw.circle(sprite_surface, eye_glow, eye_right, 3)
    pygame.draw.circle(sprite_surface, eye_color, eye_right, 2)
    pygame.draw.circle(sprite_surface, (0, 0, 0), eye_right, 1)  # Pupil
    
    # Snarling mouth with visible fangs
    mouth_y = snout_y + snout_height*0.7
    mouth_width = s_w*0.06
    mouth_rect = pygame.Rect(snout_x - s_w*0.01, mouth_y, mouth_width, s_h*0.02)
    pygame.draw.ellipse(sprite_surface, (20, 20, 20), mouth_rect)
    
    # Exposed fangs for menacing appearance
    fang_positions = [
        (snout_x, mouth_y),
        (snout_x + s_w*0.02, mouth_y),
        (snout_x + s_w*0.04, mouth_y)
    ]
    for fang_x, fang_y in fang_positions:
        pygame.draw.polygon(sprite_surface, teeth_color, [
            (fang_x, fang_y), 
            (fang_x + s_w*0.005, fang_y + s_h*0.025), 
            (fang_x - s_w*0.005, fang_y + s_h*0.025)
        ])
    
    # Slightly visible tongue
    tongue_tip = (snout_x + s_w*0.01, mouth_y + s_h*0.015)
    pygame.draw.ellipse(sprite_surface, tongue_color, 
                       (tongue_tip[0], tongue_tip[1], s_w*0.015, s_h*0.008))
    
    # Alert, pointed ears
    ear_left_outer = [
        (head_x + s_w*0.05, head_y + s_h*0.02),
        (head_x + s_w*0.02, head_y - s_h*0.08),
        (head_x + s_w*0.12, head_y + s_h*0.02)
    ]
    ear_left_inner = [
        (head_x + s_w*0.06, head_y + s_h*0.02),
        (head_x + s_w*0.04, head_y - s_h*0.05),
        (head_x + s_w*0.1, head_y + s_h*0.02)
    ]
    
    ear_right_outer = [
        (head_x + s_w*0.18, head_y + s_h*0.02),
        (head_x + s_w*0.15, head_y - s_h*0.06),
        (head_x + s_w*0.25, head_y + s_h*0.02)
    ]
    ear_right_inner = [
        (head_x + s_w*0.19, head_y + s_h*0.02),
        (head_x + s_w*0.17, head_y - s_h*0.04),
        (head_x + s_w*0.23, head_y + s_h*0.02)
    ]
    
    pygame.draw.polygon(sprite_surface, fur_dark, ear_left_outer)
    pygame.draw.polygon(sprite_surface, inner_ear, ear_left_inner)
    pygame.draw.polygon(sprite_surface, fur_dark, ear_right_outer)
    pygame.draw.polygon(sprite_surface, inner_ear, ear_right_inner)
    
    # Detailed fur markings for realistic wolf appearance
    fur_markings = [
        # Shoulder marking
        (body_rect.left + s_w*0.05, body_rect.top + s_h*0.03, s_w*0.08, s_h*0.06),
        # Back stripe
        (body_rect.centerx - s_w*0.02, body_rect.top, s_w*0.04, body_rect.height*0.4),
        # Hip marking
        (body_rect.right - s_w*0.12, body_rect.centery - s_h*0.02, s_w*0.1, s_h*0.05),
        # Head marking
        (head_x + s_w*0.08, head_y + s_h*0.12, s_w*0.12, s_h*0.04)
    ]
    
    for mark_x, mark_y, mark_w, mark_h in fur_markings:
        pygame.draw.ellipse(sprite_surface, fur_dark, (mark_x, mark_y, mark_w, mark_h))
    
    # Powerful, muscular legs
    leg_width = int(s_w*0.04)
    leg_positions = [
        (body_rect.left + s_w*0.08, 0.1, -s_w*0.01),  # Front left
        (body_rect.left + s_w*0.18, 0.3, s_w*0.01),   # Front right
        (body_rect.right - s_w*0.18, 0.6, -s_w*0.015), # Back left
        (body_rect.right - s_w*0.08, 0.8, s_w*0.015)   # Back right
    ]
    
    for leg_x, leg_offset, stance_adj in leg_positions:
        leg_top = body_rect.bottom - s_h*0.02
        leg_mid = leg_top + s_h*0.12
        leg_bottom = leg_top + s_h*0.22
        
        # Upper leg (thicker, muscular)
        pygame.draw.line(sprite_surface, body_color, 
                        (leg_x, leg_top), (leg_x + stance_adj, leg_mid), leg_width)
        # Lower leg (thinner)
        pygame.draw.line(sprite_surface, body_color, 
                        (leg_x + stance_adj, leg_mid), (leg_x + stance_adj, leg_bottom), leg_width - 1)
        
        # Knee joint
        pygame.draw.circle(sprite_surface, fur_dark, 
                          (int(leg_x + stance_adj), int(leg_mid)), leg_width // 2)
        
        # Paw with claws
        paw_center = (int(leg_x + stance_adj), int(leg_bottom))
        pygame.draw.ellipse(sprite_surface, fur_dark, 
                           (paw_center[0] - s_w*0.02, paw_center[1] - s_h*0.015, 
                            s_w*0.04, s_h*0.03))
        
        # Visible claws
        for claw_offset in [-s_w*0.01, 0, s_w*0.01]:
            claw_pos = (paw_center[0] + claw_offset, paw_center[1] + s_h*0.01)
            pygame.draw.line(sprite_surface, claw_color, 
                           claw_pos, (claw_pos[0], claw_pos[1] + s_h*0.015), 1)
    
    # Bushy tail (wolf characteristic)
    tail_base = (body_rect.right - s_w*0.02, body_rect.centery + s_h*0.05)
    tail_mid = (tail_base[0] + s_w*0.1, tail_base[1] + s_h*0.02)
    tail_tip = (tail_mid[0] + s_w*0.08, tail_mid[1] - s_h*0.08)
    
    # Tail body (thick and bushy)
    pygame.draw.line(sprite_surface, body_color, tail_base, tail_mid, 7)
    pygame.draw.line(sprite_surface, body_color, tail_mid, tail_tip, 6)
    
    # Tail fur details
    pygame.draw.line(sprite_surface, fur_dark, tail_base, tail_mid, 5)
    pygame.draw.line(sprite_surface, fur_dark, tail_mid, tail_tip, 4)
    
    # Tail tip with different coloring
    pygame.draw.circle(sprite_surface, fur_light, (int(tail_tip[0]), int(tail_tip[1])), 4)
    pygame.draw.circle(sprite_surface, fur_dark, (int(tail_tip[0]), int(tail_tip[1])), 2)
    
    return sprite_surface


def get_goblin_basic_sprite(size: Tuple[int, int] = (30, 30)) -> pygame.Surface:
    """Generate a detailed basic Goblin sprite procedurally."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Enhanced color palette
    skin_color = (80, 140, 80)      # Vibrant green skin
    dark_skin = (60, 110, 60)       # Darker green shadows
    clothing_color = (139, 69, 19)  # Brown clothing
    clothing_dark = (100, 50, 15)   # Darker brown for shadows
    eye_color = (255, 255, 0)       # Bright yellow eyes
    eye_glow = (255, 255, 100)      # Eye glow
    teeth_color = (255, 255, 200)   # Slightly yellowed teeth
    belt_color = (80, 40, 20)       # Dark brown belt
    weapon_color = (160, 160, 160)  # Simple weapon
    
    # Compact, wiry body (basic goblin is smaller than grunt)
    body_rect = pygame.Rect(s_w*0.28, s_h*0.42, s_w*0.38, s_h*0.32)
    pygame.draw.ellipse(sprite_surface, skin_color, body_rect)
    
    # Body shading for dimension
    body_shadow = pygame.Rect(body_rect.left + s_w*0.02, body_rect.centery, 
                             body_rect.width - s_w*0.02, body_rect.height//2)
    pygame.draw.ellipse(sprite_surface, dark_skin, body_shadow)
    
    # Simple but detailed clothing
    cloth_rect = pygame.Rect(s_w*0.3, s_h*0.48, s_w*0.34, s_h*0.22)
    pygame.draw.rect(sprite_surface, clothing_color, cloth_rect)
    
    # Clothing texture with rough stitching
    stitch_y = cloth_rect.top + s_h*0.02
    pygame.draw.line(sprite_surface, clothing_dark, 
                    (cloth_rect.left + s_w*0.02, stitch_y), 
                    (cloth_rect.right - s_w*0.02, stitch_y), 1)
    
    # Simple belt
    belt_rect = pygame.Rect(cloth_rect.left, cloth_rect.bottom - s_h*0.03, 
                           cloth_rect.width, s_h*0.03)
    pygame.draw.rect(sprite_surface, belt_color, belt_rect)
    
    # Characterful head (proportionally large for goblins)
    head_x, head_y = body_rect.centerx, body_rect.top - s_h*0.12
    pygame.draw.circle(sprite_surface, skin_color, (int(head_x), int(head_y)), int(s_w*0.18))
    
    # Head shading for definition
    pygame.draw.circle(sprite_surface, dark_skin, 
                      (int(head_x + s_w*0.02), int(head_y + s_h*0.02)), int(s_w*0.16))
    
    # Prominent, characteristic goblin ears
    ear_left_outer = [
        (head_x - s_w*0.14, head_y - s_h*0.06),
        (head_x - s_w*0.25, head_y - s_h*0.18),
        (head_x - s_w*0.08, head_y - s_h*0.1)
    ]
    ear_left_inner = [
        (head_x - s_w*0.12, head_y - s_h*0.07),
        (head_x - s_w*0.21, head_y - s_h*0.15),
        (head_x - s_w*0.1, head_y - s_h*0.09)
    ]
    
    pygame.draw.polygon(sprite_surface, skin_color, ear_left_outer)
    pygame.draw.polygon(sprite_surface, dark_skin, ear_left_inner)
    
    # Right ear (partially visible)
    ear_right_outer = [
        (head_x + s_w*0.12, head_y - s_h*0.08),
        (head_x + s_w*0.2, head_y - s_h*0.15),
        (head_x + s_w*0.08, head_y - s_h*0.05)
    ]
    pygame.draw.polygon(sprite_surface, skin_color, ear_right_outer)
    
    # Expressive, mischievous eyes
    eye_main = (int(head_x + s_w*0.04), int(head_y - s_h*0.02))
    eye_secondary = (int(head_x - s_w*0.02), int(head_y - s_h*0.01))
    
    # Main eye with glow
    pygame.draw.circle(sprite_surface, eye_glow, eye_main, 4)
    pygame.draw.circle(sprite_surface, eye_color, eye_main, 3)
    pygame.draw.circle(sprite_surface, (255, 255, 150), eye_main, 2)
    pygame.draw.circle(sprite_surface, (0, 0, 0), eye_main, 1)  # Pupil
    
    # Secondary eye (partially visible)
    pygame.draw.circle(sprite_surface, eye_color, eye_secondary, 2)
    pygame.draw.circle(sprite_surface, (0, 0, 0), eye_secondary, 1)
    
    # Characteristic goblin grin with sharp teeth
    mouth_rect = pygame.Rect(head_x - s_w*0.06, head_y + s_h*0.06, s_w*0.12, s_h*0.04)
    pygame.draw.ellipse(sprite_surface, (20, 20, 20), mouth_rect)
    
    # Multiple sharp teeth for menacing grin
    tooth_positions = [
        (head_x - s_w*0.05, head_y + s_h*0.06),
        (head_x - s_w*0.02, head_y + s_h*0.06),
        (head_x + s_w*0.01, head_y + s_h*0.06),
        (head_x + s_w*0.04, head_y + s_h*0.06)
    ]
    for tooth_x, tooth_y in tooth_positions:
        pygame.draw.polygon(sprite_surface, teeth_color, [
            (tooth_x, tooth_y), 
            (tooth_x + s_w*0.008, tooth_y + s_h*0.02), 
            (tooth_x - s_w*0.008, tooth_y + s_h*0.02)
        ])
    
    # Thin, wiry arms with character
    # Left arm (holding simple weapon)
    arm_left_start = (body_rect.left - s_w*0.01, body_rect.centery - s_h*0.02)
    arm_left_end = (arm_left_start[0] - s_w*0.12, arm_left_start[1] + s_h*0.08)
    pygame.draw.line(sprite_surface, skin_color, arm_left_start, arm_left_end, 4)
    
    # Simple weapon (crude knife)
    weapon_handle_start = arm_left_end
    weapon_handle_end = (weapon_handle_start[0] - s_w*0.06, weapon_handle_start[1] + s_h*0.02)
    weapon_blade_end = (weapon_handle_end[0] - s_w*0.05, weapon_handle_end[1] - s_h*0.01)
    
    # Weapon handle
    pygame.draw.line(sprite_surface, clothing_color, weapon_handle_start, weapon_handle_end, 2)
    
    # Simple blade
    blade_points = [
        weapon_handle_end,
        weapon_blade_end,
        (weapon_blade_end[0] + s_w*0.01, weapon_blade_end[1] + s_h*0.015)
    ]
    pygame.draw.polygon(sprite_surface, weapon_color, blade_points)
    
    # Right arm (gesturing or scratching)
    arm_right_start = (body_rect.right + s_w*0.01, body_rect.centery)
    arm_right_end = (arm_right_start[0] + s_w*0.08, arm_right_start[1] + s_h*0.06)
    pygame.draw.line(sprite_surface, skin_color, arm_right_start, arm_right_end, 4)
    
    # Hand detail
    pygame.draw.circle(sprite_surface, skin_color, 
                      (int(arm_right_end[0]), int(arm_right_end[1])), 2)
    
    # Stubby but sturdy legs
    leg_width = int(s_w*0.04)
    leg_spacing = s_w*0.08
    
    # Left leg
    left_leg_x = body_rect.centerx - leg_spacing//2
    leg_top = body_rect.bottom
    leg_bottom = leg_top + s_h*0.18
    
    pygame.draw.line(sprite_surface, skin_color, 
                    (left_leg_x, leg_top), (left_leg_x - s_w*0.01, leg_bottom), leg_width)
    
    # Right leg
    right_leg_x = body_rect.centerx + leg_spacing//2
    pygame.draw.line(sprite_surface, skin_color, 
                    (right_leg_x, leg_top), (right_leg_x + s_w*0.01, leg_bottom), leg_width)
    
    # Simple footwear (wrappings)
    foot_left = (int(left_leg_x - s_w*0.01), int(leg_bottom))
    foot_right = (int(right_leg_x + s_w*0.01), int(leg_bottom))
    
    pygame.draw.ellipse(sprite_surface, clothing_color, 
                       (foot_left[0] - s_w*0.02, foot_left[1] - s_h*0.015, 
                        s_w*0.04, s_h*0.03))
    pygame.draw.ellipse(sprite_surface, clothing_color, 
                       (foot_right[0] - s_w*0.02, foot_right[1] - s_h*0.015, 
                        s_w*0.04, s_h*0.03))
    
    # Small details for character
    # Scar on face
    scar_start = (head_x - s_w*0.01, head_y + s_h*0.02)
    scar_end = (head_x + s_w*0.02, head_y + s_h*0.04)
    pygame.draw.line(sprite_surface, dark_skin, scar_start, scar_end, 1)
    
    # Nose
    nose_pos = (int(head_x + s_w*0.01), int(head_y + s_h*0.02))
    pygame.draw.circle(sprite_surface, dark_skin, nose_pos, 1)
    
    return sprite_surface


# Icon generation functions for map editor
def get_goblin_grunt_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a Goblin Grunt icon for map editor."""
    icon_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Simple goblin head with weapon
    head_color = (60, 120, 60)      # Green skin
    weapon_color = (192, 192, 192)  # Silver blade
    eye_color = (255, 0, 0)         # Red eyes

    # Head
    pygame.draw.circle(icon_surface, head_color, (int(s_w*0.4), int(s_h*0.4)), int(s_w*0.2))

    # Pointed ear
    ear_points = [
        (s_w*0.25, s_h*0.35),
        (s_w*0.15, s_h*0.25),
        (s_w*0.3, s_h*0.3)
    ]
    pygame.draw.polygon(icon_surface, head_color, ear_points)

    # Eye
    pygame.draw.circle(icon_surface, eye_color, (int(s_w*0.42), int(s_h*0.38)), 2)

    # Weapon (sword)
    pygame.draw.line(icon_surface, weapon_color,
        (s_w*0.6, s_h*0.3), (s_w*0.8, s_h*0.1), 3)

    return icon_surface


def get_goblin_shaman_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a Goblin Shaman icon for map editor."""
    icon_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Shaman with staff and orb
    head_color = (40, 100, 40)      # Darker green skin
    staff_color = (139, 69, 19)     # Brown staff
    orb_color = (0, 255, 255)       # Cyan magical orb

    # Head
    pygame.draw.circle(icon_surface, head_color, (int(s_w*0.4), int(s_h*0.4)), int(s_w*0.18))

    # Staff
    pygame.draw.line(icon_surface, staff_color,
        (s_w*0.65, s_h*0.2), (s_w*0.65, s_h*0.8), 4)

    # Magical orb
    pygame.draw.circle(icon_surface, orb_color, (int(s_w*0.65), int(s_h*0.2)), 6)

    return icon_surface


def get_goblin_basic_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a basic Goblin icon for map editor."""
    icon_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Simple goblin head
    head_color = (80, 140, 80)      # Brighter green skin
    eye_color = (255, 255, 0)       # Yellow eyes
    teeth_color = (255, 255, 255)   # White teeth

    # Head
    pygame.draw.circle(icon_surface, head_color, (int(s_w*0.5), int(s_h*0.5)), int(s_w*0.25))

    # Large pointed ears
    ear_points = [
        (s_w*0.3, s_h*0.45),
        (s_w*0.15, s_h*0.3),
        (s_w*0.35, s_h*0.4)
    ]
    pygame.draw.polygon(icon_surface, head_color, ear_points)

    # Eye
    pygame.draw.circle(icon_surface, eye_color, (int(s_w*0.55), int(s_h*0.48)), 3)

    # Grinning mouth
    pygame.draw.arc(icon_surface, teeth_color,
        (s_w*0.45, s_h*0.55, s_w*0.15, s_h*0.1), 0, 3.14, 2)

    return icon_surface


def get_green_drake_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a Green Drake icon for map editor."""
    icon_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Dragon head with wing
    body_color = (34, 139, 34)      # Forest Green
    wing_color = (0, 100, 0)        # Darker green
    eye_color = (255, 255, 0)       # Yellow

    # Dragon head
    head_points = [
        (s_w*0.3, s_h*0.5),
        (s_w*0.1, s_h*0.45),
        (s_w*0.05, s_h*0.55),
        (s_w*0.25, s_h*0.6)
    ]
    pygame.draw.polygon(icon_surface, body_color, head_points)

    # Wing
    wing_points = [
        (s_w*0.4, s_h*0.3),
        (s_w*0.7, s_h*0.2),
        (s_w*0.8, s_h*0.5),
        (s_w*0.5, s_h*0.6)
    ]
    pygame.draw.polygon(icon_surface, wing_color, wing_points)

    # Eye
    pygame.draw.circle(icon_surface, eye_color, (int(s_w*0.2), int(s_h*0.48)), 3)

    return icon_surface


def get_sand_scorpion_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a Sand Scorpion icon for map editor."""
    icon_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Scorpion body with tail and stinger
    body_color = (194, 154, 108)    # Sandy brown
    stinger_color = (80, 40, 20)    # Dark brown

    # Body segments
    pygame.draw.ellipse(icon_surface, body_color,
        (s_w*0.2, s_h*0.4, s_w*0.3, s_h*0.2))
    pygame.draw.ellipse(icon_surface, body_color,
        (s_w*0.45, s_h*0.45, s_w*0.2, s_h*0.15))

    # Claws
    claw_points = [
        (s_w*0.15, s_h*0.35),
        (s_w*0.05, s_h*0.25),
        (s_w*0.1, s_h*0.4)
    ]
    pygame.draw.polygon(icon_surface, body_color, claw_points)

    # Tail with stinger
    pygame.draw.line(icon_surface, body_color,
        (s_w*0.65, s_h*0.5), (s_w*0.85, s_h*0.3), 3)
    pygame.draw.circle(icon_surface, stinger_color, (int(s_w*0.85), int(s_h*0.3)), 3)

    return icon_surface


def get_ice_wolf_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate an Ice Wolf icon for map editor."""
    icon_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Wolf head with ice effects
    body_color = (200, 220, 255)    # Icy blue-white
    eye_color = (100, 150, 255)     # Ice blue
    frost_color = (240, 248, 255)   # Almost white frost

    # Wolf head
    head_points = [
        (s_w*0.3, s_h*0.4),
        (s_w*0.1, s_h*0.45),
        (s_w*0.05, s_h*0.55),
        (s_w*0.25, s_h*0.6),
        (s_w*0.4, s_h*0.5)
    ]
    pygame.draw.polygon(icon_surface, body_color, head_points)

    # Pointed ears
    ear_points = [
        (s_w*0.35, s_h*0.3),
        (s_w*0.3, s_h*0.2),
        (s_w*0.4, s_h*0.35)
    ]
    pygame.draw.polygon(icon_surface, body_color, ear_points)

    # Ice blue eye
    pygame.draw.circle(icon_surface, eye_color, (int(s_w*0.22), int(s_h*0.45)), 3)

    # Frost markings
    pygame.draw.circle(icon_surface, frost_color, (int(s_w*0.6), int(s_h*0.4)), 4)
    pygame.draw.circle(icon_surface, frost_color, (int(s_w*0.7), int(s_h*0.6)), 3)

    return icon_surface


def get_forest_troll_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a Forest Troll icon for map editor."""
    icon_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Troll head with moss
    skin_color = (60, 80, 40)       # Dark green-brown
    moss_color = (34, 139, 34)      # Forest green moss
    eye_color = (255, 0, 0)         # Red eyes

    # Large brutish head
    pygame.draw.circle(icon_surface, skin_color, (int(s_w*0.5), int(s_h*0.5)), int(s_w*0.3))

    # Moss patches
    pygame.draw.ellipse(icon_surface, moss_color,
        (s_w*0.4, s_h*0.3, s_w*0.2, s_h*0.15))
    pygame.draw.ellipse(icon_surface, moss_color,
        (s_w*0.6, s_h*0.6, s_w*0.15, s_h*0.1))

    # Small red eyes
    pygame.draw.circle(icon_surface, eye_color, (int(s_w*0.45), int(s_h*0.45)), 2)
    pygame.draw.circle(icon_surface, eye_color, (int(s_w*0.55), int(s_h*0.45)), 2)

    # Tusk
    tusk_points = [
        (s_w*0.47, s_h*0.55),
        (s_w*0.45, s_h*0.65),
        (s_w*0.49, s_h*0.6)
    ]
    pygame.draw.polygon(icon_surface, (255, 255, 255), tusk_points)

    return icon_surface


def get_wolf_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a Wolf icon for map editor."""
    icon_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Wolf head
    body_color = (105, 105, 105)    # Gray
    eye_color = (255, 255, 0)       # Yellow
    teeth_color = (255, 255, 255)   # White

    # Wolf head
    head_points = [
        (s_w*0.3, s_h*0.4),
        (s_w*0.1, s_h*0.45),
        (s_w*0.05, s_h*0.55),
        (s_w*0.25, s_h*0.6),
        (s_w*0.4, s_h*0.5)
    ]
    pygame.draw.polygon(icon_surface, body_color, head_points)

    # Pointed ears
    ear_points = [
        (s_w*0.35, s_h*0.3),
        (s_w*0.3, s_h*0.2),
        (s_w*0.4, s_h*0.35)
    ]
    pygame.draw.polygon(icon_surface, body_color, ear_points)

    # Yellow eye
    pygame.draw.circle(icon_surface, eye_color, (int(s_w*0.22), int(s_h*0.45)), 3)
    pygame.draw.circle(icon_surface, (0, 0, 0), (int(s_w*0.22), int(s_h*0.45)), 1)  # Pupil

    # Teeth
    pygame.draw.line(icon_surface, teeth_color,
        (s_w*0.15, s_h*0.52), (s_w*0.12, s_h*0.58), 2)

    return icon_surface


# Animal icons (for peaceful creatures)
def get_horse_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a Horse icon for map editor."""
    icon_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Horse head with mane
    body_color = (139, 69, 19)      # Brown
    mane_color = (101, 67, 33)      # Dark brown

    # Horse head
    pygame.draw.ellipse(icon_surface, body_color,
        (s_w*0.2, s_w*0.3, s_w*0.4, s_h*0.5))

    # Mane
    for i in range(4):
        mane_x = s_w*0.25 + i * s_w*0.08
        pygame.draw.line(icon_surface, mane_color,
            (mane_x, s_h*0.25), (mane_x - s_w*0.03, s_h*0.15), 2)

    # Eye
    pygame.draw.circle(icon_surface, (0, 0, 0), (int(s_w*0.35), int(s_h*0.45)), 2)

    return icon_surface


def get_cow_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a Cow icon for map editor."""
    icon_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Cow head with spots
    body_color = (255, 255, 255)    # White
    spot_color = (0, 0, 0)          # Black spots

    # Cow head
    pygame.draw.ellipse(icon_surface, body_color,
        (s_w*0.25, s_h*0.3, s_w*0.5, s_h*0.4))

    # Spots
    pygame.draw.ellipse(icon_surface, spot_color,
        (s_w*0.3, s_h*0.35, s_w*0.15, s_h*0.1))
    pygame.draw.ellipse(icon_surface, spot_color,
        (s_w*0.5, s_h*0.5, s_w*0.12, s_h*0.08))

    # Eye
    pygame.draw.circle(icon_surface, (0, 0, 0), (int(s_w*0.4), int(s_h*0.45)), 2)

    return icon_surface


def get_chicken_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a Chicken icon for map editor."""
    icon_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Chicken with comb
    body_color = (255, 255, 255)    # White
    comb_color = (255, 0, 0)        # Red comb
    beak_color = (255, 165, 0)      # Orange beak

    # Chicken body
    pygame.draw.ellipse(icon_surface, body_color,
        (s_w*0.3, s_h*0.4, s_w*0.4, s_h*0.3))

    # Head
    pygame.draw.circle(icon_surface, body_color, (int(s_w*0.4), int(s_h*0.35)), int(s_w*0.12))

    # Comb
    comb_points = [
        (s_w*0.37, s_h*0.25),
        (s_w*0.4, s_h*0.18),
        (s_w*0.43, s_h*0.25)
    ]
    pygame.draw.polygon(icon_surface, comb_color, comb_points)

    # Beak
    beak_points = [
        (s_w*0.32, s_h*0.35),
        (s_w*0.25, s_h*0.37),
        (s_w*0.32, s_h*0.39)
    ]
    pygame.draw.polygon(icon_surface, beak_color, beak_points)

    return icon_surface


def get_deer_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a Deer icon for map editor."""
    icon_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Deer head with antlers
    body_color = (139, 69, 19)      # Brown
    antler_color = (101, 67, 33)    # Dark brown

    # Deer head
    pygame.draw.ellipse(icon_surface, body_color,
        (s_w*0.3, s_h*0.4, s_w*0.4, s_h*0.3))

    # Antlers
    pygame.draw.line(icon_surface, antler_color,
        (s_w*0.45, s_h*0.3), (s_w*0.4, s_h*0.15), 2)
    pygame.draw.line(icon_surface, antler_color,
        (s_w*0.55, s_h*0.3), (s_w*0.6, s_h*0.15), 2)
    # Antler points
    pygame.draw.line(icon_surface, antler_color,
        (s_w*0.42, s_h*0.2), (s_w*0.38, s_h*0.12), 1)
    pygame.draw.line(icon_surface, antler_color,
        (s_w*0.58, s_h*0.2), (s_w*0.62, s_h*0.12), 1)

    # Eye
    pygame.draw.circle(icon_surface, (0, 0, 0), (int(s_w*0.45), int(s_h*0.48)), 2)

    return icon_surface


def get_pig_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a Pig icon for map editor."""
    icon_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Pink pig with snout
    body_color = (255, 192, 203)    # Pink
    snout_color = (255, 105, 180)   # Hot pink

    # Pig head
    pygame.draw.ellipse(icon_surface, body_color,
        (s_w*0.25, s_h*0.35, s_w*0.5, s_h*0.4))

    # Snout
    pygame.draw.ellipse(icon_surface, snout_color,
        (s_w*0.15, s_h*0.5, s_w*0.2, s_h*0.15))

    # Nostrils
    pygame.draw.circle(icon_surface, (0, 0, 0), (int(s_w*0.2), int(s_h*0.55)), 1)
    pygame.draw.circle(icon_surface, (0, 0, 0), (int(s_w*0.28), int(s_h*0.55)), 1)

    # Eye
    pygame.draw.circle(icon_surface, (0, 0, 0), (int(s_w*0.4), int(s_h*0.48)), 2)

    # Ear
    ear_points = [
        (s_w*0.45, s_h*0.35),
        (s_w*0.42, s_h*0.25),
        (s_w*0.52, s_h*0.37)
    ]
    pygame.draw.polygon(icon_surface, body_color, ear_points)

    return icon_surface


def get_dog_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a Dog icon for map editor."""
    icon_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Dog head with floppy ear
    body_color = (139, 69, 19)      # Brown
    ear_color = (101, 67, 33)       # Dark brown
    tongue_color = (255, 192, 203)  # Pink

    # Dog head
    pygame.draw.ellipse(icon_surface, body_color,
        (s_w*0.3, s_h*0.35, s_w*0.4, s_h*0.3))

    # Snout
    pygame.draw.ellipse(icon_surface, body_color,
        (s_w*0.15, s_h*0.5, s_w*0.2, s_h*0.15))

    # Floppy ear
    ear_points = [
        (s_w*0.32, s_h*0.35),
        (s_w*0.25, s_h*0.25),
        (s_w*0.4, s_h*0.37)
    ]
    pygame.draw.polygon(icon_surface, ear_color, ear_points)

    # Eye
    pygame.draw.circle(icon_surface, (0, 0, 0), (int(s_w*0.42), int(s_h*0.45)), 2)

    # Tongue
    tongue_points = [
        (s_w*0.2, s_h*0.6),
        (s_w*0.18, s_h*0.68),
        (s_w*0.22, s_h*0.65)
    ]
    pygame.draw.polygon(icon_surface, tongue_color, tongue_points)

    return icon_surface


def get_spider_sprite(size: Tuple[int, int] = (40, 32)) -> pygame.Surface:
    """Generate a detailed Forest Spider sprite procedurally."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Enhanced color palette
    body_color = (45, 30, 15)       # Dark chocolate brown body
    abdomen_color = (25, 15, 8)     # Darker brown abdomen
    leg_color = (70, 50, 30)        # Lighter brown legs
    joint_color = (90, 65, 40)      # Even lighter for joints
    eye_color = (255, 40, 40)       # Bright red eyes
    fang_color = (255, 255, 200)    # Ivory fangs
    pattern_color = (100, 80, 60)   # Pattern on abdomen
    shadow_color = (20, 10, 5)      # Deep shadow
    highlight_color = (100, 70, 45) # Body highlights
    web_color = (200, 200, 200)     # Silver web strands
    
    # Main body (cephalothorax) - more realistic spider proportions
    body_rect = pygame.Rect(s_w*0.28, s_h*0.38, s_w*0.22, s_h*0.25)
    pygame.draw.ellipse(sprite_surface, body_color, body_rect)
    
    # Body depth and texture
    body_highlight = pygame.Rect(body_rect.left + s_w*0.02, body_rect.top + s_w*0.01, 
                                 body_rect.width - s_w*0.04, body_rect.height*0.6)
    pygame.draw.ellipse(sprite_surface, highlight_color, body_highlight)
    
    # Central body ridge
    ridge_rect = pygame.Rect(body_rect.centerx - s_w*0.01, body_rect.top + s_w*0.02, 
                            s_w*0.02, body_rect.height - s_w*0.04)
    pygame.draw.ellipse(sprite_surface, shadow_color, ridge_rect)

    # Large, imposing abdomen
    abdomen_rect = pygame.Rect(s_w*0.48, s_h*0.22, s_w*0.38, s_h*0.56)
    pygame.draw.ellipse(sprite_surface, abdomen_color, abdomen_rect)
    
    # Abdomen shadow for depth
    abdomen_shadow = pygame.Rect(abdomen_rect.left + s_w*0.02, abdomen_rect.centery, 
                                abdomen_rect.width - s_w*0.02, abdomen_rect.height//2)
    pygame.draw.ellipse(sprite_surface, shadow_color, abdomen_shadow)

    # Intricate pattern on abdomen - classic spider markings
    pattern_main = pygame.Rect(abdomen_rect.centerx - s_w*0.08, abdomen_rect.centery - s_h*0.12, 
                              s_w*0.16, s_h*0.24)
    pygame.draw.ellipse(sprite_surface, pattern_color, pattern_main)
    
    # Chevron pattern
    for i in range(3):
        chevron_y = abdomen_rect.top + s_h*0.1 + i * s_h*0.08
        chevron_points = [
            (abdomen_rect.centerx, chevron_y),
            (abdomen_rect.centerx - s_w*0.04, chevron_y + s_h*0.03),
            (abdomen_rect.centerx, chevron_y + s_h*0.06),
            (abdomen_rect.centerx + s_w*0.04, chevron_y + s_h*0.03)
        ]
        pygame.draw.polygon(sprite_surface, body_color, chevron_points)

    # Multiple eyes cluster - realistic spider eye arrangement
    eye_size_main = max(2, int(s_w * 0.025))
    eye_size_small = max(1, int(s_w * 0.015))
    
    # Front center eyes (largest)
    eye_front_left = (body_rect.left + s_w*0.03, body_rect.top + s_h*0.06)
    eye_front_right = (body_rect.left + s_w*0.08, body_rect.top + s_h*0.06)
    
    # Secondary eyes
    eye_side_left = (body_rect.left + s_w*0.01, body_rect.top + s_h*0.04)
    eye_side_right = (body_rect.left + s_w*0.1, body_rect.top + s_h*0.04)
    
    # Upper eyes
    eye_upper_left = (body_rect.left + s_w*0.025, body_rect.top + s_h*0.02)
    eye_upper_right = (body_rect.left + s_w*0.085, body_rect.top + s_h*0.02)
    
    # Draw eyes with glow effect
    main_eyes = [eye_front_left, eye_front_right]
    side_eyes = [eye_side_left, eye_side_right, eye_upper_left, eye_upper_right]
    
    for eye_pos in main_eyes:
        # Glow effect
        pygame.draw.circle(sprite_surface, (100, 20, 20), eye_pos, eye_size_main + 1)
        pygame.draw.circle(sprite_surface, eye_color, eye_pos, eye_size_main)
        pygame.draw.circle(sprite_surface, (255, 100, 100), eye_pos, eye_size_main // 2)
    
    for eye_pos in side_eyes:
        pygame.draw.circle(sprite_surface, (80, 15, 15), eye_pos, eye_size_small + 1)
        pygame.draw.circle(sprite_surface, eye_color, eye_pos, eye_size_small)

    # Prominent fangs (chelicerae)
    fang_size = max(2, int(s_w * 0.02))
    fang_left = (body_rect.left - s_w*0.01, body_rect.centery + s_h*0.02)
    fang_right = (body_rect.left + s_w*0.05, body_rect.centery + s_h*0.02)
    
    # Fang base (darker)
    pygame.draw.circle(sprite_surface, (180, 160, 140), fang_left, fang_size + 1)
    pygame.draw.circle(sprite_surface, (180, 160, 140), fang_right, fang_size + 1)
    
    # Fang tips (sharp ivory)
    pygame.draw.circle(sprite_surface, fang_color, fang_left, fang_size)
    pygame.draw.circle(sprite_surface, fang_color, fang_right, fang_size)
    
    # Sharp fang points
    pygame.draw.circle(sprite_surface, (255, 255, 255), fang_left, 1)
    pygame.draw.circle(sprite_surface, (255, 255, 255), fang_right, 1)

    # Detailed segmented legs with realistic spider anatomy
    leg_thickness_base = max(2, int(s_w * 0.015))
    leg_thickness_tip = max(1, int(s_w * 0.008))
    
    # Left side legs (4 legs, but 3 visible due to perspective)
    left_legs = [
        # Format: (start_x, start_y, joint1_x, joint1_y, joint2_x, joint2_y, end_x, end_y)
        (body_rect.left, body_rect.top + s_h*0.08, s_w*0.15, body_rect.top - s_h*0.02, s_w*0.08, body_rect.top + s_h*0.02, s_w*0.02, body_rect.top + s_h*0.08),  # Front leg
        (body_rect.left, body_rect.centery, s_w*0.12, body_rect.centery - s_h*0.18, s_w*0.05, body_rect.centery - s_h*0.08, s_w*0.01, body_rect.centery + s_h*0.02),  # Second leg
        (body_rect.left, body_rect.bottom - s_h*0.08, s_w*0.18, body_rect.bottom + s_h*0.08, s_w*0.12, body_rect.bottom + s_h*0.15, s_w*0.06, body_rect.bottom + s_h*0.18)  # Back leg
    ]

    # Right side legs (3 visible)
    right_legs = [
        (body_rect.right, body_rect.top + s_h*0.08, s_w*0.88, body_rect.top - s_h*0.02, s_w*0.92, body_rect.top + s_h*0.02, s_w*0.98, body_rect.top + s_h*0.08),  # Front leg
        (body_rect.right, body_rect.centery, s_w*0.85, body_rect.centery - s_h*0.18, s_w*0.92, body_rect.centery - s_h*0.08, s_w*0.98, body_rect.centery + s_h*0.02),  # Second leg
        (body_rect.right, body_rect.bottom - s_h*0.08, s_w*0.82, body_rect.bottom + s_h*0.08, s_w*0.88, body_rect.bottom + s_h*0.15, s_w*0.94, body_rect.bottom + s_h*0.18)  # Back leg
    ]

    # Draw all legs with segments and joints
    all_legs = left_legs + right_legs
    
    for start_x, start_y, joint1_x, joint1_y, joint2_x, joint2_y, end_x, end_y in all_legs:
        # Leg segments with tapering thickness
        # Femur (thickest)
        pygame.draw.line(sprite_surface, leg_color, (start_x, start_y), (joint1_x, joint1_y), leg_thickness_base)
        
        # Tibia (medium)
        pygame.draw.line(sprite_surface, leg_color, (joint1_x, joint1_y), (joint2_x, joint2_y), leg_thickness_base - 1)
        
        # Tarsus (thinnest) 
        pygame.draw.line(sprite_surface, leg_color, (joint2_x, joint2_y), (end_x, end_y), leg_thickness_tip)
        
        # Joint spheres for articulation
        pygame.draw.circle(sprite_surface, joint_color, (int(joint1_x), int(joint1_y)), leg_thickness_base // 2)
        pygame.draw.circle(sprite_surface, joint_color, (int(joint2_x), int(joint2_y)), leg_thickness_tip + 1)
        
        # Leg hairs/bristles (tiny details)
        for i in range(2):
            hair_offset = i * 2 - 1
            hair_x = joint2_x + hair_offset
            hair_y = joint2_y + hair_offset  
            pygame.draw.line(sprite_surface, (50, 35, 20), (hair_x, hair_y), (hair_x + hair_offset, hair_y + hair_offset), 1)
        
        # Sharp claw at leg tip
        pygame.draw.circle(sprite_surface, shadow_color, (int(end_x), int(end_y)), 2)
        pygame.draw.circle(sprite_surface, (40, 40, 40), (int(end_x), int(end_y)), 1)

    # Pedipalps (feeding appendages) - more prominent
    palp_size = max(2, int(s_w * 0.015))
    palp_left = (body_rect.left - s_w*0.02, body_rect.centery - s_h*0.04)
    palp_right = (body_rect.left + s_w*0.02, body_rect.centery - s_h*0.04)
    
    # Palp base
    pygame.draw.ellipse(sprite_surface, leg_color, (palp_left[0] - palp_size, palp_left[1] - palp_size//2, palp_size*2, palp_size))
    pygame.draw.ellipse(sprite_surface, leg_color, (palp_right[0] - palp_size, palp_right[1] - palp_size//2, palp_size*2, palp_size))
    
    # Palp tips
    pygame.draw.circle(sprite_surface, joint_color, palp_left, palp_size)
    pygame.draw.circle(sprite_surface, joint_color, palp_right, palp_size)

    # Web strands (optional detail - spider creating web)
    web_attachments = [
        ((s_w*0.02, s_h*0.05), (s_w*0.15, s_h*0.02)),
        ((s_w*0.98, s_h*0.08), (s_w*0.85, s_h*0.03)),
        ((s_w*0.05, s_h*0.95), (s_w*0.2, s_h*0.88))
    ]
    
    for (start_x, start_y), (end_x, end_y) in web_attachments:
        pygame.draw.line(sprite_surface, web_color, (start_x, start_y), (end_x, end_y), 1)

    # Spinnerets (web-producing organs at rear)
    spinneret_center = (abdomen_rect.right - s_w*0.02, abdomen_rect.bottom - s_h*0.03)
    pygame.draw.circle(sprite_surface, shadow_color, spinneret_center, 2)
    pygame.draw.circle(sprite_surface, (60, 40, 25), spinneret_center, 1)

    return sprite_surface


def get_spider_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a Spider icon for map editor."""
    icon_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Spider with legs and abdomen
    body_color = (60, 40, 20)       # Dark brown body
    leg_color = (80, 60, 40)        # Lighter brown legs
    eye_color = (255, 0, 0)         # Red eyes

    # Main body
    pygame.draw.ellipse(icon_surface, body_color,
        (s_w*0.35, s_h*0.4, s_w*0.2, s_h*0.2))

    # Abdomen
    pygame.draw.ellipse(icon_surface, body_color,
        (s_w*0.5, s_h*0.35, s_w*0.25, s_h*0.3))

    # Simple legs (4 visible)
    leg_thickness = 2
    # Left legs
    pygame.draw.line(icon_surface, leg_color,
        (s_w*0.35, s_h*0.45), (s_w*0.15, s_h*0.3), leg_thickness)
    pygame.draw.line(icon_surface, leg_color,
        (s_w*0.35, s_h*0.55), (s_w*0.15, s_h*0.7), leg_thickness)
    # Right legs
    pygame.draw.line(icon_surface, leg_color,
        (s_w*0.55, s_h*0.45), (s_w*0.85, s_h*0.3), leg_thickness)
    pygame.draw.line(icon_surface, leg_color,
        (s_w*0.55, s_h*0.55), (s_w*0.85, s_h*0.7), leg_thickness)

    # Eyes
    pygame.draw.circle(icon_surface, eye_color, (int(s_w*0.38), int(s_h*0.42)), 2)
    pygame.draw.circle(icon_surface, eye_color, (int(s_w*0.42), int(s_h*0.42)), 2)

    return icon_surface
