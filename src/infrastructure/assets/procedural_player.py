"""
Procedural Player Asset Generation

This module contains functions to procedurally generate player character sprites using Pygame.
This is the ONLY place in the codebase where player visual assets are created.
"""

import pygame
from typing import <PERSON><PERSON>, Dict, Optional


def get_base_player_sprite(size: Tuple[int, int] = (128, 128)) -> pygame.Surface:
    """Generate detailed base human character sprite for equipment layering."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Enhanced color palette
    skin_color = (255, 220, 177)        # Warm peach skin
    skin_shadow = (235, 200, 157)       # Darker skin for shadows
    skin_highlight = (255, 235, 195)    # Lighter skin for highlights
    hair_color = (139, 69, 19)          # Rich brown hair
    hair_highlight = (160, 85, 35)      # Hair highlights
    hair_shadow = (120, 55, 15)         # Hair shadows
    underwear_color = (240, 240, 240)   # Light gray underwear
    underwear_shadow = (220, 220, 220)  # Underwear shadows
    eye_color = (70, 130, 180)          # Blue eyes
    eyebrow_color = (100, 50, 25)       # Darker brown eyebrows
    lip_color = (200, 120, 120)         # Natural lip color

    # Head with realistic proportions and features
    head_center = (s_w // 2, int(s_h * 0.18))
    head_radius = int(s_w * 0.09)
    
    # Head base with shading
    pygame.draw.circle(sprite_surface, skin_color, head_center, head_radius)
    pygame.draw.circle(sprite_surface, skin_shadow, 
                      (head_center[0] + 2, head_center[1] + 2), head_radius - 2)
    pygame.draw.circle(sprite_surface, skin_color, 
                      (head_center[0] - 1, head_center[1] - 1), head_radius - 3)
    pygame.draw.circle(sprite_surface, skin_highlight, 
                      (head_center[0] - 3, head_center[1] - 3), head_radius - 6)

    # Detailed hair with texture and layers
    hair_main = pygame.Rect(head_center[0] - int(head_radius * 0.9), head_center[1] - head_radius + 2,
                           int(head_radius * 1.8), int(head_radius * 1.1))
    pygame.draw.ellipse(sprite_surface, hair_color, hair_main)
    
    # Hair highlights and texture
    hair_highlight_rect = pygame.Rect(hair_main.left + 4, hair_main.top + 3, 
                                     hair_main.width - 8, hair_main.height // 3)
    pygame.draw.ellipse(sprite_surface, hair_highlight, hair_highlight_rect)
    
    # Hair shadow areas
    hair_shadow_rect = pygame.Rect(hair_main.left + 2, hair_main.bottom - 8, 
                                  hair_main.width - 4, 6)
    pygame.draw.ellipse(sprite_surface, hair_shadow, hair_shadow_rect)
    
    # Individual hair strands for texture
    for i in range(5):
        strand_x = hair_main.left + 5 + i * 4
        strand_y1 = hair_main.top + 5
        strand_y2 = hair_main.top + 15
        pygame.draw.line(sprite_surface, hair_shadow, (strand_x, strand_y1), (strand_x, strand_y2), 1)

    # Detailed facial features
    # Eyes with proper anatomy
    eye_left = (head_center[0] - int(s_w * 0.02), head_center[1] - int(s_h * 0.01))
    eye_right = (head_center[0] + int(s_w * 0.02), head_center[1] - int(s_h * 0.01))
    eye_width = int(s_w * 0.015)
    eye_height = int(s_h * 0.01)
    
    # Eye whites
    eye_left_rect = pygame.Rect(eye_left[0] - eye_width, eye_left[1] - eye_height//2, 
                               eye_width * 2, eye_height)
    eye_right_rect = pygame.Rect(eye_right[0] - eye_width, eye_right[1] - eye_height//2, 
                                eye_width * 2, eye_height)
    pygame.draw.ellipse(sprite_surface, (255, 255, 255), eye_left_rect)
    pygame.draw.ellipse(sprite_surface, (255, 255, 255), eye_right_rect)
    
    # Irises
    iris_radius = int(eye_width * 0.7)
    pygame.draw.circle(sprite_surface, eye_color, eye_left, iris_radius)
    pygame.draw.circle(sprite_surface, eye_color, eye_right, iris_radius)
    
    # Pupils
    pupil_radius = int(iris_radius * 0.5)
    pygame.draw.circle(sprite_surface, (0, 0, 0), eye_left, pupil_radius)
    pygame.draw.circle(sprite_surface, (0, 0, 0), eye_right, pupil_radius)
    
    # Eye highlights
    pygame.draw.circle(sprite_surface, (255, 255, 255), 
                      (eye_left[0] - 1, eye_left[1] - 1), 2)
    pygame.draw.circle(sprite_surface, (255, 255, 255), 
                      (eye_right[0] - 1, eye_right[1] - 1), 2)

    # Eyebrows
    eyebrow_left = pygame.Rect(eye_left[0] - 6, eye_left[1] - 8, 12, 3)
    eyebrow_right = pygame.Rect(eye_right[0] - 6, eye_right[1] - 8, 12, 3)
    pygame.draw.ellipse(sprite_surface, eyebrow_color, eyebrow_left)
    pygame.draw.ellipse(sprite_surface, eyebrow_color, eyebrow_right)

    # Nose (simple but defined)
    nose_tip = (head_center[0], head_center[1] + int(s_h * 0.015))
    pygame.draw.circle(sprite_surface, skin_shadow, nose_tip, 2)
    nose_bridge = pygame.Rect(nose_tip[0] - 1, nose_tip[1] - 6, 2, 6)
    pygame.draw.rect(sprite_surface, skin_shadow, nose_bridge)

    # Mouth with natural lips
    mouth_center = (head_center[0], head_center[1] + int(s_h * 0.035))
    mouth_rect = pygame.Rect(mouth_center[0] - 6, mouth_center[1] - 2, 12, 4)
    pygame.draw.ellipse(sprite_surface, lip_color, mouth_rect)
    
    # Subtle mouth line
    pygame.draw.line(sprite_surface, skin_shadow, 
                    (mouth_center[0] - 4, mouth_center[1]), 
                    (mouth_center[0] + 4, mouth_center[1]), 1)

    # Defined jawline
    jaw_points = [
        (head_center[0] - int(head_radius * 0.7), head_center[1] + int(head_radius * 0.6)),
        (head_center[0], head_center[1] + int(head_radius * 0.8)),
        (head_center[0] + int(head_radius * 0.7), head_center[1] + int(head_radius * 0.6))
    ]
    for i in range(len(jaw_points) - 1):
        pygame.draw.line(sprite_surface, skin_shadow, jaw_points[i], jaw_points[i + 1], 1)

    # Neck with proper anatomy
    neck_rect = pygame.Rect(int(s_w * 0.45), int(s_h * 0.25), int(s_w * 0.1), int(s_h * 0.08))
    pygame.draw.rect(sprite_surface, skin_color, neck_rect)
    pygame.draw.rect(sprite_surface, skin_shadow, 
                    (neck_rect.left + 1, neck_rect.top + 2, neck_rect.width - 2, neck_rect.height - 4))
    
    # Adam's apple (subtle)
    adam_apple = (neck_rect.centerx, neck_rect.centery + 2)
    pygame.draw.circle(sprite_surface, skin_shadow, adam_apple, 2)

    # Torso with realistic proportions and muscle definition
    torso_rect = pygame.Rect(int(s_w * 0.33), int(s_h * 0.32), int(s_w * 0.34), int(s_h * 0.38))
    pygame.draw.rect(sprite_surface, skin_color, torso_rect)
    
    # Chest muscle definition
    chest_left = pygame.Rect(torso_rect.left + 4, torso_rect.top + 6, 
                            int(torso_rect.width * 0.35), int(torso_rect.height * 0.3))
    chest_right = pygame.Rect(torso_rect.right - 4 - int(torso_rect.width * 0.35), torso_rect.top + 6,
                             int(torso_rect.width * 0.35), int(torso_rect.height * 0.3))
    pygame.draw.ellipse(sprite_surface, skin_highlight, chest_left)
    pygame.draw.ellipse(sprite_surface, skin_highlight, chest_right)
    
    # Abdominal definition
    abs_rect = pygame.Rect(torso_rect.centerx - 8, torso_rect.top + int(torso_rect.height * 0.4),
                          16, int(torso_rect.height * 0.5))
    pygame.draw.rect(sprite_surface, skin_shadow, abs_rect)
    
    # Individual ab segments
    for i in range(3):
        ab_segment = pygame.Rect(torso_rect.centerx - 6, 
                                torso_rect.top + int(torso_rect.height * 0.45) + i * 8,
                                12, 4)
        pygame.draw.ellipse(sprite_surface, skin_highlight, ab_segment)

    # Shoulders with proper anatomy
    shoulder_left = (int(s_w * 0.28), int(s_h * 0.34))
    shoulder_right = (int(s_w * 0.72), int(s_h * 0.34))
    shoulder_radius = int(s_w * 0.05)
    pygame.draw.circle(sprite_surface, skin_color, shoulder_left, shoulder_radius)
    pygame.draw.circle(sprite_surface, skin_color, shoulder_right, shoulder_radius)
    pygame.draw.circle(sprite_surface, skin_highlight, 
                      (shoulder_left[0] - 2, shoulder_left[1] - 2), shoulder_radius - 3)
    pygame.draw.circle(sprite_surface, skin_highlight, 
                      (shoulder_right[0] + 2, shoulder_right[1] - 2), shoulder_radius - 3)

    # Enhanced arms with muscle definition
    # Left arm (upper and lower)
    left_upper_arm = pygame.Rect(int(s_w * 0.24), int(s_h * 0.38), int(s_w * 0.08), int(s_h * 0.15))
    left_lower_arm = pygame.Rect(int(s_w * 0.24), int(s_h * 0.53), int(s_w * 0.07), int(s_h * 0.15))
    pygame.draw.rect(sprite_surface, skin_color, left_upper_arm)
    pygame.draw.rect(sprite_surface, skin_color, left_lower_arm)
    
    # Muscle definition on left arm
    bicep_highlight = pygame.Rect(left_upper_arm.left + 1, left_upper_arm.top + 2,
                                 left_upper_arm.width - 2, left_upper_arm.height // 2)
    pygame.draw.rect(sprite_surface, skin_highlight, bicep_highlight)
    
    # Right arm (upper and lower)
    right_upper_arm = pygame.Rect(int(s_w * 0.68), int(s_h * 0.38), int(s_w * 0.08), int(s_h * 0.15))
    right_lower_arm = pygame.Rect(int(s_w * 0.69), int(s_h * 0.53), int(s_w * 0.07), int(s_h * 0.15))
    pygame.draw.rect(sprite_surface, skin_color, right_upper_arm)
    pygame.draw.rect(sprite_surface, skin_color, right_lower_arm)
    
    # Muscle definition on right arm
    bicep_highlight_right = pygame.Rect(right_upper_arm.left + 1, right_upper_arm.top + 2,
                                       right_upper_arm.width - 2, right_upper_arm.height // 2)
    pygame.draw.rect(sprite_surface, skin_highlight, bicep_highlight_right)

    # Hands with finger definition
    left_hand = pygame.Rect(int(s_w * 0.24), int(s_h * 0.68), int(s_w * 0.07), int(s_h * 0.08))
    right_hand = pygame.Rect(int(s_w * 0.69), int(s_h * 0.68), int(s_w * 0.07), int(s_h * 0.08))
    pygame.draw.ellipse(sprite_surface, skin_color, left_hand)
    pygame.draw.ellipse(sprite_surface, skin_color, right_hand)
    
    # Finger details
    for i in range(4):
        finger_left = pygame.Rect(left_hand.left + 1 + i * 2, left_hand.bottom - 2, 1, 4)
        finger_right = pygame.Rect(right_hand.left + 1 + i * 2, right_hand.bottom - 2, 1, 4)
        pygame.draw.rect(sprite_surface, skin_shadow, finger_left)
        pygame.draw.rect(sprite_surface, skin_shadow, finger_right)

    # Basic underwear/undergarments
    underwear_rect = pygame.Rect(int(s_w * 0.38), int(s_h * 0.66), int(s_w * 0.24), int(s_h * 0.08))
    pygame.draw.rect(sprite_surface, underwear_color, underwear_rect)
    pygame.draw.rect(sprite_surface, underwear_shadow, 
                    (underwear_rect.left + 1, underwear_rect.bottom - 2, underwear_rect.width - 2, 2))
    
    # Waistband detail
    waistband = pygame.Rect(underwear_rect.left, underwear_rect.top, underwear_rect.width, 3)
    pygame.draw.rect(sprite_surface, underwear_shadow, waistband)

    # Enhanced legs with muscle definition
    # Left leg (upper and lower)
    left_upper_leg = pygame.Rect(int(s_w * 0.38), int(s_h * 0.74), int(s_w * 0.1), int(s_h * 0.15))
    left_lower_leg = pygame.Rect(int(s_w * 0.39), int(s_h * 0.89), int(s_w * 0.08), int(s_h * 0.15))
    pygame.draw.rect(sprite_surface, skin_color, left_upper_leg)
    pygame.draw.rect(sprite_surface, skin_color, left_lower_leg)
    
    # Right leg (upper and lower)
    right_upper_leg = pygame.Rect(int(s_w * 0.52), int(s_h * 0.74), int(s_w * 0.1), int(s_h * 0.15))
    right_lower_leg = pygame.Rect(int(s_w * 0.53), int(s_h * 0.89), int(s_w * 0.08), int(s_h * 0.15))
    pygame.draw.rect(sprite_surface, skin_color, right_upper_leg)
    pygame.draw.rect(sprite_surface, skin_color, right_lower_leg)
    
    # Thigh muscle definition
    left_thigh_highlight = pygame.Rect(left_upper_leg.left + 1, left_upper_leg.top + 1,
                                      left_upper_leg.width - 2, left_upper_leg.height // 2)
    right_thigh_highlight = pygame.Rect(right_upper_leg.left + 1, right_upper_leg.top + 1,
                                       right_upper_leg.width - 2, right_upper_leg.height // 2)
    pygame.draw.rect(sprite_surface, skin_highlight, left_thigh_highlight)
    pygame.draw.rect(sprite_surface, skin_highlight, right_thigh_highlight)
    
    # Knee definition
    left_knee = (left_upper_leg.centerx, left_upper_leg.bottom)
    right_knee = (right_upper_leg.centerx, right_upper_leg.bottom)
    pygame.draw.circle(sprite_surface, skin_shadow, left_knee, 3)
    pygame.draw.circle(sprite_surface, skin_shadow, right_knee, 3)
    
    # Calf muscle definition
    left_calf = pygame.Rect(left_lower_leg.left + 1, left_lower_leg.top + 2,
                           left_lower_leg.width - 2, left_lower_leg.height // 2)
    right_calf = pygame.Rect(right_lower_leg.left + 1, right_lower_leg.top + 2,
                            right_lower_leg.width - 2, right_lower_leg.height // 2)
    pygame.draw.ellipse(sprite_surface, skin_highlight, left_calf)
    pygame.draw.ellipse(sprite_surface, skin_highlight, right_calf)

    # Enhanced feet with toe definition
    left_foot = pygame.Rect(int(s_w * 0.37), int(s_h * 1.04), int(s_w * 0.1), int(s_h * 0.06))
    right_foot = pygame.Rect(int(s_w * 0.53), int(s_h * 1.04), int(s_w * 0.1), int(s_h * 0.06))
    pygame.draw.ellipse(sprite_surface, skin_color, left_foot)
    pygame.draw.ellipse(sprite_surface, skin_color, right_foot)
    
    # Toe details
    for i in range(5):
        toe_left = (left_foot.left + 1 + i * 2, left_foot.top + 2)
        toe_right = (right_foot.left + 1 + i * 2, right_foot.top + 2)
        pygame.draw.circle(sprite_surface, skin_shadow, toe_left, 1)
        pygame.draw.circle(sprite_surface, skin_shadow, toe_right, 1)
    
    # Ankle definition
    left_ankle = (left_lower_leg.centerx, left_lower_leg.bottom)
    right_ankle = (right_lower_leg.centerx, right_lower_leg.bottom)
    pygame.draw.circle(sprite_surface, skin_shadow, left_ankle, 2)
    pygame.draw.circle(sprite_surface, skin_shadow, right_ankle, 2)

    return sprite_surface


def get_helmet_layer(size: Tuple[int, int] = (128, 128), helmet_type: str = "leather") -> pygame.Surface:
    """Generate helmet layer for the player sprite."""
    layer_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size
    
    head_center = (s_w // 2, int(s_h * 0.18))
    head_radius = int(s_w * 0.09)
    
    if helmet_type == "leather":
        helmet_color = (139, 69, 19)  # Brown leather
        # Simple leather cap
        helmet_rect = pygame.Rect(head_center[0] - head_radius, head_center[1] - head_radius,
                                 head_radius * 2, int(head_radius * 1.2))
        pygame.draw.ellipse(layer_surface, helmet_color, helmet_rect)
    
    elif helmet_type == "iron":
        helmet_color = (169, 169, 169)  # Iron gray
        # Metal helmet with visor
        helmet_rect = pygame.Rect(head_center[0] - head_radius, head_center[1] - head_radius,
                                 head_radius * 2, int(head_radius * 1.3))
        pygame.draw.ellipse(layer_surface, helmet_color, helmet_rect)
        
        # Visor
        visor_rect = pygame.Rect(head_center[0] - int(head_radius * 0.8), head_center[1] - int(head_radius * 0.2),
                                int(head_radius * 1.6), int(head_radius * 0.4))
        pygame.draw.rect(layer_surface, (100, 100, 100), visor_rect)
    
    return layer_surface


def get_armor_layer(size: Tuple[int, int] = (128, 128), armor_type: str = "leather") -> pygame.Surface:
    """Generate armor layer for the player sprite."""
    layer_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size
    
    torso_rect = pygame.Rect(int(s_w * 0.33), int(s_h * 0.32), int(s_w * 0.34), int(s_h * 0.38))
    
    if armor_type == "leather":
        armor_color = (139, 69, 19)  # Brown leather
        # Leather vest
        pygame.draw.rect(layer_surface, armor_color, torso_rect)
        
        # Leather straps
        for i in range(3):
            strap_y = torso_rect.top + 10 + i * 12
            pygame.draw.line(layer_surface, (100, 50, 25), 
                           (torso_rect.left + 5, strap_y), (torso_rect.right - 5, strap_y), 2)
    
    elif armor_type == "chainmail":
        armor_color = (169, 169, 169)  # Silver chainmail
        pygame.draw.rect(layer_surface, armor_color, torso_rect)
        
        # Chainmail texture
        for row in range(5):
            for col in range(6):
                ring_x = torso_rect.left + 5 + col * 5
                ring_y = torso_rect.top + 5 + row * 6
                pygame.draw.circle(layer_surface, (120, 120, 120), (ring_x, ring_y), 2, 1)
    
    return layer_surface


def get_weapon_layer(size: Tuple[int, int] = (128, 128), weapon_type: str = "sword") -> pygame.Surface:
    """Generate weapon layer for the player sprite."""
    layer_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size
    
    if weapon_type == "sword":
        # Sword in right hand
        sword_blade = pygame.Rect(int(s_w * 0.75), int(s_h * 0.3), int(s_w * 0.04), int(s_h * 0.3))
        sword_hilt = pygame.Rect(int(s_w * 0.73), int(s_h * 0.6), int(s_w * 0.08), int(s_h * 0.05))
        
        pygame.draw.rect(layer_surface, (220, 220, 220), sword_blade)  # Bright steel
        pygame.draw.rect(layer_surface, (139, 69, 19), sword_hilt)     # Brown leather grip
        
        # Crossguard
        guard_rect = pygame.Rect(int(s_w * 0.7), int(s_h * 0.58), int(s_w * 0.14), int(s_h * 0.02))
        pygame.draw.rect(layer_surface, (192, 192, 192), guard_rect)
    
    elif weapon_type == "bow":
        # Bow in left hand
        bow_string = pygame.Rect(int(s_w * 0.2), int(s_h * 0.4), int(s_w * 0.02), int(s_h * 0.25))
        pygame.draw.rect(layer_surface, (139, 69, 19), bow_string)  # Wood bow
        
        # Bow string
        pygame.draw.line(layer_surface, (200, 200, 200), 
                        (int(s_w * 0.21), int(s_h * 0.4)), 
                        (int(s_w * 0.21), int(s_h * 0.65)), 1)
    
    return layer_surface


def create_layered_player_sprite(size: Tuple[int, int] = (128, 128), 
                                helmet: str = None, 
                                armor: str = None, 
                                weapon: str = None) -> pygame.Surface:
    """Create a complete player sprite with equipment layers."""
    final_sprite = get_base_player_sprite(size)
    
    if armor:
        armor_layer = get_armor_layer(size, armor)
        final_sprite.blit(armor_layer, (0, 0))
    
    if helmet:
        helmet_layer = get_helmet_layer(size, helmet)
        final_sprite.blit(helmet_layer, (0, 0))
    
    if weapon:
        weapon_layer = get_weapon_layer(size, weapon)
        final_sprite.blit(weapon_layer, (0, 0))
    
    return final_sprite


def get_player_sprite(size: Tuple[int, int] = (128, 128)) -> pygame.Surface:
    """Generate basic player character sprite (for backward compatibility)."""
    # For backward compatibility, return base player with basic clothing
    equipment_ids = {
        "head_equipment": "cloth_cap",
        "chest_equipment": "cloth_shirt", 
        "legs_equipment": "cloth_pants",
        "boots_equipment": "simple_shoes",
        "main_hand_weapon": "rusty_sword",
        "off_hand_equipment": None
    }
    return get_equipped_player_sprite(equipment_ids, size)


def get_equipped_player_sprite(equipment_ids: Dict[str, Optional[str]], size: Tuple[int, int] = (128, 128)) -> pygame.Surface:
    """Generate player sprite with equipment layered on top."""
    # For now, delegate to the original implementation in procedural_items
    # This maintains compatibility while we gradually migrate functionality
    from .procedural_items import get_equipped_player_sprite as _get_equipped_player_sprite
    return _get_equipped_player_sprite(equipment_ids, size)
