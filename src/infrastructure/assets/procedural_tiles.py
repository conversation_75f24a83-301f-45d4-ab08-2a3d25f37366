"""
Procedural Tile Asset Generation

This module contains functions to procedurally generate tile sprites using Pygame.
"""

import pygame
import random
import math
from typing import <PERSON><PERSON>


def get_stone_wall_tile(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """
    Generate a detailed stone wall tile procedurally.
    
    Creates a realistic medieval stone wall with:
    - Varied stone block sizes and shapes
    - Realistic mortar joints
    - Stone texture and weathering
    - Proper lighting and shadows
    - Depth and dimension
    """
    tile_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size
    
    # Expanded color palette for realism
    base_stone_color = (140, 135, 130)      # Warm gray base
    light_stone_color = (160, 155, 150)     # Lighter stone variation
    dark_stone_color = (110, 105, 100)      # Darker stone variation
    mortar_color = (85, 80, 75)             # Dark mortar
    mortar_highlight = (105, 100, 95)       # Lighter mortar
    stone_highlight = (180, 175, 170)       # Stone highlights
    stone_shadow = (90, 85, 80)             # Stone shadows
    crack_color = (70, 65, 60)              # Cracks and wear
    
    # Fill base with mortar color
    tile_surface.fill(mortar_color)
    
    # Create irregular stone blocks with realistic proportions
    random.seed(42)  # Consistent randomness for tiling
    
    # Define stone block layout with varied sizes
    stones = [
        # Row 1 (top) - alternating pattern
        {'rect': pygame.Rect(int(s_w*0.05), int(s_h*0.05), int(s_w*0.4), int(s_h*0.25)), 'color_var': 0},
        {'rect': pygame.Rect(int(s_w*0.5), int(s_h*0.05), int(s_w*0.45), int(s_h*0.2)), 'color_var': 1},
        
        # Row 2 (middle) - offset pattern
        {'rect': pygame.Rect(int(s_w*0.05), int(s_h*0.35), int(s_w*0.5), int(s_h*0.3)), 'color_var': 2},
        {'rect': pygame.Rect(int(s_w*0.6), int(s_h*0.3), int(s_w*0.35), int(s_h*0.25)), 'color_var': 0},
        
        # Row 3 (bottom) - different sizes
        {'rect': pygame.Rect(int(s_w*0.05), int(s_h*0.7), int(s_w*0.35), int(s_h*0.25)), 'color_var': 1},
        {'rect': pygame.Rect(int(s_w*0.45), int(s_h*0.6), int(s_w*0.25), int(s_h*0.35)), 'color_var': 2},
        {'rect': pygame.Rect(int(s_w*0.75), int(s_h*0.7), int(s_w*0.2), int(s_h*0.25)), 'color_var': 0},
    ]
    
    # Draw each stone with detailed shading and texture
    for stone in stones:
        rect = stone['rect']
        color_variation = stone['color_var']
        
        # Choose stone color variation
        if color_variation == 0:
            main_color = base_stone_color
        elif color_variation == 1:
            main_color = light_stone_color
        else:
            main_color = dark_stone_color
        
        # Draw main stone block
        pygame.draw.rect(tile_surface, main_color, rect)
        
        # Add stone texture with small random spots
        for _ in range(8):
            tex_x = rect.left + random.randint(1, rect.width - 2)
            tex_y = rect.top + random.randint(1, rect.height - 2)
            tex_color = (main_color[0] + random.randint(-15, 15),
                        main_color[1] + random.randint(-15, 15),
                        main_color[2] + random.randint(-15, 15))
            # Clamp color values
            tex_color = tuple(max(0, min(255, c)) for c in tex_color)
            pygame.draw.circle(tile_surface, tex_color, (tex_x, tex_y), 1)
        
        # Add 3D effect with highlights and shadows
        # Top and left highlights (light source from top-left)
        pygame.draw.line(tile_surface, stone_highlight, 
                        (rect.left, rect.top), (rect.right - 1, rect.top), 2)
        pygame.draw.line(tile_surface, stone_highlight, 
                        (rect.left, rect.top), (rect.left, rect.bottom - 1), 2)
        
        # Bottom and right shadows
        pygame.draw.line(tile_surface, stone_shadow, 
                        (rect.left + 1, rect.bottom - 1), (rect.right - 1, rect.bottom - 1), 2)
        pygame.draw.line(tile_surface, stone_shadow, 
                        (rect.right - 1, rect.top + 1), (rect.right - 1, rect.bottom - 1), 2)
        
        # Inner highlight for more depth
        inner_rect = pygame.Rect(rect.left + 2, rect.top + 2, rect.width - 4, rect.height - 4)
        if inner_rect.width > 0 and inner_rect.height > 0:
            pygame.draw.line(tile_surface, stone_highlight, 
                            (inner_rect.left, inner_rect.top), 
                            (inner_rect.left + inner_rect.width // 3, inner_rect.top), 1)
            pygame.draw.line(tile_surface, stone_highlight, 
                            (inner_rect.left, inner_rect.top), 
                            (inner_rect.left, inner_rect.top + inner_rect.height // 3), 1)
    
    # Add weathering details - cracks and wear marks
    random.seed(84)  # Different seed for weathering
    for _ in range(5):
        crack_x = random.randint(int(s_w*0.1), int(s_w*0.9))
        crack_y = random.randint(int(s_h*0.1), int(s_h*0.9))
        crack_length = random.randint(3, 8)
        
        # Random crack direction
        if random.choice([True, False]):
            # Horizontal crack
            pygame.draw.line(tile_surface, crack_color, 
                            (crack_x, crack_y), 
                            (crack_x + crack_length, crack_y), 1)
        else:
            # Vertical crack
            pygame.draw.line(tile_surface, crack_color, 
                            (crack_x, crack_y), 
                            (crack_x, crack_y + crack_length), 1)
    
    # Enhance mortar joints with realistic depth
    # Add subtle highlights to mortar to show depth
    for stone in stones:
        rect = stone['rect']
        # Draw mortar highlight on the "raised" edges
        if rect.left > 2:
            pygame.draw.line(tile_surface, mortar_highlight, 
                            (rect.left - 1, rect.top), (rect.left - 1, rect.bottom), 1)
        if rect.top > 2:
            pygame.draw.line(tile_surface, mortar_highlight, 
                            (rect.left, rect.top - 1), (rect.right, rect.top - 1), 1)
    
    return tile_surface


def get_dirt_floor_tile(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """
    Generate a detailed dirt floor tile procedurally.
    
    Creates a realistic dirt ground with:
    - Varied soil composition and moisture levels
    - Small rocks, pebbles, and debris
    - Organic matter and decomposing leaves
    - Ant trails and small insect activity
    - Subtle color variations for natural appearance
    - Tileable seamless edges
    """
    tile_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size
    
    # Expanded color palette for realistic dirt
    dirt_base = (139, 69, 19)            # Saddle brown base
    dirt_dark = (101, 67, 33)            # Dark brown shadows
    dirt_light = (160, 82, 45)           # Light brown highlights
    dirt_red = (120, 50, 30)             # Reddish clay dirt
    dirt_yellow = (150, 95, 55)          # Sandy dirt mixture
    
    # Natural elements
    rock_color = (105, 105, 105)         # Small rocks
    rock_dark = (75, 75, 75)             # Dark rock shadows
    pebble_color = (128, 128, 128)       # Small pebbles
    organic_color = (90, 45, 25)         # Decomposing organic matter
    organic_dark = (70, 35, 20)          # Dark organic matter
    leaf_color = (110, 60, 30)           # Dried leaves
    ant_color = (60, 30, 15)             # Ant trails
    
    # Fill base with dirt
    tile_surface.fill(dirt_base)
    
    # Create natural dirt texture base
    import random
    random.seed(42)  # Consistent randomness for tiling
    
    # Layer 1: Soil composition variations
    for _ in range(15):
        patch_x = random.randint(0, s_w - 6)
        patch_y = random.randint(0, s_h - 6)
        patch_size = random.randint(4, 8)
        soil_type = random.choice([dirt_dark, dirt_light, dirt_red, dirt_yellow])
        
        # Create organic soil patches
        for _ in range(patch_size):
            soil_x = patch_x + random.randint(-3, 3)
            soil_y = patch_y + random.randint(-3, 3)
            if 0 <= soil_x < s_w and 0 <= soil_y < s_h:
                pygame.draw.circle(tile_surface, soil_type, (soil_x, soil_y), 1)
    
    # Layer 2: Small rocks and pebbles
    for _ in range(8):
        rock_x = random.randint(1, s_w - 2)
        rock_y = random.randint(1, s_h - 2)
        rock_size = random.randint(2, 4)
        
        # Draw main rock
        pygame.draw.circle(tile_surface, rock_color, (rock_x, rock_y), rock_size)
        
        # Add shadow/depth
        pygame.draw.circle(tile_surface, rock_dark, (rock_x + 1, rock_y + 1), rock_size - 1)
        
        # Add small pebbles around rocks
        for _ in range(random.randint(2, 4)):
            pebble_x = rock_x + random.randint(-6, 6)
            pebble_y = rock_y + random.randint(-6, 6)
            if 0 <= pebble_x < s_w and 0 <= pebble_y < s_h:
                pygame.draw.circle(tile_surface, pebble_color, (pebble_x, pebble_y), 1)
    
    # Layer 3: Organic matter and decomposing material
    for _ in range(12):
        organic_x = random.randint(1, s_w - 2)
        organic_y = random.randint(1, s_h - 2)
        organic_size = random.randint(2, 5)
        organic_shade = random.choice([organic_color, organic_dark])
        
        # Draw irregular organic patches
        for _ in range(organic_size):
            matter_x = organic_x + random.randint(-2, 2)
            matter_y = organic_y + random.randint(-2, 2)
            if 0 <= matter_x < s_w and 0 <= matter_y < s_h:
                pygame.draw.circle(tile_surface, organic_shade, (matter_x, matter_y), 1)
    
    # Layer 4: Dried leaves and plant debris
    for _ in range(6):
        leaf_x = random.randint(2, s_w - 3)
        leaf_y = random.randint(2, s_h - 3)
        
        # Draw small leaf shapes
        leaf_points = [
            (leaf_x, leaf_y - 2),
            (leaf_x + 2, leaf_y - 1),
            (leaf_x + 1, leaf_y + 1),
            (leaf_x - 1, leaf_y + 1),
            (leaf_x - 2, leaf_y - 1)
        ]
        pygame.draw.polygon(tile_surface, leaf_color, leaf_points)
        
        # Add leaf detail line
        pygame.draw.line(tile_surface, organic_dark, (leaf_x - 1, leaf_y), (leaf_x + 1, leaf_y), 1)
    
    # Layer 5: Ant trails and insect activity
    for _ in range(3):
        trail_start_x = random.randint(0, s_w - 1)
        trail_start_y = random.randint(0, s_h - 1)
        trail_length = random.randint(8, 15)
        trail_angle = random.uniform(0, math.pi * 2)
        
        # Draw winding ant trail
        for step in range(trail_length):
            step_progress = step / trail_length
            # Add natural curve to trail
            curve_offset = math.sin(step_progress * math.pi * 4) * 2
            
            trail_x = trail_start_x + int(math.cos(trail_angle) * step + curve_offset)
            trail_y = trail_start_y + int(math.sin(trail_angle) * step)
            
            if 0 <= trail_x < s_w and 0 <= trail_y < s_h:
                # Make trail fade as it progresses
                trail_alpha = int(255 * (1 - step_progress * 0.5))
                if trail_alpha > 50:
                    pygame.draw.circle(tile_surface, ant_color, (trail_x, trail_y), 1)
    
    # Layer 6: Fine dirt texture and particles
    for _ in range(40):
        particle_x = random.randint(0, s_w - 1)
        particle_y = random.randint(0, s_h - 1)
        particle_shade = random.choice([dirt_dark, dirt_light, dirt_base])
        
        # Add fine texture particles
        if random.random() < 0.4:  # 40% chance for texture
            pygame.draw.circle(tile_surface, particle_shade, (particle_x, particle_y), 1)
    
    # Layer 7: Moisture variations and compaction
    for _ in range(6):
        moisture_x = random.randint(2, s_w - 3)
        moisture_y = random.randint(2, s_h - 3)
        moisture_size = random.randint(3, 6)
        
        # Darker areas suggest moisture or compaction
        for _ in range(moisture_size):
            moist_x = moisture_x + random.randint(-2, 2)
            moist_y = moisture_y + random.randint(-2, 2)
            if 0 <= moist_x < s_w and 0 <= moist_y < s_h:
                pygame.draw.circle(tile_surface, dirt_dark, (moist_x, moist_y), 1)
    
    # Layer 8: Tiny insects and life activity (very subtle)
    for _ in range(2):
        bug_x = random.randint(2, s_w - 3)
        bug_y = random.randint(2, s_h - 3)
        
        # Draw tiny insects as small dark dots
        if random.random() < 0.4:  # 40% chance to show
            pygame.draw.circle(tile_surface, (40, 20, 10), (bug_x, bug_y), 1)
    
    return tile_surface


def get_sand_floor_tile(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """
    Generate a detailed sand floor tile procedurally.
    
    Creates realistic sand with:
    - Varied grain sizes and sand types
    - Wind patterns and ripples
    - Small shells and debris
    - Moisture variations and compaction
    - Subtle color transitions
    - Tileable seamless edges
    """
    tile_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Expanded color palette for realistic sand
    sand_base = (238, 203, 173)         # Peach puff base
    sand_dark = (205, 175, 149)         # Darker sand
    sand_light = (255, 228, 196)        # Light sand
    sand_yellow = (255, 218, 185)       # Yellow sand
    sand_white = (250, 240, 230)        # White sand highlights
    
    # Natural elements
    shell_color = (255, 255, 255)       # Small shells
    shell_pink = (255, 192, 203)        # Pink shell tint
    debris_color = (139, 69, 19)        # Small wood debris
    stone_color = (128, 128, 128)       # Small stones
    moisture_color = (180, 150, 120)    # Moist sand
    
    # Fill base with sand
    tile_surface.fill(sand_base)
    
    # Create natural sand texture base
    import random
    random.seed(123)  # Consistent randomness for tiling
    
    # Layer 1: Sand grain variations
    for _ in range(60):
        grain_x = random.randint(0, s_w - 1)
        grain_y = random.randint(0, s_h - 1)
        grain_type = random.choice([sand_dark, sand_light, sand_yellow, sand_white])
        
        # Add individual sand grains
        if random.random() < 0.5:  # 50% chance for grain
            pygame.draw.circle(tile_surface, grain_type, (grain_x, grain_y), 1)
    
    # Layer 2: Wind ripples and patterns
    for ripple in range(4):
        ripple_y = random.randint(int(s_h * 0.1), int(s_h * 0.9))
        ripple_length = random.randint(int(s_w * 0.3), int(s_w * 0.8))
        ripple_start_x = random.randint(0, s_w - ripple_length)
        
        # Create subtle ripple effect
        for x in range(ripple_length):
            ripple_x = ripple_start_x + x
            # Create wave-like ripple
            ripple_height = int(math.sin(x * 0.3) * 2)
            ripple_color = sand_dark if ripple_height < 0 else sand_light
            
            for y_offset in range(abs(ripple_height)):
                ripple_pixel_y = ripple_y + y_offset * (1 if ripple_height > 0 else -1)
                if 0 <= ripple_x < s_w and 0 <= ripple_pixel_y < s_h:
                    pygame.draw.circle(tile_surface, ripple_color, (ripple_x, ripple_pixel_y), 1)
    
    # Layer 3: Small shells and marine debris
    for _ in range(5):
        shell_x = random.randint(2, s_w - 3)
        shell_y = random.randint(2, s_h - 3)
        shell_size = random.randint(2, 4)
        shell_shade = random.choice([shell_color, shell_pink])
        
        # Draw small shell shapes
        shell_points = [
            (shell_x, shell_y - 1),
            (shell_x + 1, shell_y),
            (shell_x, shell_y + 1),
            (shell_x - 1, shell_y)
        ]
        pygame.draw.polygon(tile_surface, shell_shade, shell_points)
        
        # Add shell detail
        pygame.draw.circle(tile_surface, sand_dark, (shell_x, shell_y), 1)
    
    # Layer 4: Small stones and pebbles
    for _ in range(8):
        stone_x = random.randint(1, s_w - 2)
        stone_y = random.randint(1, s_h - 2)
        stone_size = random.randint(1, 3)
        
        # Draw small rounded stones
        pygame.draw.circle(tile_surface, stone_color, (stone_x, stone_y), stone_size)
        
        # Add subtle shadow
        pygame.draw.circle(tile_surface, sand_dark, (stone_x + 1, stone_y + 1), stone_size - 1)
    
    # Layer 5: Organic debris (seaweed, small twigs)
    for _ in range(4):
        debris_x = random.randint(2, s_w - 3)
        debris_y = random.randint(2, s_h - 3)
        debris_length = random.randint(3, 6)
        debris_angle = random.uniform(0, math.pi * 2)
        
        # Draw small debris line
        end_x = debris_x + int(math.cos(debris_angle) * debris_length)
        end_y = debris_y + int(math.sin(debris_angle) * debris_length)
        
        if 0 <= end_x < s_w and 0 <= end_y < s_h:
            pygame.draw.line(tile_surface, debris_color, (debris_x, debris_y), (end_x, end_y), 1)
    
    # Layer 6: Moisture variations and compaction
    for _ in range(6):
        moisture_x = random.randint(2, s_w - 3)
        moisture_y = random.randint(2, s_h - 3)
        moisture_size = random.randint(3, 5)
        
        # Darker, more compact sand areas
        for _ in range(moisture_size):
            moist_x = moisture_x + random.randint(-2, 2)
            moist_y = moisture_y + random.randint(-2, 2)
            if 0 <= moist_x < s_w and 0 <= moist_y < s_h:
                pygame.draw.circle(tile_surface, moisture_color, (moist_x, moist_y), 1)
    
    # Layer 7: Fine sand texture and sparkles
    for _ in range(35):
        sparkle_x = random.randint(0, s_w - 1)
        sparkle_y = random.randint(0, s_h - 1)
        
        # Add sand sparkles (quartz, mica)
        if random.random() < 0.3:  # 30% chance for sparkle
            sparkle_color = random.choice([sand_white, sand_light])
            pygame.draw.circle(tile_surface, sparkle_color, (sparkle_x, sparkle_y), 1)
    
    # Layer 8: Footprint traces and disturbances (very subtle)
    for _ in range(3):
        track_x = random.randint(4, s_w - 5)
        track_y = random.randint(4, s_h - 5)
        
        # Small depression suggesting past activity
        if random.random() < 0.4:  # 40% chance for tracks
            for i in range(3):
                depression_x = track_x + i
                depression_y = track_y + random.randint(-1, 1)
                if 0 <= depression_x < s_w and 0 <= depression_y < s_h:
                    pygame.draw.circle(tile_surface, sand_dark, (depression_x, depression_y), 1)
    
    # Layer 9: Sand drift patterns
    for drift in range(3):
        drift_x = random.randint(0, s_w - 8)
        drift_y = random.randint(0, s_h - 1)
        drift_length = random.randint(4, 8)
        
        # Create subtle drift pattern
        for d in range(drift_length):
            drift_pixel_x = drift_x + d
            drift_intensity = math.sin(d * 0.5) * 0.5 + 0.5
            drift_color = sand_light if drift_intensity > 0.5 else sand_base
            
            if 0 <= drift_pixel_x < s_w:
                pygame.draw.circle(tile_surface, drift_color, (drift_pixel_x, drift_y), 1)
    
    return tile_surface


def get_water_tile(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """
    Generate a detailed water tile procedurally.
    
    Creates realistic water with:
    - Multi-layered depth with transparency effects
    - Dynamic wave patterns and ripples
    - Light reflections and caustics
    - Foam and surface tension effects
    - Underwater depth gradient
    - Animated-looking surface disturbances
    """
    tile_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size
    
    # Expanded color palette for realistic water
    deep_water_color = (8, 74, 140)        # Deep ocean blue
    mid_water_color = (16, 109, 156)       # Medium depth blue
    surface_water_color = (32, 137, 189)   # Surface blue
    light_water_color = (64, 164, 223)     # Light blue highlights
    foam_color = (200, 230, 255)           # White foam
    reflection_color = (180, 220, 255)     # Light reflections
    caustic_color = (120, 200, 255)        # Underwater caustics
    shadow_color = (4, 45, 85)             # Deep shadows
    sparkle_color = (255, 255, 255)        # Surface sparkles
    
    # Fill base with deep water
    tile_surface.fill(deep_water_color)
    
    # Create depth layers with gradient effect
    random.seed(42)  # Consistent randomness for tiling
    
    # Layer 1: Deep underwater shadows and variations
    for _ in range(25):
        shadow_x = random.randint(0, s_w - 1)
        shadow_y = random.randint(0, s_h - 1)
        shadow_radius = random.randint(3, 8)
        shadow_intensity = random.randint(20, 60)
        
        # Create soft shadow spots
        shadow_surf = pygame.Surface((shadow_radius * 2, shadow_radius * 2), pygame.SRCALPHA)
        shadow_alpha = max(0, 255 - shadow_intensity * 3)
        pygame.draw.circle(shadow_surf, (*shadow_color, shadow_alpha), 
                          (shadow_radius, shadow_radius), shadow_radius)
        tile_surface.blit(shadow_surf, (shadow_x - shadow_radius, shadow_y - shadow_radius), 
                         special_flags=pygame.BLEND_ALPHA_SDL2)
    
    # Layer 2: Mid-depth water with current flows
    for flow_y in range(0, s_h, 6):
        for flow_x in range(s_w):
            # Create flowing water effect with sine waves
            flow_offset = math.sin(flow_x * 0.4 + flow_y * 0.3) * 3
            current_strength = math.cos(flow_x * 0.2 + flow_y * 0.5) * 2
            
            if (flow_x + int(flow_offset)) % 8 < 5:
                flow_color = mid_water_color
                if current_strength > 0:
                    # Lighter areas for upward currents
                    flow_color = tuple(min(255, c + int(current_strength * 15)) for c in flow_color)
                else:
                    # Darker areas for downward currents
                    flow_color = tuple(max(0, c + int(current_strength * 10)) for c in flow_color)
                
                pygame.draw.circle(tile_surface, flow_color, (flow_x, flow_y), 2)
    
    # Layer 3: Surface water with complex wave patterns
    for wave_layer in range(3):
        wave_offset_x = wave_layer * 2.1
        wave_offset_y = wave_layer * 1.7
        wave_freq_x = 0.3 + wave_layer * 0.1
        wave_freq_y = 0.25 + wave_layer * 0.08
        
        for y in range(0, s_h, 3):
            for x in range(0, s_w, 2):
                # Multi-frequency wave interference
                wave1 = math.sin(x * wave_freq_x + wave_offset_x) * 1.5
                wave2 = math.cos(y * wave_freq_y + wave_offset_y) * 1.2
                wave3 = math.sin((x + y) * 0.15 + wave_layer) * 0.8
                
                combined_wave = wave1 + wave2 + wave3
                
                # Determine wave height and create appropriate color
                if combined_wave > 1.5:
                    # Wave crests - lighter
                    crest_intensity = min(1.0, (combined_wave - 1.5) / 1.5)
                    wave_color = tuple(int(surface_water_color[i] + 
                                          (light_water_color[i] - surface_water_color[i]) * crest_intensity) 
                                     for i in range(3))
                    pygame.draw.circle(tile_surface, wave_color, (x, y), 2)
                elif combined_wave < -1.0:
                    # Wave troughs - darker
                    trough_intensity = min(1.0, abs(combined_wave + 1.0) / 1.5)
                    wave_color = tuple(int(surface_water_color[i] - 
                                          (surface_water_color[i] - deep_water_color[i]) * trough_intensity * 0.3) 
                                     for i in range(3))
                    pygame.draw.circle(tile_surface, wave_color, (x, y), 1)
    
    # Layer 4: Underwater caustics (light patterns)
    random.seed(123)  # Different seed for caustics
    for _ in range(15):
        caustic_x = random.randint(int(s_w * 0.1), int(s_w * 0.9))
        caustic_y = random.randint(int(s_h * 0.1), int(s_h * 0.9))
        
        # Create caustic light patterns
        caustic_points = []
        center_angle = random.random() * math.pi * 2
        for i in range(6):
            angle = center_angle + (i * math.pi / 3) + random.uniform(-0.3, 0.3)
            radius = random.randint(2, 6)
            point_x = caustic_x + int(math.cos(angle) * radius)
            point_y = caustic_y + int(math.sin(angle) * radius)
            caustic_points.append((point_x, point_y))
        
        # Draw subtle caustic lines
        if len(caustic_points) >= 3:
            caustic_surf = pygame.Surface((12, 12), pygame.SRCALPHA)
            adjusted_points = [(p[0] - caustic_x + 6, p[1] - caustic_y + 6) for p in caustic_points[:3]]
            pygame.draw.polygon(caustic_surf, (*caustic_color, 60), adjusted_points)
            tile_surface.blit(caustic_surf, (caustic_x - 6, caustic_y - 6), 
                             special_flags=pygame.BLEND_ADD)
    
    # Layer 5: Surface foam and bubbles
    random.seed(456)  # Different seed for surface effects
    for _ in range(12):
        foam_x = random.randint(0, s_w - 1)
        foam_y = random.randint(0, s_h - 1)
        foam_size = random.randint(1, 3)
        
        # Create foam with varying opacity
        foam_alpha = random.randint(40, 120)
        foam_surf = pygame.Surface((foam_size * 2, foam_size * 2), pygame.SRCALPHA)
        pygame.draw.circle(foam_surf, (*foam_color, foam_alpha), 
                          (foam_size, foam_size), foam_size)
        tile_surface.blit(foam_surf, (foam_x - foam_size, foam_y - foam_size), 
                         special_flags=pygame.BLEND_ALPHA_SDL2)
    
    # Layer 6: Light reflections and sparkles
    random.seed(789)  # Different seed for light effects
    for _ in range(8):
        sparkle_x = random.randint(int(s_w * 0.1), int(s_w * 0.9))
        sparkle_y = random.randint(int(s_h * 0.1), int(s_h * 0.9))
        
        # Create shimmering light reflections
        reflection_strength = random.uniform(0.3, 0.8)
        
        # Main sparkle
        sparkle_surf = pygame.Surface((4, 4), pygame.SRCALPHA)
        sparkle_alpha = int(255 * reflection_strength)
        pygame.draw.circle(sparkle_surf, (*sparkle_color, sparkle_alpha), (2, 2), 2)
        tile_surface.blit(sparkle_surf, (sparkle_x - 2, sparkle_y - 2), 
                         special_flags=pygame.BLEND_ADD)
        
        # Reflection streaks
        streak_length = random.randint(3, 7)
        streak_angle = random.uniform(0, math.pi * 2)
        streak_end_x = sparkle_x + int(math.cos(streak_angle) * streak_length)
        streak_end_y = sparkle_y + int(math.sin(streak_angle) * streak_length)
        
        # Draw reflection streak with fading alpha
        for i in range(streak_length):
            streak_alpha = int(sparkle_alpha * (1 - i / streak_length) * 0.6)
            streak_x = sparkle_x + int(math.cos(streak_angle) * i)
            streak_y = sparkle_y + int(math.sin(streak_angle) * i)
            
            if 0 <= streak_x < s_w and 0 <= streak_y < s_h:
                streak_surf = pygame.Surface((2, 2), pygame.SRCALPHA)
                pygame.draw.circle(streak_surf, (*reflection_color, streak_alpha), (1, 1), 1)
                tile_surface.blit(streak_surf, (streak_x - 1, streak_y - 1), 
                                 special_flags=pygame.BLEND_ADD)
    
    # Layer 7: Subtle depth variations and water movement
    for depth_y in range(0, s_h, 4):
        for depth_x in range(0, s_w, 4):
            # Create subtle depth variations
            depth_noise = (math.sin(depth_x * 0.2) + math.cos(depth_y * 0.3) + 
                          math.sin((depth_x + depth_y) * 0.1)) / 3
            
            if depth_noise > 0.2:
                # Slightly shallower areas
                depth_color = tuple(min(255, int(c + depth_noise * 20)) for c in surface_water_color)
                pygame.draw.circle(tile_surface, depth_color, (depth_x, depth_y), 2)
            elif depth_noise < -0.2:
                # Slightly deeper areas
                depth_color = tuple(max(0, int(c + depth_noise * 15)) for c in mid_water_color)
                pygame.draw.circle(tile_surface, depth_color, (depth_x, depth_y), 1)
    
    # Layer 8: Final surface tension details
    for edge_detail in range(6):
        # Add subtle edge effects that suggest surface tension
        tension_x = random.randint(1, s_w - 2)
        tension_y = random.choice([0, 1, s_h - 2, s_h - 1])  # Top and bottom edges
        
        tension_surf = pygame.Surface((3, 2), pygame.SRCALPHA)
        pygame.draw.ellipse(tension_surf, (*light_water_color, 80), (0, 0, 3, 2))
        tile_surface.blit(tension_surf, (tension_x - 1, tension_y), 
                         special_flags=pygame.BLEND_ALPHA_SDL2)
        
        # Side edges
        tension_x = random.choice([0, 1, s_w - 2, s_w - 1])  # Left and right edges
        tension_y = random.randint(1, s_h - 2)
        
        tension_surf = pygame.Surface((2, 3), pygame.SRCALPHA)
        pygame.draw.ellipse(tension_surf, (*light_water_color, 80), (0, 0, 2, 3))
        tile_surface.blit(tension_surf, (tension_x, tension_y - 1), 
                         special_flags=pygame.BLEND_ALPHA_SDL2)
    
    return tile_surface


def get_tree_tile(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """
    Generate a detailed tree tile procedurally.
    
    Creates a realistic oak tree with:
    - Complex trunk with bark texture and 3D shading
    - Multi-layered canopy with varied leaf clusters
    - Natural branch structure and extensions
    - Root system and grass details
    - Seasonal color variations and depth
    - Proper lighting and shadows
    """
    tile_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size
    
    # Expanded color palette for realism
    trunk_base_color = (92, 51, 23)        # Dark brown trunk base
    trunk_highlight = (139, 69, 19)        # Lighter trunk highlight
    trunk_shadow = (62, 39, 35)            # Deep trunk shadow
    bark_texture = (110, 60, 30)           # Bark ridges
    
    canopy_base = (34, 102, 34)            # Deep forest green
    canopy_mid = (50, 130, 50)             # Medium green
    canopy_light = (80, 160, 80)           # Light green highlights
    canopy_dark = (20, 70, 20)             # Dark green shadows
    canopy_accent = (100, 150, 60)         # Yellow-green accent
    
    grass_base = (40, 120, 40)             # Grass base
    grass_dark = (25, 90, 25)              # Dark grass patches
    grass_light = (60, 140, 60)            # Light grass highlights
    
    root_color = (80, 45, 20)              # Visible root color
    moss_color = (60, 140, 60)             # Moss on trunk
    
    # Fill with varied grass background
    tile_surface.fill(grass_base)
    
    # Create grass texture base with natural variations
    random.seed(42)  # Consistent randomness for tiling
    
    # Layer 1: Grass base texture
    for _ in range(40):
        grass_x = random.randint(0, s_w - 1)
        grass_y = random.randint(int(s_h * 0.6), s_h - 1)  # Lower portion
        grass_shade = random.choice([grass_dark, grass_light, grass_base])
        grass_size = random.randint(1, 3)
        pygame.draw.circle(tile_surface, grass_shade, (grass_x, grass_y), grass_size)
    
    # Layer 2: Root system (drawn first, partially visible)
    random.seed(123)
    for root_num in range(4):
        root_angle = (root_num * 90 + random.randint(-30, 30)) * math.pi / 180
        root_start_x = s_w * 0.5 + random.randint(-3, 3)
        root_start_y = s_h * 0.85
        
        # Create curved root extending outward
        for root_segment in range(6):
            segment_progress = root_segment / 5.0
            root_x = root_start_x + math.cos(root_angle) * segment_progress * s_w * 0.3
            root_y = root_start_y + math.sin(root_angle) * segment_progress * s_h * 0.2 + segment_progress * s_h * 0.1
            root_thickness = max(1, int(3 * (1 - segment_progress)))
            
            if 0 <= root_x < s_w and 0 <= root_y < s_h:
                pygame.draw.circle(tile_surface, root_color, (int(root_x), int(root_y)), root_thickness)
    
    # Layer 3: Main trunk with detailed bark texture
    trunk_center_x = s_w * 0.5
    trunk_base_y = s_h * 0.9
    trunk_top_y = s_h * 0.4
    trunk_base_width = s_w * 0.15
    trunk_top_width = s_w * 0.12
    
    # Draw trunk in segments for natural taper
    trunk_segments = 8
    for segment in range(trunk_segments):
        segment_progress = segment / (trunk_segments - 1)
        
        # Calculate position and width for this segment
        segment_y = trunk_base_y + (trunk_top_y - trunk_base_y) * segment_progress
        segment_width = trunk_base_width + (trunk_top_width - trunk_base_width) * segment_progress
        
        # Add slight trunk curve/lean
        trunk_lean = math.sin(segment_progress * math.pi * 0.5) * s_w * 0.02
        segment_x = trunk_center_x + trunk_lean
        
        # Draw trunk segment with 3D effect
        trunk_rect = pygame.Rect(int(segment_x - segment_width/2), int(segment_y - 2), 
                                int(segment_width), 4)
        
        # Main trunk color with variation
        trunk_color = tuple(int(trunk_base_color[i] + random.randint(-10, 15)) for i in range(3))
        trunk_color = tuple(max(0, min(255, c)) for c in trunk_color)
        pygame.draw.rect(tile_surface, trunk_color, trunk_rect)
        
        # Add bark texture details
        for bark_detail in range(int(segment_width * 2)):
            bark_x = int(segment_x - segment_width/2 + bark_detail)
            bark_y = int(segment_y)
            if random.random() < 0.4:  # Random bark ridges
                bark_intensity = random.randint(-15, 25)
                bark_shade = tuple(max(0, min(255, bark_texture[i] + bark_intensity)) for i in range(3))
                pygame.draw.circle(tile_surface, bark_shade, (bark_x, bark_y), 1)
        
        # 3D highlighting - left side lighter, right side darker
        if segment_width > 2:
            # Left highlight
            highlight_rect = pygame.Rect(int(segment_x - segment_width/2), int(segment_y - 2), 
                                       max(1, int(segment_width * 0.3)), 4)
            pygame.draw.rect(tile_surface, trunk_highlight, highlight_rect)
            
            # Right shadow
            shadow_rect = pygame.Rect(int(segment_x + segment_width/4), int(segment_y - 2), 
                                    max(1, int(segment_width * 0.3)), 4)
            pygame.draw.rect(tile_surface, trunk_shadow, shadow_rect)
    
    # Layer 4: Branch structure extending from trunk
    random.seed(456)
    main_branches = [
        {'angle': -60, 'length': 0.25, 'start_height': 0.6},
        {'angle': -30, 'length': 0.2, 'start_height': 0.55},
        {'angle': 30, 'length': 0.22, 'start_height': 0.5},
        {'angle': 60, 'length': 0.18, 'start_height': 0.65},
    ]
    
    for branch in main_branches:
        branch_angle = branch['angle'] * math.pi / 180
        branch_length = branch['length'] * s_w
        start_height = branch['start_height'] * s_h
        
        branch_start_x = trunk_center_x
        branch_start_y = start_height
        
        # Draw branch in segments for natural curve
        branch_segments = 5
        for b_seg in range(branch_segments):
            seg_progress = b_seg / (branch_segments - 1)
            
            # Add natural curve to branch
            curve_modifier = math.sin(seg_progress * math.pi) * 0.1
            current_angle = branch_angle + curve_modifier
            
            seg_x = branch_start_x + math.cos(current_angle) * branch_length * seg_progress
            seg_y = branch_start_y + math.sin(current_angle) * branch_length * seg_progress * 0.5
            
            branch_thickness = max(1, int(4 * (1 - seg_progress * 0.7)))
            
            if 0 <= seg_x < s_w and 0 <= seg_y < s_h:
                pygame.draw.circle(tile_surface, trunk_base_color, (int(seg_x), int(seg_y)), branch_thickness)
    
    # Layer 5: Complex canopy with multiple leaf cluster layers
    random.seed(789)
    
    # Background leaf layer (darker, more distant)
    background_clusters = [
        {'pos': (s_w*0.2, s_h*0.3), 'size': 0.18},
        {'pos': (s_w*0.8, s_h*0.35), 'size': 0.16},
        {'pos': (s_w*0.35, s_h*0.2), 'size': 0.15},
        {'pos': (s_w*0.65, s_h*0.25), 'size': 0.17},
    ]
    
    for cluster in background_clusters:
        center_x, center_y = cluster['pos']
        cluster_radius = int(cluster['size'] * s_w)
        
        # Draw irregular cluster shape
        cluster_points = []
        for angle_step in range(0, 360, 30):
            angle_rad = angle_step * math.pi / 180
            radius_variation = cluster_radius * random.uniform(0.7, 1.3)
            point_x = center_x + math.cos(angle_rad) * radius_variation
            point_y = center_y + math.sin(angle_rad) * radius_variation
            cluster_points.append((int(point_x), int(point_y)))
        
        if len(cluster_points) >= 3:
            pygame.draw.polygon(tile_surface, canopy_dark, cluster_points)
    
    # Mid-layer leaf clusters (main canopy)
    main_clusters = [
        {'pos': (s_w*0.5, s_h*0.25), 'size': 0.22},
        {'pos': (s_w*0.3, s_h*0.35), 'size': 0.2},
        {'pos': (s_w*0.7, s_h*0.3), 'size': 0.19},
        {'pos': (s_w*0.4, s_h*0.15), 'size': 0.16},
        {'pos': (s_w*0.6, s_h*0.18), 'size': 0.18},
    ]
    
    for cluster in main_clusters:
        center_x, center_y = cluster['pos']
        cluster_radius = int(cluster['size'] * s_w)
        
        # Main cluster shape
        pygame.draw.circle(tile_surface, canopy_base, (int(center_x), int(center_y)), cluster_radius)
        
        # Add internal variations and texture
        for detail in range(cluster_radius):
            detail_angle = random.random() * math.pi * 2
            detail_distance = random.random() * cluster_radius * 0.8
            detail_x = center_x + math.cos(detail_angle) * detail_distance
            detail_y = center_y + math.sin(detail_angle) * detail_distance
            
            detail_color = random.choice([canopy_mid, canopy_base, canopy_dark])
            detail_size = random.randint(1, 3)
            
            if 0 <= detail_x < s_w and 0 <= detail_y < s_h:
                pygame.draw.circle(tile_surface, detail_color, (int(detail_x), int(detail_y)), detail_size)
    
    # Foreground leaf layer (lighter, closer)
    foreground_clusters = [
        {'pos': (s_w*0.45, s_h*0.28), 'size': 0.15},
        {'pos': (s_w*0.55, s_h*0.32), 'size': 0.14},
        {'pos': (s_w*0.25, s_h*0.4), 'size': 0.12},
        {'pos': (s_w*0.75, s_h*0.38), 'size': 0.13},
    ]
    
    for cluster in foreground_clusters:
        center_x, center_y = cluster['pos']
        cluster_radius = int(cluster['size'] * s_w)
        
        # Lighter, more detailed clusters
        pygame.draw.circle(tile_surface, canopy_light, (int(center_x), int(center_y)), cluster_radius)
        
        # Add highlights and accent colors
        highlight_radius = cluster_radius // 2
        pygame.draw.circle(tile_surface, canopy_accent, 
                         (int(center_x - cluster_radius*0.3), int(center_y - cluster_radius*0.3)), 
                         highlight_radius)
    
    # Layer 6: Moss and organic details on trunk
    random.seed(101112)
    for _ in range(8):
        moss_x = trunk_center_x + random.randint(-int(trunk_base_width*0.6), int(trunk_base_width*0.6))
        moss_y = random.randint(int(s_h*0.5), int(s_h*0.8))
        moss_size = random.randint(2, 4)
        
        if 0 <= moss_x < s_w and 0 <= moss_y < s_h:
            pygame.draw.circle(tile_surface, moss_color, (int(moss_x), int(moss_y)), moss_size)
    
    # Layer 7: Individual leaf details and sparkles
    random.seed(131415)
    for _ in range(15):
        leaf_x = random.randint(int(s_w*0.1), int(s_w*0.9))
        leaf_y = random.randint(int(s_h*0.1), int(s_h*0.5))
        
        # Check if we're roughly in the canopy area
        canopy_center_distance = math.sqrt((leaf_x - s_w*0.5)**2 + (leaf_y - s_h*0.3)**2)
        if canopy_center_distance < s_w * 0.4:
            leaf_color = random.choice([canopy_light, canopy_accent, canopy_mid])
            # Draw small leaf-like shapes
            leaf_points = [
                (leaf_x, leaf_y - 2),
                (leaf_x + 2, leaf_y),
                (leaf_x, leaf_y + 2),
                (leaf_x - 2, leaf_y)
            ]
            pygame.draw.polygon(tile_surface, leaf_color, leaf_points)
    
    # Layer 8: Subtle wind effect and movement hints
    for wind_effect in range(6):
        effect_x = random.randint(int(s_w*0.2), int(s_w*0.8))
        effect_y = random.randint(int(s_h*0.1), int(s_h*0.4))
        
        # Subtle directional shading to suggest movement
        wind_surf = pygame.Surface((3, 2), pygame.SRCALPHA)
        pygame.draw.ellipse(wind_surf, (*canopy_accent, 60), (0, 0, 3, 2))
        tile_surface.blit(wind_surf, (effect_x - 1, effect_y), 
                         special_flags=pygame.BLEND_ALPHA_SDL2)
    
    return tile_surface


def get_wooden_door_tile(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """
    Generate a detailed wooden door tile procedurally (closed state).
    
    Creates a realistic wooden door with:
    - Heavy wooden frame and construction
    - Detailed wood grain and aging
    - Metal hinges and hardware
    - Decorative panels and cross-bracing
    - Professional medieval styling
    """
    tile_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Enhanced wood color palette
    wood_base = (139, 69, 19)           # Saddle brown base
    wood_dark = (101, 67, 33)           # Dark brown shadows
    wood_light = (160, 82, 45)          # Light brown highlights
    wood_weathered = (120, 60, 30)      # Weathered wood
    wood_grain = (90, 50, 25)           # Deep grain lines
    frame_color = (80, 50, 25)          # Dark frame
    
    # Metal hardware colors
    metal_base = (80, 80, 80)           # Dark iron
    metal_highlight = (120, 120, 120)   # Metal highlights
    metal_dark = (50, 50, 50)           # Deep metal shadows
    rust_color = (120, 80, 60)          # Rust stains
    
    # Create consistent randomness
    random.seed(123)
    
    # Layer 1: Door frame (heavy wooden construction)
    frame_thickness = max(2, s_w // 8)
    frame_rect = pygame.Rect(0, 0, s_w, s_h)
    pygame.draw.rect(tile_surface, frame_color, frame_rect)
    
    # Add frame depth with highlights and shadows
    # Outer highlight
    pygame.draw.rect(tile_surface, wood_light, frame_rect, 1)
    # Inner shadow
    inner_frame = pygame.Rect(frame_thickness - 1, frame_thickness - 1, 
                             s_w - 2 * (frame_thickness - 1), s_h - 2 * (frame_thickness - 1))
    pygame.draw.rect(tile_surface, wood_dark, inner_frame, 1)
    
    # Layer 2: Main door surface
    door_inset = frame_thickness
    door_rect = pygame.Rect(door_inset, door_inset, 
                           s_w - 2 * door_inset, s_h - 2 * door_inset)
    pygame.draw.rect(tile_surface, wood_base, door_rect)
    
    # Layer 3: Door panels (raised rectangular sections)
    panel_margin = max(1, door_rect.width // 8)
    panel_gap = max(1, door_rect.height // 12)
    
    # Upper panel
    upper_panel = pygame.Rect(door_rect.left + panel_margin, door_rect.top + panel_margin,
                             door_rect.width - 2 * panel_margin, door_rect.height // 3 - panel_gap)
    pygame.draw.rect(tile_surface, wood_light, upper_panel)
    pygame.draw.rect(tile_surface, wood_dark, upper_panel, 1)
    
    # Add raised effect to upper panel
    pygame.draw.line(tile_surface, wood_light, 
                    (upper_panel.left, upper_panel.top), 
                    (upper_panel.right - 1, upper_panel.top), 1)
    pygame.draw.line(tile_surface, wood_light, 
                    (upper_panel.left, upper_panel.top), 
                    (upper_panel.left, upper_panel.bottom - 1), 1)
    
    # Lower panel
    lower_panel = pygame.Rect(door_rect.left + panel_margin, 
                             upper_panel.bottom + panel_gap * 2,
                             door_rect.width - 2 * panel_margin,
                             door_rect.bottom - (upper_panel.bottom + panel_gap * 2) - panel_margin)
    pygame.draw.rect(tile_surface, wood_light, lower_panel)
    pygame.draw.rect(tile_surface, wood_dark, lower_panel, 1)
    
    # Add raised effect to lower panel
    pygame.draw.line(tile_surface, wood_light, 
                    (lower_panel.left, lower_panel.top), 
                    (lower_panel.right - 1, lower_panel.top), 1)
    pygame.draw.line(tile_surface, wood_light, 
                    (lower_panel.left, lower_panel.top), 
                    (lower_panel.left, lower_panel.bottom - 1), 1)
    
    # Layer 4: Cross-bracing (diagonal supports)
    brace_color = wood_dark
    
    # X-brace on upper panel
    if upper_panel.width > 6 and upper_panel.height > 6:
        pygame.draw.line(tile_surface, brace_color,
                        (upper_panel.left + 2, upper_panel.top + 2),
                        (upper_panel.right - 2, upper_panel.bottom - 2), 1)
        pygame.draw.line(tile_surface, brace_color,
                        (upper_panel.right - 2, upper_panel.top + 2),
                        (upper_panel.left + 2, upper_panel.bottom - 2), 1)
    
    # X-brace on lower panel
    if lower_panel.width > 6 and lower_panel.height > 6:
        pygame.draw.line(tile_surface, brace_color,
                        (lower_panel.left + 2, lower_panel.top + 2),
                        (lower_panel.right - 2, lower_panel.bottom - 2), 1)
        pygame.draw.line(tile_surface, brace_color,
                        (lower_panel.right - 2, lower_panel.top + 2),
                        (lower_panel.left + 2, lower_panel.bottom - 2), 1)
    
    # Layer 5: Wood grain details
    # Vertical grain lines on main door surface
    for grain_line in range(4):
        grain_x = door_rect.left + (grain_line + 1) * door_rect.width // 5
        grain_start_y = door_rect.top + random.randint(2, 4)
        grain_end_y = door_rect.bottom - random.randint(2, 4)
        
        # Draw wavy grain line
        for y in range(grain_start_y, grain_end_y, 2):
            wave_offset = int(math.sin(y * 0.3) * 1.5)
            grain_point_x = grain_x + wave_offset
            if door_rect.left < grain_point_x < door_rect.right:
                pygame.draw.circle(tile_surface, wood_grain, (grain_point_x, y), 1)
    
    # Horizontal grain on panels
    for panel_rect in [upper_panel, lower_panel]:
        for h_grain in range(2):
            grain_y = panel_rect.top + (h_grain + 1) * panel_rect.height // 3
            pygame.draw.line(tile_surface, wood_grain,
                           (panel_rect.left + 1, grain_y),
                           (panel_rect.right - 1, grain_y), 1)
    
    # Layer 6: Metal hinges (left side)
    hinge_width = max(3, s_w // 8)
    hinge_height = max(4, s_h // 6)
    
    # Upper hinge
    upper_hinge_y = door_rect.top + door_rect.height // 6
    upper_hinge = pygame.Rect(door_rect.left - 1, upper_hinge_y, hinge_width, hinge_height)
    pygame.draw.rect(tile_surface, metal_base, upper_hinge)
    pygame.draw.rect(tile_surface, metal_highlight, upper_hinge, 1)
    
    # Lower hinge
    lower_hinge_y = door_rect.bottom - door_rect.height // 6 - hinge_height
    lower_hinge = pygame.Rect(door_rect.left - 1, lower_hinge_y, hinge_width, hinge_height)
    pygame.draw.rect(tile_surface, metal_base, lower_hinge)
    pygame.draw.rect(tile_surface, metal_highlight, lower_hinge, 1)
    
    # Hinge pins and screws
    for hinge_rect in [upper_hinge, lower_hinge]:
        # Center pin
        pin_x = hinge_rect.left + hinge_rect.width // 2
        pin_y = hinge_rect.top + hinge_rect.height // 2
        pygame.draw.circle(tile_surface, metal_dark, (pin_x, pin_y), 2)
        pygame.draw.circle(tile_surface, metal_highlight, (pin_x, pin_y), 1)
        
        # Mounting screws
        if hinge_rect.height >= 6:
            screw_y1 = hinge_rect.top + 2
            screw_y2 = hinge_rect.bottom - 2
            screw_x = hinge_rect.right - 2
            pygame.draw.circle(tile_surface, metal_dark, (screw_x, screw_y1), 1)
            pygame.draw.circle(tile_surface, metal_dark, (screw_x, screw_y2), 1)
    
    # Layer 7: Door handle/latch assembly (right side)
    handle_x = door_rect.right - door_rect.width // 4
    handle_y = door_rect.top + door_rect.height // 2
    
    # Handle plate
    plate_width = max(3, s_w // 10)
    plate_height = max(6, s_h // 8)
    handle_plate = pygame.Rect(handle_x - plate_width // 2, handle_y - plate_height // 2,
                              plate_width, plate_height)
    pygame.draw.rect(tile_surface, metal_base, handle_plate)
    pygame.draw.rect(tile_surface, metal_highlight, handle_plate, 1)
    
    # Handle ring
    pygame.draw.circle(tile_surface, metal_base, (handle_x, handle_y), 3)
    pygame.draw.circle(tile_surface, metal_highlight, (handle_x, handle_y), 2)
    pygame.draw.circle(tile_surface, metal_dark, (handle_x, handle_y), 1)
    
    # Keyhole
    keyhole_y = handle_y + 4
    pygame.draw.circle(tile_surface, metal_dark, (handle_x, keyhole_y), 1)
    
    # Layer 8: Weathering and aging details
    # Wood weathering spots
    for weather_spot in range(6):
        spot_x = random.randint(door_rect.left + 2, door_rect.right - 2)
        spot_y = random.randint(door_rect.top + 2, door_rect.bottom - 2)
        spot_size = random.randint(1, 2)
        pygame.draw.circle(tile_surface, wood_weathered, (spot_x, spot_y), spot_size)
    
    # Rust stains near metal hardware
    for hinge_rect in [upper_hinge, lower_hinge]:
        rust_x = hinge_rect.right + random.randint(0, 2)
        rust_y = hinge_rect.top + random.randint(2, hinge_rect.height - 2)
        pygame.draw.circle(tile_surface, rust_color, (rust_x, rust_y), 1)
    
    # Wear marks around handle
    wear_x = handle_x + random.randint(-2, 2)
    wear_y = handle_y + random.randint(-2, 2)
    pygame.draw.circle(tile_surface, wood_weathered, (wear_x, wear_y), 1)
    
    # Layer 9: Wood knots and natural imperfections
    for knot in range(2):
        knot_x = random.randint(door_rect.left + 3, door_rect.right - 3)
        knot_y = random.randint(door_rect.top + door_rect.height // 4, 
                               door_rect.bottom - door_rect.height // 4)
        knot_size = random.randint(1, 2)
        pygame.draw.circle(tile_surface, wood_dark, (knot_x, knot_y), knot_size)
        if knot_size > 1:
            pygame.draw.circle(tile_surface, wood_grain, (knot_x, knot_y), knot_size - 1)

    return tile_surface


def get_wooden_door_open_tile(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """
    Generate a detailed open wooden door tile procedurally.
    
    Creates an open door showing:
    - Stone or dirt floor background
    - Door frame remaining in place
    - Door swung open to the side (showing edge)
    - Realistic shadows and depth
    - Consistent floor texturing
    """
    tile_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Enhanced color palette
    # Floor colors (stone floor for indoor areas)
    floor_base = (120, 100, 80)         # Warm stone base
    floor_dark = (90, 75, 60)           # Dark stone
    floor_light = (140, 120, 100)       # Light stone highlights
    mortar_color = (80, 70, 60)         # Mortar between stones
    
    # Wood colors
    wood_base = (139, 69, 19)           # Saddle brown base
    wood_dark = (101, 67, 33)           # Dark brown shadows
    wood_light = (160, 82, 45)          # Light brown highlights
    wood_weathered = (120, 60, 30)      # Weathered wood
    frame_color = (80, 50, 25)          # Dark frame
    
    # Metal hardware colors
    metal_base = (80, 80, 80)           # Dark iron
    metal_highlight = (120, 120, 120)   # Metal highlights
    
    # Create consistent randomness
    random.seed(456)
    
    # Layer 1: Stone floor background
    tile_surface.fill(floor_base)
    
    # Create stone floor pattern
    # Add varied stone colors
    for _ in range(25):
        stone_x = random.randint(0, s_w - 1)
        stone_y = random.randint(0, s_h - 1)
        stone_size = random.randint(2, 5)
        stone_color = random.choice([floor_dark, floor_light, floor_base])
        
        # Draw irregular stone shape
        for stone_dot in range(stone_size):
            dot_x = stone_x + random.randint(-2, 2)
            dot_y = stone_y + random.randint(-2, 2)
            if 0 <= dot_x < s_w and 0 <= dot_y < s_h:
                pygame.draw.circle(tile_surface, stone_color, (dot_x, dot_y), 1)
    
    # Add mortar lines (stone joints)
    for mortar_line in range(4):
        # Horizontal mortar lines
        mortar_y = random.randint(s_h // 4, 3 * s_h // 4)
        mortar_start = random.randint(0, s_w // 3)
        mortar_end = random.randint(2 * s_w // 3, s_w)
        pygame.draw.line(tile_surface, mortar_color, 
                        (mortar_start, mortar_y), (mortar_end, mortar_y), 1)
        
        # Vertical mortar lines
        if mortar_line < 2:  # Fewer vertical lines
            mortar_x = random.randint(s_w // 4, 3 * s_w // 4)
            mortar_start_y = random.randint(0, s_h // 3)
            mortar_end_y = random.randint(2 * s_h // 3, s_h)
            pygame.draw.line(tile_surface, mortar_color, 
                            (mortar_x, mortar_start_y), (mortar_x, mortar_end_y), 1)
    
    # Layer 2: Door frame (remains in place when door is open)
    frame_thickness = max(2, s_w // 8)
    
    # Left frame (hinge side) - full height
    left_frame = pygame.Rect(0, 0, frame_thickness, s_h)
    pygame.draw.rect(tile_surface, frame_color, left_frame)
    pygame.draw.rect(tile_surface, wood_dark, left_frame, 1)
    
    # Top frame
    top_frame = pygame.Rect(0, 0, s_w, frame_thickness)
    pygame.draw.rect(tile_surface, frame_color, top_frame)
    pygame.draw.rect(tile_surface, wood_dark, top_frame, 1)
    
    # Right frame (partial, where handle side was)
    right_frame_width = frame_thickness // 2
    right_frame = pygame.Rect(s_w - right_frame_width, 0, right_frame_width, s_h)
    pygame.draw.rect(tile_surface, frame_color, right_frame)
    
    # Add frame depth details
    pygame.draw.line(tile_surface, wood_light, 
                    (left_frame.right - 1, left_frame.top), 
                    (left_frame.right - 1, left_frame.bottom), 1)
    pygame.draw.line(tile_surface, wood_light, 
                    (top_frame.left, top_frame.bottom - 1), 
                    (top_frame.right, top_frame.bottom - 1), 1)
    
    # Layer 3: Open door (swung to the left, showing edge)
    door_edge_width = max(2, s_w // 12)
    door_edge_height = int(s_h * 0.8)
    door_edge_y = frame_thickness
    
    # Door edge positioned at the left side
    door_edge = pygame.Rect(frame_thickness + 1, door_edge_y, door_edge_width, door_edge_height)
    pygame.draw.rect(tile_surface, wood_base, door_edge)
    
    # Add door edge details
    pygame.draw.rect(tile_surface, wood_dark, door_edge, 1)
    pygame.draw.line(tile_surface, wood_light, 
                    (door_edge.left, door_edge.top), 
                    (door_edge.left, door_edge.bottom), 1)
    
    # Show door thickness with a thin line
    pygame.draw.line(tile_surface, wood_dark,
                    (door_edge.right, door_edge.top),
                    (door_edge.right, door_edge.bottom), 1)
    
    # Layer 4: Hinges on the left frame (still visible and functional)
    hinge_width = max(3, s_w // 8)
    hinge_height = max(4, s_h // 8)
    
    # Upper hinge
    upper_hinge_y = door_edge_y + door_edge_height // 6
    upper_hinge = pygame.Rect(left_frame.right - 1, upper_hinge_y, hinge_width, hinge_height)
    pygame.draw.rect(tile_surface, metal_base, upper_hinge)
    pygame.draw.rect(tile_surface, metal_highlight, upper_hinge, 1)
    
    # Lower hinge
    lower_hinge_y = door_edge_y + door_edge_height - door_edge_height // 6 - hinge_height
    lower_hinge = pygame.Rect(left_frame.right - 1, lower_hinge_y, hinge_width, hinge_height)
    pygame.draw.rect(tile_surface, metal_base, lower_hinge)
    pygame.draw.rect(tile_surface, metal_highlight, lower_hinge, 1)
    
    # Hinge pins
    for hinge_rect in [upper_hinge, lower_hinge]:
        pin_x = hinge_rect.left + hinge_rect.width // 2
        pin_y = hinge_rect.top + hinge_rect.height // 2
        pygame.draw.circle(tile_surface, metal_highlight, (pin_x, pin_y), 1)
    
    # Layer 5: Door handle (on the door edge, now facing sideways)
    if door_edge_width >= 3:
        handle_x = door_edge.right - 1
        handle_y = door_edge.top + door_edge_height // 2
        pygame.draw.circle(tile_surface, metal_base, (handle_x, handle_y), 1)
    
    # Layer 6: Floor details in the open doorway
    # Enhanced floor texture in the revealed area
    doorway_left = left_frame.right
    doorway_right = s_w - right_frame_width
    doorway_top = top_frame.bottom
    doorway_bottom = s_h
    
    for _ in range(15):
        floor_x = random.randint(doorway_left, doorway_right - 1)
        floor_y = random.randint(doorway_top, doorway_bottom - 1)
        floor_detail_color = random.choice([floor_dark, floor_light])
        pygame.draw.circle(tile_surface, floor_detail_color, (floor_x, floor_y), 1)
    
    # Layer 7: Shadows cast by the open door and frame
    shadow_color = (60, 50, 40)  # Dark shadow
    
    # Shadow from door edge
    for shadow_offset in range(2):
        shadow_x = door_edge.right + shadow_offset + 1
        if shadow_x < s_w:
            pygame.draw.line(tile_surface, shadow_color,
                           (shadow_x, door_edge.top),
                           (shadow_x, door_edge.bottom), 1)
    
    # Shadow from frame
    pygame.draw.line(tile_surface, shadow_color,
                    (left_frame.right, top_frame.bottom),
                    (left_frame.right + 3, top_frame.bottom), 1)
    
    # Layer 8: Weathering details
    # Wear marks on frame
    for wear_spot in range(3):
        wear_x = left_frame.left + random.randint(1, left_frame.width - 1)
        wear_y = random.randint(door_edge_y + door_edge_height // 4, 
                               door_edge_y + 3 * door_edge_height // 4)
        pygame.draw.circle(tile_surface, wood_weathered, (wear_x, wear_y), 1)
    
    # Scuff marks on floor (from door opening/closing)
    scuff_color = (100, 85, 70)
    arc_center_x = left_frame.right
    arc_center_y = door_edge.top + door_edge_height
    arc_radius = door_edge_height + door_edge_width
    
    # Draw subtle arc showing door's swing path
    for arc_point in range(5):
        angle = math.pi * 0.3 * arc_point / 5  # 30 degree arc
        arc_x = arc_center_x + int(math.cos(angle) * arc_radius)
        arc_y = arc_center_y - int(math.sin(angle) * arc_radius)
        if 0 <= arc_x < s_w and 0 <= arc_y < s_h:
            pygame.draw.circle(tile_surface, scuff_color, (arc_x, arc_y), 1)

    return tile_surface


def get_wooden_bridge_tile(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """
    Generate a side view of a single arched wooden bridge tile.
    
    Creates a realistic arched wooden bridge with:
    - Curved arch structure showing bridge engineering
    - Wooden planks forming the deck
    - Stone or wood foundation supports
    - Railings and structural details
    - Water visible underneath the arch
    - Tileable for longer bridge spans
    """
    tile_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size
    
    # Enhanced bridge color palette
    wood_base = (139, 69, 19)           # Main wood color
    wood_dark = (101, 67, 33)           # Dark weathered wood
    wood_light = (160, 82, 45)          # Light wood highlights
    stone_color = (105, 105, 105)       # Stone supports
    stone_dark = (85, 85, 85)           # Dark stone shadows
    rope_color = (139, 105, 69)         # Hemp rope railings
    
    # Water colors (matching water tile)
    deep_water_color = (8, 74, 140)        # Deep ocean blue
    mid_water_color = (16, 109, 156)       # Medium depth blue
    surface_water_color = (32, 137, 189)   # Surface blue
    light_water_color = (64, 164, 223)     # Light blue highlights
    foam_color = (200, 230, 255)           # White foam
    reflection_color = (180, 220, 255)     # Light reflections
    shadow_color = (4, 45, 85)             # Deep shadows
    caustic_color = (120, 200, 255)        # Underwater caustics
    sparkle_color = (255, 255, 255)        # Surface sparkles
    
    # Create consistent randomness for tiling
    random.seed(789)
    
    # Layer 1: Complete water background (matching water tile exactly)
    # Fill base with deep water
    tile_surface.fill(deep_water_color)
    
    # Create full water tile implementation for background
    # Layer 1: Deep underwater shadows and variations
    for _ in range(25):
        shadow_x = random.randint(0, s_w - 1)
        shadow_y = random.randint(0, s_h - 1)
        shadow_radius = random.randint(3, 8)
        shadow_intensity = random.randint(20, 60)
        
        # Create soft shadow spots
        shadow_surf = pygame.Surface((shadow_radius * 2, shadow_radius * 2), pygame.SRCALPHA)
        shadow_alpha = max(0, 255 - shadow_intensity * 3)
        pygame.draw.circle(shadow_surf, (*shadow_color, shadow_alpha), 
                          (shadow_radius, shadow_radius), shadow_radius)
        tile_surface.blit(shadow_surf, (shadow_x - shadow_radius, shadow_y - shadow_radius), 
                         special_flags=pygame.BLEND_ALPHA_SDL2)
    
    # Layer 2: Mid-depth water with current flows
    for flow_y in range(0, s_h, 6):
        for flow_x in range(s_w):
            # Create flowing water effect with sine waves
            flow_offset = math.sin(flow_x * 0.4 + flow_y * 0.3) * 3
            current_strength = math.cos(flow_x * 0.2 + flow_y * 0.5) * 2
            
            if (flow_x + int(flow_offset)) % 8 < 5:
                flow_color = mid_water_color
                if current_strength > 0:
                    # Lighter areas for upward currents
                    flow_color = tuple(min(255, c + int(current_strength * 15)) for c in flow_color)
                else:
                    # Darker areas for downward currents
                    flow_color = tuple(max(0, c + int(current_strength * 10)) for c in flow_color)
                
                pygame.draw.circle(tile_surface, flow_color, (flow_x, flow_y), 2)
    
    # Layer 3: Surface water with complex wave patterns
    for wave_layer in range(3):
        wave_offset_x = wave_layer * 2.1
        wave_offset_y = wave_layer * 1.7
        wave_freq_x = 0.3 + wave_layer * 0.1
        wave_freq_y = 0.25 + wave_layer * 0.08
        
        for y in range(0, s_h, 3):
            for x in range(0, s_w, 2):
                # Multi-frequency wave interference
                wave1 = math.sin(x * wave_freq_x + wave_offset_x) * 1.5
                wave2 = math.cos(y * wave_freq_y + wave_offset_y) * 1.2
                wave3 = math.sin((x + y) * 0.15 + wave_layer) * 0.8
                
                combined_wave = wave1 + wave2 + wave3
                
                # Determine wave height and create appropriate color
                if combined_wave > 1.5:
                    # Wave crests - lighter
                    crest_intensity = min(1.0, (combined_wave - 1.5) / 1.5)
                    wave_color = tuple(int(surface_water_color[i] + 
                                          (light_water_color[i] - surface_water_color[i]) * crest_intensity) 
                                     for i in range(3))
                    pygame.draw.circle(tile_surface, wave_color, (x, y), 2)
                elif combined_wave < -1.0:
                    # Wave troughs - darker
                    trough_intensity = min(1.0, abs(combined_wave + 1.0) / 1.5)
                    wave_color = tuple(int(surface_water_color[i] - 
                                          (surface_water_color[i] - deep_water_color[i]) * trough_intensity * 0.3) 
                                     for i in range(3))
                    pygame.draw.circle(tile_surface, wave_color, (x, y), 1)
    
    # Layer 4: Underwater caustics (light patterns)
    random.seed(123)
    for _ in range(15):
        caustic_x = random.randint(int(s_w * 0.1), int(s_w * 0.9))
        caustic_y = random.randint(int(s_h * 0.1), int(s_h * 0.9))
        
        # Create caustic light patterns
        caustic_points = []
        center_angle = random.random() * math.pi * 2
        for i in range(6):
            angle = center_angle + (i * math.pi / 3) + random.uniform(-0.3, 0.3)
            radius = random.randint(2, 6)
            point_x = caustic_x + int(math.cos(angle) * radius)
            point_y = caustic_y + int(math.sin(angle) * radius)
            caustic_points.append((point_x, point_y))
        
        # Draw subtle caustic lines
        if len(caustic_points) >= 3:
            caustic_surf = pygame.Surface((12, 12), pygame.SRCALPHA)
            adjusted_points = [(p[0] - caustic_x + 6, p[1] - caustic_y + 6) for p in caustic_points[:3]]
            pygame.draw.polygon(caustic_surf, (*caustic_color, 60), adjusted_points)
            tile_surface.blit(caustic_surf, (caustic_x - 6, caustic_y - 6), 
                             special_flags=pygame.BLEND_ADD)
    
    # Layer 5: Surface foam and bubbles
    random.seed(456)
    for _ in range(12):
        foam_x = random.randint(0, s_w - 1)
        foam_y = random.randint(0, s_h - 1)
        foam_size = random.randint(1, 3)
        
        # Create foam with varying opacity
        foam_alpha = random.randint(40, 120)
        foam_surf = pygame.Surface((foam_size * 2, foam_size * 2), pygame.SRCALPHA)
        pygame.draw.circle(foam_surf, (*foam_color, foam_alpha), 
                          (foam_size, foam_size), foam_size)
        tile_surface.blit(foam_surf, (foam_x - foam_size, foam_y - foam_size), 
                         special_flags=pygame.BLEND_ALPHA_SDL2)
    
    # Layer 6: Light reflections and sparkles
    random.seed(789)
    for _ in range(8):
        sparkle_x = random.randint(int(s_w * 0.1), int(s_w * 0.9))
        sparkle_y = random.randint(int(s_h * 0.1), int(s_h * 0.9))
        
        # Create shimmering light reflections
        reflection_strength = random.uniform(0.3, 0.8)
        
        # Main sparkle
        sparkle_surf = pygame.Surface((4, 4), pygame.SRCALPHA)
        sparkle_alpha = int(255 * reflection_strength)
        pygame.draw.circle(sparkle_surf, (*sparkle_color, sparkle_alpha), (2, 2), 2)
        tile_surface.blit(sparkle_surf, (sparkle_x - 2, sparkle_y - 2), 
                         special_flags=pygame.BLEND_ADD)
        
        # Reflection streaks
        streak_length = random.randint(3, 7)
        streak_angle = random.uniform(0, math.pi * 2)
        streak_end_x = sparkle_x + int(math.cos(streak_angle) * streak_length)
        streak_end_y = sparkle_y + int(math.sin(streak_angle) * streak_length)
        
        # Draw reflection streak with fading alpha
        for i in range(streak_length):
            streak_alpha = int(sparkle_alpha * (1 - i / streak_length) * 0.6)
            streak_x = sparkle_x + int(math.cos(streak_angle) * i)
            streak_y = sparkle_y + int(math.sin(streak_angle) * i)
            
            if 0 <= streak_x < s_w and 0 <= streak_y < s_h:
                streak_surf = pygame.Surface((2, 2), pygame.SRCALPHA)
                pygame.draw.circle(streak_surf, (*reflection_color, streak_alpha), (1, 1), 1)
                tile_surface.blit(streak_surf, (streak_x - 1, streak_y - 1), 
                                 special_flags=pygame.BLEND_ADD)
    
    # Layer 7: Subtle depth variations and water movement
    for depth_y in range(0, s_h, 4):
        for depth_x in range(0, s_w, 4):
            # Create subtle depth variations
            depth_noise = (math.sin(depth_x * 0.2) + math.cos(depth_y * 0.3) + 
                          math.sin((depth_x + depth_y) * 0.1)) / 3
            
            if depth_noise > 0.2:
                # Slightly shallower areas
                depth_color = tuple(min(255, int(c + depth_noise * 20)) for c in surface_water_color)
                pygame.draw.circle(tile_surface, depth_color, (depth_x, depth_y), 2)
            elif depth_noise < -0.2:
                # Slightly deeper areas
                depth_color = tuple(max(0, int(c + depth_noise * 15)) for c in mid_water_color)
                pygame.draw.circle(tile_surface, depth_color, (depth_x, depth_y), 1)
    
    # Layer 8: Final surface tension details
    for edge_detail in range(6):
        # Add subtle edge effects that suggest surface tension
        tension_x = random.randint(1, s_w - 2)
        tension_y = random.choice([0, 1, s_h - 2, s_h - 1])  # Top and bottom edges
        
        tension_surf = pygame.Surface((3, 2), pygame.SRCALPHA)
        pygame.draw.ellipse(tension_surf, (*light_water_color, 80), (0, 0, 3, 2))
        tile_surface.blit(tension_surf, (tension_x - 1, tension_y), 
                         special_flags=pygame.BLEND_ALPHA_SDL2)
        
        # Side edges
        tension_x = random.choice([0, 1, s_w - 2, s_w - 1])  # Left and right edges
        tension_y = random.randint(1, s_h - 2)
        
        tension_surf = pygame.Surface((2, 3), pygame.SRCALPHA)
        pygame.draw.ellipse(tension_surf, (*light_water_color, 80), (0, 0, 2, 3))
        tile_surface.blit(tension_surf, (tension_x, tension_y - 1), 
                         special_flags=pygame.BLEND_ALPHA_SDL2)
    
    # Layer 2: Stone foundation supports at the edges
    # Left support
    left_support_width = int(s_w * 0.15)
    left_support_rect = pygame.Rect(0, int(s_h * 0.6), left_support_width, int(s_h * 0.4))
    pygame.draw.rect(tile_surface, stone_color, left_support_rect)
    
    # Right support
    right_support_width = int(s_w * 0.15)
    right_support_rect = pygame.Rect(s_w - right_support_width, int(s_h * 0.6), 
                                   right_support_width, int(s_h * 0.4))
    pygame.draw.rect(tile_surface, stone_color, right_support_rect)
    
    # Add stone texture
    for stone_detail in range(10):
        side = random.choice(['left', 'right'])
        if side == 'left':
            stone_x = random.randint(0, left_support_width)
            stone_y = random.randint(int(s_h * 0.6), s_h - 1)
        else:
            stone_x = random.randint(s_w - right_support_width, s_w - 1)
            stone_y = random.randint(int(s_h * 0.6), s_h - 1)
        
        stone_shade = random.choice([stone_dark, stone_color])
        pygame.draw.circle(tile_surface, stone_shade, (stone_x, stone_y), 1)
    
    # Layer 3: Main arch structure (curved wooden beams)
    arch_center_x = s_w // 2
    arch_center_y = int(s_h * 0.65)
    arch_radius = int(s_w * 0.4)
    
    # Create arch curve using multiple segments
    arch_segments = 20
    arch_points = []
    
    for segment in range(arch_segments + 1):
        # Calculate angle from left to right across the arch
        angle = math.pi * (segment / arch_segments)
        
        # Calculate position on the arch
        x = arch_center_x + math.cos(angle) * arch_radius
        y = arch_center_y - math.sin(angle) * (arch_radius * 0.6)  # Flatten the arch a bit
        
        # Ensure points stay within bounds
        x = max(left_support_width, min(s_w - right_support_width, x))
        y = max(int(s_h * 0.3), min(int(s_h * 0.7), y))
        
        arch_points.append((int(x), int(y)))
    
    # Draw the arch structure (multiple wooden beams)
    beam_thickness = 3
    for beam_offset in range(-1, 2):  # Three parallel beams
        offset_points = []
        for point in arch_points:
            offset_x = point[0]
            offset_y = point[1] + beam_offset * 2
            offset_points.append((offset_x, offset_y))
        
        if len(offset_points) > 1:
            pygame.draw.lines(tile_surface, wood_dark, False, offset_points, beam_thickness)
    
    # Layer 4: Bridge deck (horizontal planks)
    deck_y = int(s_h * 0.35)
    deck_height = int(s_h * 0.1)
    deck_rect = pygame.Rect(0, deck_y, s_w, deck_height)
    pygame.draw.rect(tile_surface, wood_base, deck_rect)
    
    # Add individual plank divisions
    plank_width = s_w // 8
    for plank_num in range(9):
        plank_x = plank_num * plank_width
        if plank_x < s_w:
            pygame.draw.line(tile_surface, wood_dark, 
                           (plank_x, deck_y), (plank_x, deck_y + deck_height), 1)
    
    # Add wood grain to deck
    for grain_line in range(3):
        grain_y = deck_y + grain_line * (deck_height // 3)
        grain_color = wood_dark if grain_line % 2 == 0 else wood_light
        pygame.draw.line(tile_surface, grain_color, 
                       (0, grain_y), (s_w, grain_y), 1)
    
    # Layer 5: Bridge railings
    railing_height = int(s_h * 0.2)
    railing_y = deck_y - railing_height
    
    # Top rail
    pygame.draw.line(tile_surface, wood_base, 
                   (0, railing_y), (s_w, railing_y), 2)
    
    # Bottom rail
    bottom_rail_y = deck_y - int(railing_height * 0.5)
    pygame.draw.line(tile_surface, wood_base, 
                   (0, bottom_rail_y), (s_w, bottom_rail_y), 2)
    
    # Vertical posts
    post_spacing = s_w // 4
    for post_num in range(5):
        post_x = post_num * post_spacing
        if post_x < s_w:
            pygame.draw.line(tile_surface, wood_dark,
                           (post_x, railing_y), (post_x, deck_y), 2)
    
    # Layer 6: Rope or chain details on railings
    random.seed(456)
    rope_segments = 12
    for segment in range(rope_segments):
        rope_x = segment * (s_w // rope_segments)
        rope_y = railing_y + random.randint(2, int(railing_height * 0.4))
        
        # Create sagging rope effect
        sag_amount = math.sin((segment / rope_segments) * math.pi * 2) * 2
        rope_y += int(sag_amount)
        
        if 0 <= rope_x < s_w and 0 <= rope_y < deck_y:
            pygame.draw.circle(tile_surface, rope_color, (rope_x, rope_y), 1)
    
    # Layer 7: Structural cross-braces under the deck
    # Diagonal braces connecting arch to deck
    brace_points = [
        (int(s_w * 0.2), deck_y + deck_height),
        (int(s_w * 0.3), int(s_h * 0.55)),
        (int(s_w * 0.7), int(s_h * 0.55)),
        (int(s_w * 0.8), deck_y + deck_height)
    ]
    
    for i in range(len(brace_points) - 1):
        pygame.draw.line(tile_surface, wood_dark, 
                       brace_points[i], brace_points[i + 1], 2)
    
    # Layer 8: Environmental details
    # Moss and weathering on stone supports
    random.seed(321)
    moss_color = (60, 100, 60)
    for moss_spot in range(8):
        side = random.choice(['left', 'right'])
        if side == 'left':
            moss_x = random.randint(0, left_support_width)
        else:
            moss_x = random.randint(s_w - right_support_width, s_w - 1)
        
        moss_y = random.randint(int(s_h * 0.6), s_h - 1)
        moss_size = random.randint(1, 2)
        pygame.draw.circle(tile_surface, moss_color, (moss_x, moss_y), moss_size)
    
    # Water stains and aging on wood
    for stain in range(6):
        stain_x = random.randint(0, s_w - 1)
        stain_y = random.randint(deck_y, deck_y + deck_height)
        stain_color = (120, 100, 80)  # Weathered wood color
        stain_size = random.randint(1, 3)
        pygame.draw.circle(tile_surface, stain_color, (stain_x, stain_y), stain_size)
    
    # Layer 9: Highlight the arch structure
    # Add highlights to make the arch more prominent
    highlight_color = wood_light
    for segment in range(0, len(arch_points) - 1, 2):
        if segment < len(arch_points) - 1:
            start_point = arch_points[segment]
            end_point = arch_points[segment + 1]
            pygame.draw.line(tile_surface, highlight_color, start_point, end_point, 1)
    
    return tile_surface


def get_mountain_tile(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a mountain tile procedurally."""
    tile_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    rock_color = (105, 105, 105)        # Gray rock
    dark_rock_color = (70, 70, 70)      # Dark gray
    snow_color = (255, 255, 255)        # White snow
    grass_color = (34, 139, 34)         # Forest green background

    # Fill with grass background
    tile_surface.fill(grass_color)

    # Mountain shape
    mountain_points = [
        (0, s_h),                       # Bottom left
        (s_w*0.3, s_h*0.4),            # Left peak
        (s_w*0.5, s_h*0.1),            # Main peak
        (s_w*0.7, s_h*0.3),            # Right peak
        (s_w, s_h),                     # Bottom right
    ]
    pygame.draw.polygon(tile_surface, rock_color, mountain_points)
    
    # Add shading
    shadow_points = [
        (s_w*0.5, s_h*0.1),            # Main peak
        (s_w*0.7, s_h*0.3),            # Right peak
        (s_w, s_h),                     # Bottom right
        (s_w*0.6, s_h),                 # Shadow bottom
    ]
    pygame.draw.polygon(tile_surface, dark_rock_color, shadow_points)
    
    # Snow cap
    snow_points = [
        (s_w*0.4, s_h*0.2),            # Left snow
        (s_w*0.5, s_h*0.1),            # Peak
        (s_w*0.6, s_h*0.2),            # Right snow
    ]
    pygame.draw.polygon(tile_surface, snow_color, snow_points)
    
    return tile_surface


def get_exit_portal_tile(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate an exit portal tile procedurally."""
    tile_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    portal_color = (138, 43, 226)        # Blue violet
    portal_inner = (75, 0, 130)         # Indigo
    portal_glow = (255, 255, 255)       # White glow
    portal_sparkle = (255, 215, 0)      # Gold sparkles

    # Draw portal base (circular)
    center = (s_w // 2, s_h // 2)
    outer_radius = min(s_w, s_h) // 2 - 2
    inner_radius = outer_radius - 4

    # Outer glow
    pygame.draw.circle(tile_surface, portal_glow, center, outer_radius + 1)

    # Main portal ring
    pygame.draw.circle(tile_surface, portal_color, center, outer_radius)
    pygame.draw.circle(tile_surface, portal_inner, center, inner_radius)

    # Inner swirl effect
    import math
    for angle in range(0, 360, 30):
        rad = math.radians(angle)
        x = center[0] + int(math.cos(rad) * (inner_radius - 2))
        y = center[1] + int(math.sin(rad) * (inner_radius - 2))
        pygame.draw.circle(tile_surface, portal_sparkle, (x, y), 1)

    # Center dot
    pygame.draw.circle(tile_surface, portal_glow, center, 2)

    return tile_surface


def get_grass_tile(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """
    Generate a detailed grass tile procedurally.
    
    Creates a natural grass field with:
    - Varied grass blade lengths and densities
    - Small wildflowers and seeds
    - Soil patches and organic matter
    - Subtle color variations for realistic fields
    - Tileable seamless edges
    """
    tile_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Expanded color palette for natural grass
    grass_base = (34, 139, 34)           # Forest green base
    grass_dark = (20, 100, 20)           # Dark green shadows
    grass_light = (60, 180, 60)          # Light green highlights
    grass_yellow = (80, 160, 40)         # Yellow-green variation
    grass_blue = (30, 120, 50)           # Blue-green variation
    
    # Natural elements
    soil_color = (101, 67, 33)           # Brown soil patches
    soil_dark = (80, 50, 25)             # Dark soil
    clover_color = (40, 120, 40)         # Clover patches
    flower_white = (255, 255, 255)       # Small white flowers
    flower_yellow = (255, 255, 0)        # Small yellow flowers
    seed_color = (160, 140, 100)         # Dried seed heads
    
    # Fill base with varied grass
    tile_surface.fill(grass_base)
    
    # Create natural grass texture base
    random.seed(456)  # Consistent randomness for tiling
    
    # Layer 1: Soil patches and organic base
    for _ in range(8):
        soil_x = random.randint(0, s_w - 4)
        soil_y = random.randint(0, s_h - 4)
        soil_size = random.randint(2, 4)
        soil_shade = random.choice([soil_color, soil_dark])
        
        # Draw irregular soil patches
        for patch_dot in range(soil_size):
            patch_x = soil_x + random.randint(-2, 2)
            patch_y = soil_y + random.randint(-2, 2)
            if 0 <= patch_x < s_w and 0 <= patch_y < s_h:
                pygame.draw.circle(tile_surface, soil_shade, (patch_x, patch_y), 1)
    
    # Layer 2: Base grass variation patches
    for _ in range(12):
        patch_x = random.randint(0, s_w - 6)
        patch_y = random.randint(0, s_h - 6)
        patch_size = random.randint(3, 6)
        grass_shade = random.choice([grass_dark, grass_light, grass_yellow, grass_blue])
        
        # Create organic grass patches
        for _ in range(patch_size):
            grass_x = patch_x + random.randint(-3, 3)
            grass_y = patch_y + random.randint(-3, 3)
            if 0 <= grass_x < s_w and 0 <= grass_y < s_h:
                pygame.draw.circle(tile_surface, grass_shade, (grass_x, grass_y), 1)
    
    # Layer 3: Individual grass blades (varied lengths and directions)
    for _ in range(50):
        blade_x = random.randint(1, s_w - 2)
        blade_y = random.randint(1, s_h - 2)
        blade_height = random.randint(2, 8)
        blade_color = random.choice([grass_dark, grass_light, grass_base, grass_yellow])
        
        # Add slight curve to grass blades
        blade_lean = random.randint(-1, 1)
        blade_top_x = blade_x + blade_lean
        blade_top_y = max(0, blade_y - blade_height)
        
        # Draw grass blade
        pygame.draw.line(tile_surface, blade_color, 
                        (blade_x, blade_y), (blade_top_x, blade_top_y), 1)
    
    # Layer 4: Clover and ground cover
    for _ in range(6):
        clover_x = random.randint(2, s_w - 3)
        clover_y = random.randint(2, s_h - 3)
        
        # Draw small clover cluster (three-leaf pattern)
        pygame.draw.circle(tile_surface, clover_color, (clover_x, clover_y), 1)
        pygame.draw.circle(tile_surface, clover_color, (clover_x + 1, clover_y - 1), 1)
        pygame.draw.circle(tile_surface, clover_color, (clover_x - 1, clover_y - 1), 1)
    
    # Layer 5: Small wildflowers (very small, scattered)
    for _ in range(4):
        flower_x = random.randint(1, s_w - 2)
        flower_y = random.randint(1, s_h - 2)
        flower_color = random.choice([flower_white, flower_yellow])
        
        # Draw tiny flower (just a small dot with center)
        pygame.draw.circle(tile_surface, flower_color, (flower_x, flower_y), 1)
        # Add tiny center dot
        if flower_color == flower_white:
            pygame.draw.circle(tile_surface, flower_yellow, (flower_x, flower_y), 1)
    
    # Layer 6: Seed heads and dried grass
    for _ in range(8):
        seed_x = random.randint(1, s_w - 2)
        seed_y = random.randint(1, s_h - 2)
        seed_height = random.randint(3, 6)
        
        # Draw thin seed stem
        pygame.draw.line(tile_surface, grass_dark, 
                        (seed_x, seed_y), (seed_x, seed_y - seed_height), 1)
        # Add seed head
        pygame.draw.circle(tile_surface, seed_color, (seed_x, seed_y - seed_height), 1)
    
    # Layer 7: Subtle natural variations and texture
    for _ in range(20):
        var_x = random.randint(0, s_w - 1)
        var_y = random.randint(0, s_h - 1)
        variation_shade = random.choice([grass_dark, grass_light, grass_base])
        
        # Add subtle color variation
        if random.random() < 0.3:  # 30% chance for variation
            pygame.draw.circle(tile_surface, variation_shade, (var_x, var_y), 1)
    
    # Layer 8: Tiny insects and life hints (very subtle)
    for _ in range(2):
        bug_x = random.randint(2, s_w - 3)
        bug_y = random.randint(2, s_h - 3)
        
        # Draw tiny moving insects as small dark dots
        if random.random() < 0.5:  # 50% chance to show
            pygame.draw.circle(tile_surface, (30, 30, 30), (bug_x, bug_y), 1)
    
    return tile_surface


def get_gravel_tile(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a gravel tile procedurally."""
    tile_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    gravel_color = (128, 128, 128)      # Gray
    dark_gravel_color = (96, 96, 96)    # Dark gray
    light_gravel_color = (160, 160, 160) # Light gray

    # Fill base
    tile_surface.fill(gravel_color)

    # Add gravel stones
    import random
    random.seed(789)  # Consistent randomness

    for _ in range(60):
        x = random.randint(0, s_w - 1)
        y = random.randint(0, s_h - 1)
        stone_size = random.randint(1, 3)
        color = random.choice([dark_gravel_color, light_gravel_color, gravel_color])

        # Draw small stone
        pygame.draw.circle(tile_surface, color, (x, y), stone_size)

    return tile_surface


def get_willow_tree_tile(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """
    Generate a detailed willow tree tile procedurally.
    
    Creates a realistic weeping willow with:
    - Graceful drooping branches and foliage
    - Distinctive trunk with natural texture
    - Cascading leaf curtains with movement
    - Layered depth and atmospheric effects
    - Subtle wind movement and flow
    - Proper lighting and organic shading
    """
    tile_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size
    
    # Expanded color palette for realistic willow
    trunk_base_color = (85, 47, 25)       # Darker willow trunk
    trunk_highlight = (120, 68, 35)       # Trunk highlights
    trunk_shadow = (55, 30, 18)           # Deep trunk shadows
    bark_detail = (95, 55, 30)            # Bark texture
    
    # Willow-specific greens (more yellow-green)
    leaf_base = (124, 175, 124)           # Light willow green
    leaf_mid = (144, 195, 124)            # Medium willow green
    leaf_light = (164, 215, 144)          # Bright willow highlights
    leaf_dark = (84, 135, 84)             # Dark willow shadows
    leaf_accent = (180, 220, 140)         # Yellow-green accent
    
    grass_base = (45, 125, 45)            # Grass base
    grass_light = (65, 145, 65)           # Light grass
    grass_dark = (30, 95, 30)             # Dark grass patches
    
    water_hint = (100, 150, 180)          # Subtle water reflection hint
    mist_color = (200, 220, 200)          # Atmospheric mist
    
    # Fill with grass base
    tile_surface.fill(grass_base)
    
    # Layer 1: Grass and ground texture with moisture hints
    random.seed(42)  # Consistent randomness for tiling
    
    # Create moisture-loving grass texture
    for _ in range(45):
        grass_x = random.randint(0, s_w - 1)
        grass_y = random.randint(int(s_h * 0.6), s_h - 1)
        
        # More varied grass around willow (likes moisture)
        if random.random() < 0.3:
            grass_shade = water_hint  # Hint of moisture
        else:
            grass_shade = random.choice([grass_dark, grass_light, grass_base])
        
        grass_size = random.randint(1, 3)
        pygame.draw.circle(tile_surface, grass_shade, (grass_x, grass_y), grass_size)
    
    # Layer 2: Atmospheric mist/moisture base
    random.seed(123)
    for _ in range(8):
        mist_x = random.randint(0, s_w - 1)
        mist_y = random.randint(0, int(s_h * 0.7))
        mist_size = random.randint(4, 8)
        
        # Create soft mist effect
        mist_surf = pygame.Surface((mist_size * 2, mist_size * 2), pygame.SRCALPHA)
        mist_alpha = random.randint(20, 50)
        pygame.draw.circle(mist_surf, (*mist_color, mist_alpha), 
                          (mist_size, mist_size), mist_size)
        tile_surface.blit(mist_surf, (mist_x - mist_size, mist_y - mist_size), 
                         special_flags=pygame.BLEND_ALPHA_SDL2)
    
    # Layer 3: Main trunk (slightly more slender than oak)
    trunk_center_x = s_w * 0.5
    trunk_base_y = s_h * 0.85
    trunk_top_y = s_h * 0.45
    trunk_base_width = s_w * 0.12
    trunk_top_width = s_w * 0.08
    
    # Draw trunk with natural willow characteristics
    trunk_segments = 10
    for segment in range(trunk_segments):
        segment_progress = segment / (trunk_segments - 1)
        
        # Willow trunks often have a slight curve
        segment_y = trunk_base_y + (trunk_top_y - trunk_base_y) * segment_progress
        segment_width = trunk_base_width + (trunk_top_width - trunk_base_width) * segment_progress
        
        # Add characteristic willow lean/curve
        willow_curve = math.sin(segment_progress * math.pi * 0.7) * s_w * 0.04
        segment_x = trunk_center_x + willow_curve
        
        # Draw trunk segment
        trunk_rect = pygame.Rect(int(segment_x - segment_width/2), int(segment_y - 1.5), 
                                int(segment_width), 3)
        
        # Natural color variation
        trunk_color = tuple(int(trunk_base_color[i] + random.randint(-8, 12)) for i in range(3))
        trunk_color = tuple(max(0, min(255, c)) for c in trunk_color)
        pygame.draw.rect(tile_surface, trunk_color, trunk_rect)
        
        # Willow bark texture (often smoother than oak)
        if random.random() < 0.3:
            bark_x = int(segment_x)
            bark_y = int(segment_y)
            pygame.draw.circle(tile_surface, bark_detail, (bark_x, bark_y), 1)
        
        # 3D shading
        if segment_width > 2:
            # Left highlight
            highlight_rect = pygame.Rect(int(segment_x - segment_width/2), int(segment_y - 1.5), 
                                       max(1, int(segment_width * 0.25)), 3)
            pygame.draw.rect(tile_surface, trunk_highlight, highlight_rect)
            
            # Right shadow
            shadow_rect = pygame.Rect(int(segment_x + segment_width/3), int(segment_y - 1.5), 
                                    max(1, int(segment_width * 0.25)), 3)
            pygame.draw.rect(tile_surface, trunk_shadow, shadow_rect)
    
    # Layer 4: Main drooping branches (willow characteristic)
    random.seed(456)
    main_branches = [
        {'angle': -45, 'start_height': 0.5, 'droop': 0.8},
        {'angle': -20, 'start_height': 0.55, 'droop': 0.9},
        {'angle': 0, 'start_height': 0.48, 'droop': 0.85},
        {'angle': 20, 'start_height': 0.52, 'droop': 0.95},
        {'angle': 45, 'start_height': 0.49, 'droop': 0.87},
    ]
    
    for branch in main_branches:
        initial_angle = branch['angle'] * math.pi / 180
        start_height = branch['start_height'] * s_h
        droop_factor = branch['droop']
        
        branch_start_x = trunk_center_x + random.randint(-2, 2)
        branch_start_y = start_height
        
        # Draw drooping branch in many segments for natural curve
        branch_segments = 8
        for b_seg in range(branch_segments):
            seg_progress = b_seg / (branch_segments - 1)
            
            # Willow branches droop heavily - create downward curve
            droop_angle = seg_progress * droop_factor * math.pi * 0.5
            current_angle = initial_angle + droop_angle
            
            branch_length = s_w * 0.15 * (1 + seg_progress * 0.5)
            
            seg_x = branch_start_x + math.cos(current_angle) * branch_length * seg_progress
            seg_y = branch_start_y + abs(math.sin(current_angle)) * branch_length * seg_progress
            
            branch_thickness = max(1, int(3 * (1 - seg_progress * 0.5)))
            
            if 0 <= seg_x < s_w and 0 <= seg_y < s_h:
                pygame.draw.circle(tile_surface, trunk_base_color, (int(seg_x), int(seg_y)), branch_thickness)
    
    # Layer 5: Cascading foliage layers (signature willow drooping leaves)
    random.seed(789)
    
    # Background curtain layer (furthest)
    background_curtains = [
        {'x': s_w*0.15, 'top_y': s_h*0.3, 'length': 0.5, 'width': 0.08},
        {'x': s_w*0.85, 'top_y': s_h*0.32, 'length': 0.48, 'width': 0.07},
        {'x': s_w*0.25, 'top_y': s_h*0.28, 'length': 0.52, 'width': 0.09},
        {'x': s_w*0.75, 'top_y': s_h*0.31, 'length': 0.46, 'width': 0.08},
    ]
    
    for curtain in background_curtains:
        curtain_x = curtain['x']
        curtain_top = curtain['top_y']
        curtain_length = curtain['length'] * s_h
        curtain_width = curtain['width'] * s_w
        
        # Draw flowing curtain of leaves
        curtain_segments = 12
        for c_seg in range(curtain_segments):
            seg_progress = c_seg / (curtain_segments - 1)
            
            # Create gentle wave motion in the hanging foliage
            wave_offset = math.sin(seg_progress * math.pi * 2 + curtain_x * 0.1) * s_w * 0.03
            
            seg_x = curtain_x + wave_offset
            seg_y = curtain_top + curtain_length * seg_progress
            seg_width = curtain_width * (1 - seg_progress * 0.3)  # Tapers toward bottom
            
            if seg_y < s_h:
                # Draw elliptical leaf cluster
                leaf_rect = pygame.Rect(int(seg_x - seg_width/2), int(seg_y - 2), 
                                       max(2, int(seg_width)), 4)
                pygame.draw.ellipse(tile_surface, leaf_dark, leaf_rect)
    
    # Mid-layer curtains (main drooping foliage)
    main_curtains = [
        {'x': s_w*0.3, 'top_y': s_h*0.25, 'length': 0.6, 'width': 0.12},
        {'x': s_w*0.5, 'top_y': s_h*0.22, 'length': 0.65, 'width': 0.14},
        {'x': s_w*0.7, 'top_y': s_h*0.26, 'length': 0.58, 'width': 0.11},
        {'x': s_w*0.4, 'top_y': s_h*0.28, 'length': 0.55, 'width': 0.10},
        {'x': s_w*0.6, 'top_y': s_h*0.24, 'length': 0.62, 'width': 0.13},
    ]
    
    for curtain in main_curtains:
        curtain_x = curtain['x']
        curtain_top = curtain['top_y']
        curtain_length = curtain['length'] * s_h
        curtain_width = curtain['width'] * s_w
        
        # Main leaf curtains with more detail
        curtain_segments = 15
        for c_seg in range(curtain_segments):
            seg_progress = c_seg / (curtain_segments - 1)
            
            # More complex wave pattern for main curtains
            wave1 = math.sin(seg_progress * math.pi * 3 + curtain_x * 0.2) * s_w * 0.02
            wave2 = math.cos(seg_progress * math.pi * 1.5 + curtain_x * 0.15) * s_w * 0.015
            wave_offset = wave1 + wave2
            
            seg_x = curtain_x + wave_offset
            seg_y = curtain_top + curtain_length * seg_progress
            seg_width = curtain_width * (1 - seg_progress * 0.2)
            
            if seg_y < s_h:
                # Draw main leaf clusters
                leaf_rect = pygame.Rect(int(seg_x - seg_width/2), int(seg_y - 2), 
                                       max(3, int(seg_width)), 4)
                leaf_color = random.choice([leaf_base, leaf_mid])
                pygame.draw.ellipse(tile_surface, leaf_color, leaf_rect)
                
                # Add internal leaf detail
                for detail in range(int(seg_width * 0.5)):
                    detail_x = seg_x + random.uniform(-seg_width*0.3, seg_width*0.3)
                    detail_y = seg_y + random.uniform(-1, 1)
                    detail_color = random.choice([leaf_mid, leaf_light, leaf_base])
                    
                    if 0 <= detail_x < s_w and 0 <= detail_y < s_h:
                        pygame.draw.circle(tile_surface, detail_color, (int(detail_x), int(detail_y)), 1)
    
    # Foreground curtains (closest, most detailed)
    foreground_curtains = [
        {'x': s_w*0.35, 'top_y': s_h*0.27, 'length': 0.5, 'width': 0.10},
        {'x': s_w*0.55, 'top_y': s_h*0.25, 'length': 0.53, 'width': 0.11},
        {'x': s_w*0.45, 'top_y': s_h*0.29, 'length': 0.48, 'width': 0.09},
    ]
    
    for curtain in foreground_curtains:
        curtain_x = curtain['x']
        curtain_top = curtain['top_y']
        curtain_length = curtain['length'] * s_h
        curtain_width = curtain['width'] * s_w
        
        # Foreground details with highlights
        curtain_segments = 12
        for c_seg in range(curtain_segments):
            seg_progress = c_seg / (curtain_segments - 1)
            
            # Subtle movement for foreground
            wave_offset = math.sin(seg_progress * math.pi * 2.5 + curtain_x * 0.25) * s_w * 0.015
            
            seg_x = curtain_x + wave_offset
            seg_y = curtain_top + curtain_length * seg_progress
            seg_width = curtain_width * (1 - seg_progress * 0.15)
            
            if seg_y < s_h:
                # Lighter, more detailed foreground clusters
                leaf_rect = pygame.Rect(int(seg_x - seg_width/2), int(seg_y - 2), 
                                       max(2, int(seg_width)), 4)
                pygame.draw.ellipse(tile_surface, leaf_light, leaf_rect)
                
                # Add highlights
                if seg_progress < 0.7:  # Top portions get more light
                    highlight_rect = pygame.Rect(int(seg_x - seg_width*0.3), int(seg_y - 1), 
                                               max(1, int(seg_width*0.4)), 2)
                    pygame.draw.ellipse(tile_surface, leaf_accent, highlight_rect)
    
    # Layer 6: Individual hanging leaf strands
    random.seed(101112)
    for strand in range(12):
        strand_x = random.randint(int(s_w*0.2), int(s_w*0.8))
        strand_top = random.randint(int(s_h*0.3), int(s_h*0.4))
        strand_length = random.randint(int(s_h*0.2), int(s_h*0.4))
        
        # Draw individual leaf strands
        strand_segments = random.randint(6, 10)
        for s_seg in range(strand_segments):
            seg_progress = s_seg / (strand_segments - 1)
            
            # Natural sway in individual strands
            sway = math.sin(seg_progress * math.pi + strand_x * 0.3) * 2
            
            leaf_x = strand_x + sway
            leaf_y = strand_top + strand_length * seg_progress
            
            if leaf_y < s_h and 0 <= leaf_x < s_w:
                leaf_color = random.choice([leaf_mid, leaf_light, leaf_accent])
                # Draw small individual leaves
                leaf_points = [
                    (leaf_x, leaf_y - 1),
                    (leaf_x + 1, leaf_y),
                    (leaf_x, leaf_y + 1),
                    (leaf_x - 1, leaf_y)
                ]
                pygame.draw.polygon(tile_surface, leaf_color, leaf_points)
    
    # Layer 7: Atmospheric effects and light filtering
    random.seed(131415)
    for light_effect in range(8):
        light_x = random.randint(int(s_w*0.1), int(s_w*0.9))
        light_y = random.randint(int(s_h*0.1), int(s_h*0.6))
        
        # Dappled light filtering through willow leaves
        light_intensity = random.uniform(0.2, 0.6)
        light_size = random.randint(2, 4)
        
        light_surf = pygame.Surface((light_size * 2, light_size * 2), pygame.SRCALPHA)
        light_alpha = int(255 * light_intensity * 0.3)
        pygame.draw.circle(light_surf, (*leaf_accent, light_alpha), 
                          (light_size, light_size), light_size)
        tile_surface.blit(light_surf, (light_x - light_size, light_y - light_size), 
                         special_flags=pygame.BLEND_ADD)
    
    # Layer 8: Subtle wind movement indicators
    for wind_hint in range(4):
        wind_x = random.randint(int(s_w*0.2), int(s_w*0.8))
        wind_y = random.randint(int(s_h*0.2), int(s_h*0.5))
        
        # Directional blur effect to suggest movement
        wind_surf = pygame.Surface((4, 2), pygame.SRCALPHA)
        pygame.draw.ellipse(wind_surf, (*leaf_light, 40), (0, 0, 4, 2))
        tile_surface.blit(wind_surf, (wind_x - 2, wind_y), 
                         special_flags=pygame.BLEND_ALPHA_SDL2)
    
    return tile_surface


def get_hill_tile(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a hill tile procedurally."""
    tile_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    hill_color = (107, 142, 35)         # Olive drab
    dark_hill_color = (85, 107, 47)     # Dark olive green
    grass_color = (34, 139, 34)         # Forest green

    # Fill with grass background
    tile_surface.fill(grass_color)

    # Hill shape (gentler than mountain)
    hill_points = [
        (0, s_h),                       # Bottom left
        (s_w*0.2, s_h*0.6),            # Left slope
        (s_w*0.5, s_h*0.3),            # Hill peak
        (s_w*0.8, s_h*0.6),            # Right slope
        (s_w, s_h),                     # Bottom right
    ]
    pygame.draw.polygon(tile_surface, hill_color, hill_points)

    # Add shading on right side
    shadow_points = [
        (s_w*0.5, s_h*0.3),            # Hill peak
        (s_w*0.8, s_h*0.6),            # Right slope
        (s_w, s_h),                     # Bottom right
        (s_w*0.7, s_h),                 # Shadow bottom
    ]
    pygame.draw.polygon(tile_surface, dark_hill_color, shadow_points)

    # Add grass texture on top
    import random
    random.seed(321)  # Consistent randomness

    for _ in range(20):
        x = random.randint(int(s_w*0.2), int(s_w*0.8))
        y = random.randint(int(s_h*0.3), int(s_h*0.7))
        # Only draw grass if it's on the hill (simple height check)
        if y > s_h*0.3 + (abs(x - s_w*0.5) / (s_w*0.3)) * (s_h*0.3):
            pygame.draw.circle(tile_surface, grass_color, (x, y), 1)

    return tile_surface


def get_swamp_tile(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """
    Generate a detailed swamp tile procedurally.
    
    Creates a realistic swamp with:
    - Murky water with organic matter
    - Floating debris and vegetation
    - Methane bubbles and gas pockets
    - Mud patches and organic sediment
    - Decomposing plant matter
    - Subtle mist and atmospheric effects
    - Insect life and activity
    """
    tile_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Expanded color palette for realistic swamp
    swamp_base = (47, 79, 79)            # Dark slate gray water
    swamp_dark = (25, 50, 50)            # Very dark swamp water
    swamp_light = (70, 100, 100)         # Lighter swamp areas
    
    # Natural elements
    mud_color = (101, 67, 33)            # Brown mud
    mud_dark = (80, 50, 25)              # Dark mud
    algae_color = (85, 107, 47)          # Dark olive green algae
    algae_bright = (107, 142, 35)        # Brighter algae
    scum_color = (139, 149, 85)          # Surface scum
    
    # Organic matter
    debris_color = (90, 65, 45)          # Floating debris
    debris_dark = (70, 50, 30)           # Dark debris
    leaf_color = (100, 85, 45)           # Decomposing leaves
    root_color = (120, 80, 50)           # Visible roots
    
    # Atmospheric effects
    bubble_color = (105, 105, 105)       # Methane bubbles
    gas_color = (85, 95, 85)             # Gas pockets
    mist_color = (180, 190, 180)         # Swamp mist
    
    # Fill base with swamp water
    tile_surface.fill(swamp_base)
    
    # Create natural swamp texture base
    import random
    random.seed(654)  # Consistent randomness for tiling
    
    # Layer 1: Water depth variations
    for _ in range(20):
        water_x = random.randint(0, s_w - 5)
        water_y = random.randint(0, s_h - 5)
        water_size = random.randint(3, 7)
        water_shade = random.choice([swamp_dark, swamp_light])
        
        # Create water depth patches
        for _ in range(water_size):
            depth_x = water_x + random.randint(-2, 2)
            depth_y = water_y + random.randint(-2, 2)
            if 0 <= depth_x < s_w and 0 <= depth_y < s_h:
                pygame.draw.circle(tile_surface, water_shade, (depth_x, depth_y), 1)
    
    # Layer 2: Mud patches and sediment
    for _ in range(10):
        mud_x = random.randint(1, s_w - 4)
        mud_y = random.randint(1, s_h - 4)
        mud_size = random.randint(3, 8)
        mud_shade = random.choice([mud_color, mud_dark])
        
        # Draw irregular mud patches
        pygame.draw.circle(tile_surface, mud_shade, (mud_x, mud_y), mud_size)
        
        # Add texture to mud
        for _ in range(mud_size):
            texture_x = mud_x + random.randint(-mud_size, mud_size)
            texture_y = mud_y + random.randint(-mud_size, mud_size)
            if 0 <= texture_x < s_w and 0 <= texture_y < s_h:
                texture_shade = random.choice([mud_dark, debris_dark])
                pygame.draw.circle(tile_surface, texture_shade, (texture_x, texture_y), 1)
    
    # Layer 3: Algae and surface vegetation
    for _ in range(15):
        algae_x = random.randint(1, s_w - 3)
        algae_y = random.randint(1, s_h - 3)
        algae_size = random.randint(2, 5)
        algae_shade = random.choice([algae_color, algae_bright])
        
        # Draw algae clusters
        pygame.draw.circle(tile_surface, algae_shade, (algae_x, algae_y), algae_size)
        
        # Add algae filaments
        for _ in range(3):
            strand_x = algae_x + random.randint(-4, 4)
            strand_y = algae_y + random.randint(-4, 4)
            strand_length = random.randint(2, 5)
            strand_angle = random.uniform(0, math.pi * 2)
            
            strand_end_x = strand_x + int(math.cos(strand_angle) * strand_length)
            strand_end_y = strand_y + int(math.sin(strand_angle) * strand_length)
            
            if 0 <= strand_end_x < s_w and 0 <= strand_end_y < s_h:
                pygame.draw.line(tile_surface, algae_color, (strand_x, strand_y), (strand_end_x, strand_end_y), 1)
    
    # Layer 4: Floating debris and organic matter
    for _ in range(12):
        debris_x = random.randint(2, s_w - 3)
        debris_y = random.randint(2, s_h - 3)
        debris_type = random.choice(['stick', 'leaf', 'root'])
        
        if debris_type == 'stick':
            # Draw floating stick
            stick_length = random.randint(4, 8)
            stick_angle = random.uniform(0, math.pi * 2)
            stick_end_x = debris_x + int(math.cos(stick_angle) * stick_length)
            stick_end_y = debris_y + int(math.sin(stick_angle) * stick_length)
            
            if 0 <= stick_end_x < s_w and 0 <= stick_end_y < s_h:
                pygame.draw.line(tile_surface, debris_color, (debris_x, debris_y), (stick_end_x, stick_end_y), 2)
        
        elif debris_type == 'leaf':
            # Draw decomposing leaf
            leaf_points = [
                (debris_x, debris_y - 2),
                (debris_x + 2, debris_y - 1),
                (debris_x + 1, debris_y + 1),
                (debris_x - 1, debris_y + 1),
                (debris_x - 2, debris_y - 1)
            ]
            pygame.draw.polygon(tile_surface, leaf_color, leaf_points)
        
        else:  # root
            # Draw visible root
            root_segments = 3
            root_x, root_y = debris_x, debris_y
            for segment in range(root_segments):
                next_x = root_x + random.randint(-2, 2)
                next_y = root_y + random.randint(-2, 2)
                if 0 <= next_x < s_w and 0 <= next_y < s_h:
                    pygame.draw.line(tile_surface, root_color, (root_x, root_y), (next_x, next_y), 1)
                    root_x, root_y = next_x, next_y
    
    # Layer 5: Methane bubbles and gas pockets
    for _ in range(8):
        bubble_x = random.randint(2, s_w - 3)
        bubble_y = random.randint(2, s_h - 3)
        bubble_size = random.randint(1, 3)
        
        # Draw bubble with slight transparency effect
        pygame.draw.circle(tile_surface, bubble_color, (bubble_x, bubble_y), bubble_size)
        
        # Add bubble highlight
        if bubble_size > 1:
            pygame.draw.circle(tile_surface, (150, 150, 150), (bubble_x - 1, bubble_y - 1), 1)
    
    # Layer 6: Surface scum and film
    for _ in range(10):
        scum_x = random.randint(1, s_w - 4)
        scum_y = random.randint(1, s_h - 4)
        scum_size = random.randint(2, 4)
        
        # Draw irregular scum patches
        for _ in range(scum_size):
            scum_spot_x = scum_x + random.randint(-2, 2)
            scum_spot_y = scum_y + random.randint(-2, 2)
            if 0 <= scum_spot_x < s_w and 0 <= scum_spot_y < s_h:
                pygame.draw.circle(tile_surface, scum_color, (scum_spot_x, scum_spot_y), 1)
    
    # Layer 7: Atmospheric mist (very subtle)
    for _ in range(5):
        mist_x = random.randint(0, s_w - 1)
        mist_y = random.randint(0, int(s_h * 0.6))
        mist_size = random.randint(4, 8)
        
        # Create soft mist effect
        mist_surf = pygame.Surface((mist_size * 2, mist_size * 2), pygame.SRCALPHA)
        mist_alpha = random.randint(15, 35)
        pygame.draw.circle(mist_surf, (*mist_color, mist_alpha), 
                          (mist_size, mist_size), mist_size)
        tile_surface.blit(mist_surf, (mist_x - mist_size, mist_y - mist_size), 
                         special_flags=pygame.BLEND_ALPHA_SDL2)
    
    # Layer 8: Insect activity and life signs
    for _ in range(4):
        insect_x = random.randint(2, s_w - 3)
        insect_y = random.randint(2, s_h - 3)
        
        # Draw mosquito or fly activity
        if random.random() < 0.3:  # 30% chance for visible insects
            pygame.draw.circle(tile_surface, (40, 40, 20), (insect_x, insect_y), 1)
            
            # Add flight path hint
            if random.random() < 0.5:
                path_x = insect_x + random.randint(-3, 3)
                path_y = insect_y + random.randint(-3, 3)
                if 0 <= path_x < s_w and 0 <= path_y < s_h:
                    pygame.draw.circle(tile_surface, (60, 60, 40), (path_x, path_y), 1)
    
    # Layer 9: Gas disturbances and ripples
    for _ in range(6):
        gas_x = random.randint(2, s_w - 3)
        gas_y = random.randint(2, s_h - 3)
        
        # Create subtle gas disturbance
        disturbance_surf = pygame.Surface((6, 6), pygame.SRCALPHA)
        disturbance_alpha = random.randint(20, 50)
        pygame.draw.circle(disturbance_surf, (*gas_color, disturbance_alpha), (3, 3), 3)
        tile_surface.blit(disturbance_surf, (gas_x - 3, gas_y - 3), 
                         special_flags=pygame.BLEND_ALPHA_SDL2)
    
    # Layer 10: Reed clumps with cattails
    random.seed(777)  # Different seed for reeds
    
    # Reed and cattail colors
    reed_green = (60, 100, 60)           # Dark green reed stems
    reed_brown = (101, 67, 33)           # Brown reed stems
    reed_light = (80, 120, 80)           # Light green reed variation
    cattail_brown = (139, 69, 19)        # Brown cattail heads
    cattail_dark = (92, 51, 23)          # Dark cattail heads
    
    # Generate 2-5 reed clumps randomly placed
    num_reed_clumps = random.randint(2, 5)
    
    for clump_id in range(num_reed_clumps):
        # Random position for each clump (avoid edges to prevent tiling issues)
        clump_x = random.randint(int(s_w * 0.1), int(s_w * 0.9))
        clump_y = random.randint(int(s_h * 0.1), int(s_h * 0.9))
        
        # Random clump size (number of reeds in this clump)
        clump_size = random.randint(3, 8)
        
        # Generate individual reeds in this clump
        for reed_id in range(clump_size):
            # Position reeds around the clump center
            reed_offset_x = random.randint(-4, 4)
            reed_offset_y = random.randint(-4, 4)
            reed_x = clump_x + reed_offset_x
            reed_y = clump_y + reed_offset_y
            
            # Ensure reed stays within tile bounds
            if not (2 <= reed_x < s_w - 2 and 2 <= reed_y < s_h - 2):
                continue
                
            # Random reed height (less than 1/3 of tile height as requested)
            max_reed_height = int(s_h * 0.33)
            reed_height = random.randint(int(max_reed_height * 0.4), max_reed_height)
            
            # Random reed properties
            reed_color = random.choice([reed_green, reed_brown, reed_light])
            has_cattail = random.random() < 0.6  # 60% chance for cattail
            reed_segments = random.randint(3, 6)
            
            # Draw reed stem in segments for natural curve
            current_x = reed_x
            current_y = reed_y
            segment_height = reed_height / reed_segments
            
            for segment in range(reed_segments):
                segment_progress = segment / (reed_segments - 1)
                
                # Calculate next position with slight natural curve
                curve_amount = math.sin(segment_progress * math.pi * 2) * 1.5
                next_x = current_x + curve_amount
                next_y = current_y - segment_height
                
                # Ensure segment stays in bounds
                if next_y < 0:
                    next_y = 0
                if next_x < 0:
                    next_x = 0
                elif next_x >= s_w:
                    next_x = s_w - 1
                
                # Draw reed segment
                if current_y >= 0 and next_y >= 0:
                    pygame.draw.line(tile_surface, reed_color, 
                                   (int(current_x), int(current_y)), 
                                   (int(next_x), int(next_y)), 1)
                
                # Add reed thickness variation
                if segment < reed_segments - 1:  # Not the top segment
                    thickness = random.choice([1, 2])
                    if thickness == 2:
                        pygame.draw.line(tile_surface, reed_color, 
                                       (int(current_x) + 1, int(current_y)), 
                                       (int(next_x) + 1, int(next_y)), 1)
                
                current_x = next_x
                current_y = next_y
            
            # Add cattail head if this reed has one
            if has_cattail and current_y >= 0:
                cattail_head_x = int(current_x)
                cattail_head_y = int(current_y)
                
                # Draw cattail head (elongated oval)
                cattail_color = random.choice([cattail_brown, cattail_dark])
                cattail_width = random.randint(2, 4)
                cattail_height = random.randint(4, 7)
                
                # Draw cattail as filled ellipse
                cattail_rect = pygame.Rect(cattail_head_x - cattail_width//2, 
                                         cattail_head_y - cattail_height//2,
                                         cattail_width, cattail_height)
                pygame.draw.ellipse(tile_surface, cattail_color, cattail_rect)
                
                # Add cattail texture (horizontal lines)
                for texture_line in range(2, cattail_height - 1):
                    texture_y = cattail_head_y - cattail_height//2 + texture_line
                    if 0 <= texture_y < s_h:
                        pygame.draw.line(tile_surface, cattail_dark,
                                       (cattail_head_x - cattail_width//2 + 1, texture_y),
                                       (cattail_head_x + cattail_width//2 - 1, texture_y), 1)
                
                # Add cattail highlight
                if cattail_width > 2:
                    highlight_x = cattail_head_x - cattail_width//2 + 1
                    highlight_y = cattail_head_y - cattail_height//2 + 1
                    if 0 <= highlight_x < s_w and 0 <= highlight_y < s_h:
                        pygame.draw.circle(tile_surface, (180, 140, 100), 
                                         (highlight_x, highlight_y), 1)
            
            # Add small reed leaves (optional detail)
            if random.random() < 0.4:  # 40% chance for visible leaves
                leaf_count = random.randint(1, 3)
                for leaf in range(leaf_count):
                    leaf_height = random.randint(int(reed_height * 0.3), int(reed_height * 0.7))
                    leaf_y = reed_y - leaf_height
                    
                    if leaf_y >= 0:
                        # Draw small leaf blade
                        leaf_length = random.randint(3, 6)
                        leaf_angle = random.uniform(-0.5, 0.5)  # Slight angle
                        leaf_end_x = reed_x + math.cos(leaf_angle) * leaf_length
                        leaf_end_y = leaf_y + math.sin(leaf_angle) * leaf_length * 0.3
                        
                        if 0 <= leaf_end_x < s_w and 0 <= leaf_end_y < s_h:
                            leaf_color = random.choice([reed_green, reed_light])
                            pygame.draw.line(tile_surface, leaf_color,
                                           (reed_x, int(leaf_y)), 
                                           (int(leaf_end_x), int(leaf_end_y)), 1)
    
    return tile_surface


def get_cobblestone_path_tile(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a cobblestone path tile procedurally."""
    tile_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    cobble_color = (169, 169, 169)      # Dark gray
    dark_cobble_color = (105, 105, 105) # Dim gray
    light_cobble_color = (192, 192, 192) # Silver
    mortar_color = (128, 128, 128)      # Gray mortar

    # Fill base with mortar
    tile_surface.fill(mortar_color)

    # Draw cobblestones in a pattern
    import random
    random.seed(987)  # Consistent randomness

    stone_size = s_w // 6
    for y in range(0, s_h, stone_size + 1):
        for x in range(0, s_w, stone_size + 1):
            # Add some variation in positioning
            offset_x = random.randint(-1, 1)
            offset_y = random.randint(-1, 1)

            stone_x = x + offset_x
            stone_y = y + offset_y

            # Ensure stone stays within bounds
            if stone_x >= 0 and stone_y >= 0 and stone_x + stone_size < s_w and stone_y + stone_size < s_h:
                # Choose stone color
                color = random.choice([cobble_color, dark_cobble_color, light_cobble_color])

                # Draw irregular stone shape (circle with some variation)
                stone_radius = stone_size // 2 + random.randint(-1, 1)
                center_x = stone_x + stone_size // 2
                center_y = stone_y + stone_size // 2

                pygame.draw.circle(tile_surface, color, (center_x, center_y), stone_radius)

                # Add highlight for 3D effect
                highlight_color = (min(255, color[0] + 30), min(255, color[1] + 30), min(255, color[2] + 30))
                pygame.draw.circle(tile_surface, highlight_color, (center_x - 1, center_y - 1), stone_radius // 2)

    return tile_surface


def get_wood_floor_tile(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """
    Generate a detailed wood floor tile procedurally.
    
    Creates realistic wooden flooring with:
    - Individual wood planks with natural grain patterns
    - Varied wood tones and aging effects
    - Realistic wood joints and nail holes
    - Subtle wear patterns and scuff marks
    - Seamless tiling edges
    """
    tile_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size
    
    # Enhanced wood color palette
    wood_base = (139, 69, 19)           # Saddle brown base
    wood_dark = (101, 67, 33)           # Dark brown grain
    wood_light = (160, 82, 45)          # Light brown highlights
    wood_red = (120, 60, 30)            # Reddish wood tint
    wood_yellow = (150, 85, 40)         # Yellowish wood tint
    
    # Detail colors
    nail_color = (80, 60, 40)           # Dark nail holes
    joint_color = (90, 50, 25)          # Wood joints
    wear_color = (180, 100, 60)         # Worn areas
    
    # Fill base with medium wood tone
    tile_surface.fill(wood_base)
    
    # Create consistent randomness for tiling
    random.seed(456)
    
    # Layer 1: Individual wood planks (horizontal)
    plank_height = s_h // 4  # 4 planks per tile
    for plank_num in range(4):
        plank_y = plank_num * plank_height
        plank_rect = pygame.Rect(0, plank_y, s_w, plank_height - 1)
        
        # Vary wood tone for each plank
        plank_base = random.choice([wood_base, wood_light, wood_red, wood_yellow])
        pygame.draw.rect(tile_surface, plank_base, plank_rect)
        
        # Add wood grain lines
        grain_lines = random.randint(2, 4)
        for grain in range(grain_lines):
            grain_y = plank_y + random.randint(1, plank_height - 2)
            grain_start = random.randint(0, s_w // 4)
            grain_end = random.randint(3 * s_w // 4, s_w)
            grain_color = random.choice([wood_dark, wood_light])
            
            # Draw slightly wavy grain line
            for x in range(grain_start, grain_end, 2):
                wave_y = grain_y + int(math.sin(x * 0.3) * 0.5)
                if 0 <= wave_y < s_h:
                    pygame.draw.circle(tile_surface, grain_color, (x, wave_y), 1)
    
    # Layer 2: Wood joints between planks
    for plank_num in range(1, 4):  # Don't draw joint at top edge
        joint_y = plank_num * plank_height - 1
        pygame.draw.line(tile_surface, joint_color, (0, joint_y), (s_w, joint_y), 1)
        
        # Add slight shadow below joint
        if joint_y + 1 < s_h:
            pygame.draw.line(tile_surface, wood_dark, (0, joint_y + 1), (s_w, joint_y + 1), 1)
    
    # Layer 3: Nail holes and hardware details
    for plank_num in range(4):
        plank_center_y = plank_num * plank_height + plank_height // 2
        
        # Left side nails
        nail_x = s_w // 8
        pygame.draw.circle(tile_surface, nail_color, (nail_x, plank_center_y), 1)
        
        # Right side nails
        nail_x = 7 * s_w // 8
        pygame.draw.circle(tile_surface, nail_color, (nail_x, plank_center_y), 1)
        
        # Occasional center nail
        if random.random() < 0.3:
            center_nail_x = s_w // 2 + random.randint(-4, 4)
            pygame.draw.circle(tile_surface, nail_color, (center_nail_x, plank_center_y), 1)
    
    # Layer 4: Wear patterns and aging
    for _ in range(8):
        wear_x = random.randint(s_w // 4, 3 * s_w // 4)
        wear_y = random.randint(s_h // 4, 3 * s_h // 4)
        wear_size = random.randint(2, 5)
        
        # Create worn area with lighter color
        for _ in range(wear_size):
            w_x = wear_x + random.randint(-3, 3)
            w_y = wear_y + random.randint(-2, 2)
            if 0 <= w_x < s_w and 0 <= w_y < s_h:
                pygame.draw.circle(tile_surface, wear_color, (w_x, w_y), 1)
    
    # Layer 5: Wood knots and natural imperfections
    for _ in range(3):
        knot_x = random.randint(s_w // 6, 5 * s_w // 6)
        knot_y = random.randint(s_h // 6, 5 * s_h // 6)
        knot_size = random.randint(2, 4)
        
        # Draw wood knot
        pygame.draw.circle(tile_surface, wood_dark, (knot_x, knot_y), knot_size)
        pygame.draw.circle(tile_surface, nail_color, (knot_x, knot_y), knot_size - 1)
        
        # Add knot ring detail
        if knot_size > 2:
            pygame.draw.circle(tile_surface, wood_base, (knot_x, knot_y), 1)
    
    # Layer 6: Fine wood texture details
    for _ in range(20):
        tex_x = random.randint(0, s_w - 1)
        tex_y = random.randint(0, s_h - 1)
        tex_color = random.choice([wood_light, wood_dark])
        
        if random.random() < 0.4:  # 40% chance for texture detail
            pygame.draw.circle(tile_surface, tex_color, (tex_x, tex_y), 1)
    
    return tile_surface


def get_dirt_road_tile(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """
    Generate a detailed dirt road tile procedurally.
    
    Creates a realistic dirt road with:
    - Varied dirt composition and compaction levels
    - Deep wheel ruts from wagon traffic
    - Small stones and pebbles
    - Foot prints and hoof marks
    - Erosion patterns and water damage
    - Road edges blending to grass
    """
    tile_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Enhanced dirt road color palette
    dirt_base = (160, 82, 45)           # Main road surface
    dirt_dark = (139, 69, 19)           # Darker compacted areas
    dirt_light = (205, 133, 63)         # Lighter sandy areas
    dirt_red = (140, 70, 35)            # Clay-rich areas
    
    # Road features
    rut_deep = (101, 67, 33)            # Deep wheel ruts
    rut_shadow = (80, 50, 25)           # Rut shadows
    stone_color = (120, 120, 120)       # Small road stones
    grass_edge = (60, 120, 40)          # Grass growing at edges
    
    # Fill base with road surface
    tile_surface.fill(dirt_base)
    
    # Create consistent randomness for tiling
    random.seed(654)
    
    # Layer 1: Road surface composition variations
    for _ in range(30):
        patch_x = random.randint(0, s_w - 4)
        patch_y = random.randint(0, s_h - 4)
        patch_size = random.randint(3, 7)
        surface_type = random.choice([dirt_dark, dirt_light, dirt_red])
        
        # Create organic surface patches
        for _ in range(patch_size):
            surf_x = patch_x + random.randint(-2, 2)
            surf_y = patch_y + random.randint(-2, 2)
            if 0 <= surf_x < s_w and 0 <= surf_y < s_h:
                pygame.draw.circle(tile_surface, surface_type, (surf_x, surf_y), 1)
    
    # Layer 2: Deep wheel ruts (main road feature)
    left_rut_x = s_w // 4
    right_rut_x = 3 * s_w // 4
    
    for y in range(0, s_h, 2):
        # Add natural variation to rut positions
        left_var = random.randint(-1, 1)
        right_var = random.randint(-1, 1)
        
        # Left wheel rut
        rut_x = max(2, min(s_w - 3, left_rut_x + left_var))
        pygame.draw.circle(tile_surface, rut_deep, (rut_x, y), 2)
        # Add rut depth shadow
        pygame.draw.circle(tile_surface, rut_shadow, (rut_x - 1, y), 1)
        
        # Right wheel rut
        rut_x = max(2, min(s_w - 3, right_rut_x + right_var))
        pygame.draw.circle(tile_surface, rut_deep, (rut_x, y), 2)
        # Add rut depth shadow
        pygame.draw.circle(tile_surface, rut_shadow, (rut_x - 1, y), 1)
    
    # Layer 3: Road stones and gravel
    for _ in range(12):
        stone_x = random.randint(1, s_w - 2)
        stone_y = random.randint(1, s_h - 2)
        stone_size = random.randint(1, 3)
        
        # Avoid placing stones directly in wheel ruts
        rut_distance = min(abs(stone_x - left_rut_x), abs(stone_x - right_rut_x))
        if rut_distance > 3:
            pygame.draw.circle(tile_surface, stone_color, (stone_x, stone_y), stone_size)
            # Add stone shadow
            pygame.draw.circle(tile_surface, dirt_dark, (stone_x + 1, stone_y + 1), stone_size - 1)
    
    # Layer 4: Foot prints and hoof marks
    for _ in range(6):
        print_x = random.randint(s_w // 6, 5 * s_w // 6)
        print_y = random.randint(s_h // 6, 5 * s_h // 6)
        
        # Avoid wheel rut areas
        rut_distance = min(abs(print_x - left_rut_x), abs(print_x - right_rut_x))
        if rut_distance > 5:
            if random.random() < 0.6:  # Foot print
                pygame.draw.ellipse(tile_surface, dirt_dark, (print_x - 2, print_y - 3, 4, 6))
            else:  # Hoof mark
                pygame.draw.circle(tile_surface, dirt_dark, (print_x, print_y), 2)
                pygame.draw.circle(tile_surface, dirt_dark, (print_x, print_y - 3), 2)
    
    # Layer 5: Grass encroachment at road edges
    # Left edge
    for _ in range(8):
        edge_x = random.randint(0, s_w // 8)
        edge_y = random.randint(0, s_h - 1)
        pygame.draw.circle(tile_surface, grass_edge, (edge_x, edge_y), 1)
    
    # Right edge
    for _ in range(8):
        edge_x = random.randint(7 * s_w // 8, s_w - 1)
        edge_y = random.randint(0, s_h - 1)
        pygame.draw.circle(tile_surface, grass_edge, (edge_x, edge_y), 1)
    
    # Layer 6: Road wear patterns and compaction
    for _ in range(15):
        wear_x = random.randint(s_w // 8, 7 * s_w // 8)
        wear_y = random.randint(0, s_h - 1)
        
        # More wear in center of road
        if s_w // 3 <= wear_x <= 2 * s_w // 3:
            wear_color = dirt_dark  # More compacted
        else:
            wear_color = dirt_light  # Less traveled
        
        pygame.draw.circle(tile_surface, wear_color, (wear_x, wear_y), 1)
    
    # Layer 7: Water erosion patterns (subtle)
    for _ in range(4):
        erosion_start_x = random.randint(0, s_w // 2)
        erosion_start_y = random.randint(0, s_h - 1)
        erosion_length = random.randint(8, 15)
        erosion_angle = random.uniform(-0.3, 0.3)  # Slight diagonal
        
        # Draw erosion channel
        for step in range(erosion_length):
            erode_x = erosion_start_x + step
            erode_y = erosion_start_y + int(step * erosion_angle)
            
            if 0 <= erode_x < s_w and 0 <= erode_y < s_h:
                pygame.draw.circle(tile_surface, dirt_dark, (erode_x, erode_y), 1)
    
    return tile_surface


def get_wood_fence_tile(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """
    Generate a detailed wood fence tile procedurally.
    
    Creates a tileable wooden fence with:
    - Weathered wooden posts and rails
    - Natural wood grain and aging effects
    - Post alignment for seamless tiling
    - Grass background with varied texture
    - Realistic shadowing and depth
    """
    tile_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Enhanced wood color palette
    wood_base = (139, 69, 19)           # Saddle brown base
    wood_dark = (101, 67, 33)           # Dark brown shadows
    wood_light = (160, 82, 45)          # Light brown highlights
    wood_weathered = (120, 60, 30)      # Weathered wood
    wood_grain = (90, 50, 25)           # Grain lines
    
    # Metal hardware colors
    nail_color = (80, 80, 80)           # Dark gray nails
    rust_color = (120, 80, 60)          # Rust stains
    
    # Grass colors
    grass_base = (34, 139, 34)          # Forest green base
    grass_dark = (20, 100, 20)          # Dark grass shadows
    grass_light = (50, 160, 50)         # Light grass highlights
    grass_yellow = (60, 140, 30)        # Yellow-green variation

    # Fill with varied grass background
    tile_surface.fill(grass_base)
    
    # Create consistent randomness for tiling
    random.seed(123)
    
    # Layer 1: Enhanced grass texture
    for _ in range(35):
        grass_x = random.randint(0, s_w - 1)
        grass_y = random.randint(int(s_h * 0.7), s_h - 1)  # Lower portion only
        grass_shade = random.choice([grass_dark, grass_light, grass_yellow, grass_base])
        grass_size = random.randint(1, 2)
        pygame.draw.circle(tile_surface, grass_shade, (grass_x, grass_y), grass_size)
    
    # Layer 2: Fence posts - positioned for perfect tiling
    post_width = max(3, s_w // 10)
    post_height = int(s_h * 0.75)
    post_y = int(s_h * 0.1)
    
    # Center post (main post for this tile)
    center_post = pygame.Rect(s_w // 2 - post_width // 2, post_y, post_width, post_height)
    pygame.draw.rect(tile_surface, wood_base, center_post)
    
    # Add wood grain to center post
    for grain_line in range(3):
        grain_y = center_post.y + (grain_line + 1) * center_post.height // 4
        grain_start = center_post.left + 1
        grain_end = center_post.right - 1
        pygame.draw.line(tile_surface, wood_grain, (grain_start, grain_y), (grain_end, grain_y), 1)
    
    # Post shadows and highlights for 3D effect
    # Right shadow
    pygame.draw.line(tile_surface, wood_dark, 
                    (center_post.right - 1, center_post.top), 
                    (center_post.right - 1, center_post.bottom), 1)
    # Left highlight
    pygame.draw.line(tile_surface, wood_light, 
                    (center_post.left, center_post.top), 
                    (center_post.left, center_post.bottom), 1)
    
    # Edge posts (for tiling continuity) - only show edges
    edge_post_width = post_width // 2
    
    # Left edge post
    left_edge_post = pygame.Rect(0, post_y, edge_post_width, post_height)
    pygame.draw.rect(tile_surface, wood_base, left_edge_post)
    
    # Right edge post  
    right_edge_post = pygame.Rect(s_w - edge_post_width, post_y, edge_post_width, post_height)
    pygame.draw.rect(tile_surface, wood_base, right_edge_post)
    
    # Layer 3: Horizontal rails
    rail_height = max(2, s_h // 16)
    rail_depth = 2  # 3D depth effect
    
    # Top rail
    top_rail_y = int(s_h * 0.25)
    top_rail = pygame.Rect(0, top_rail_y, s_w, rail_height)
    pygame.draw.rect(tile_surface, wood_light, top_rail)
    
    # Top rail shadow (below)
    shadow_rail = pygame.Rect(0, top_rail_y + rail_height, s_w, 1)
    pygame.draw.rect(tile_surface, wood_dark, shadow_rail)
    
    # Bottom rail
    bottom_rail_y = int(s_h * 0.6)
    bottom_rail = pygame.Rect(0, bottom_rail_y, s_w, rail_height)
    pygame.draw.rect(tile_surface, wood_light, bottom_rail)
    
    # Bottom rail shadow
    shadow_rail_bottom = pygame.Rect(0, bottom_rail_y + rail_height, s_w, 1)
    pygame.draw.rect(tile_surface, wood_dark, shadow_rail_bottom)
    
    # Layer 4: Wood details and aging
    # Add wood weathering to posts
    for weather_spot in range(4):
        spot_x = center_post.left + random.randint(1, center_post.width - 2)
        spot_y = center_post.top + random.randint(center_post.height // 4, 3 * center_post.height // 4)
        spot_size = random.randint(1, 2)
        pygame.draw.circle(tile_surface, wood_weathered, (spot_x, spot_y), spot_size)
    
    # Layer 5: Hardware details
    # Nails connecting rails to posts
    nail_size = 1
    
    # Top rail nails on center post
    pygame.draw.circle(tile_surface, nail_color, 
                      (center_post.centerx - 1, top_rail_y + rail_height // 2), nail_size)
    pygame.draw.circle(tile_surface, nail_color, 
                      (center_post.centerx + 1, top_rail_y + rail_height // 2), nail_size)
    
    # Bottom rail nails on center post
    pygame.draw.circle(tile_surface, nail_color, 
                      (center_post.centerx - 1, bottom_rail_y + rail_height // 2), nail_size)
    pygame.draw.circle(tile_surface, nail_color, 
                      (center_post.centerx + 1, bottom_rail_y + rail_height // 2), nail_size)
    
    # Subtle rust stains near nails
    for rust_spot in range(2):
        rust_x = center_post.centerx + random.randint(-3, 3)
        rust_y = top_rail_y + random.randint(-2, rail_height + 2)
        pygame.draw.circle(tile_surface, rust_color, (rust_x, rust_y), 1)
    
    # Layer 6: Environmental details
    # Small moss/lichen on posts (weathering)
    moss_color = (60, 100, 60)
    for moss in range(2):
        moss_x = center_post.left + random.randint(0, center_post.width - 1)
        moss_y = center_post.top + random.randint(center_post.height // 2, center_post.height - 1)
        pygame.draw.circle(tile_surface, moss_color, (moss_x, moss_y), 1)
    
    # Ground vegetation around posts
    for veg in range(6):
        veg_x = center_post.centerx + random.randint(-post_width, post_width)
        veg_y = center_post.bottom + random.randint(0, s_h - center_post.bottom - 1)
        if 0 <= veg_x < s_w and 0 <= veg_y < s_h:
            veg_color = random.choice([grass_dark, grass_light])
            pygame.draw.circle(tile_surface, veg_color, (veg_x, veg_y), 1)

    return tile_surface


def get_wood_fence_gate_tile(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """
    Generate a detailed wood fence gate tile procedurally (closed state).
    
    Creates a gate that perfectly matches the fence tile design with:
    - Gate posts aligned with fence post style
    - Double gate panels that meet in the center
    - Realistic hinges and hardware
    - Wood aging and weathering effects
    - Consistent styling with fence tiles
    """
    tile_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Enhanced wood color palette (matching fence)
    wood_base = (139, 69, 19)           # Saddle brown base
    wood_dark = (101, 67, 33)           # Dark brown shadows
    wood_light = (160, 82, 45)          # Light brown highlights
    wood_weathered = (120, 60, 30)      # Weathered wood
    wood_grain = (90, 50, 25)           # Grain lines
    
    # Metal hardware colors
    metal_color = (80, 80, 80)          # Dark gray metal
    metal_highlight = (120, 120, 120)   # Metal highlights
    rust_color = (120, 80, 60)          # Rust stains
    
    # Grass colors (matching fence)
    grass_base = (34, 139, 34)          # Forest green base
    grass_dark = (20, 100, 20)          # Dark grass shadows
    grass_light = (50, 160, 50)         # Light grass highlights
    grass_yellow = (60, 140, 30)        # Yellow-green variation

    # Fill with varied grass background
    tile_surface.fill(grass_base)
    
    # Create consistent randomness for tiling
    random.seed(456)
    
    # Layer 1: Enhanced grass texture
    for _ in range(30):
        grass_x = random.randint(0, s_w - 1)
        grass_y = random.randint(int(s_h * 0.7), s_h - 1)
        grass_shade = random.choice([grass_dark, grass_light, grass_yellow, grass_base])
        grass_size = random.randint(1, 2)
        pygame.draw.circle(tile_surface, grass_shade, (grass_x, grass_y), grass_size)
    
    # Layer 2: Gate posts - slightly thicker than fence posts
    post_width = max(3, s_w // 8)
    post_height = int(s_h * 0.75)
    post_y = int(s_h * 0.1)
    
    # Left gate post
    left_post = pygame.Rect(s_w // 8, post_y, post_width, post_height)
    pygame.draw.rect(tile_surface, wood_base, left_post)
    
    # Right gate post
    right_post = pygame.Rect(s_w - s_w // 8 - post_width, post_y, post_width, post_height)
    pygame.draw.rect(tile_surface, wood_base, right_post)
    
    # Add wood grain to posts
    for post_rect in [left_post, right_post]:
        for grain_line in range(3):
            grain_y = post_rect.y + (grain_line + 1) * post_rect.height // 4
            grain_start = post_rect.left + 1
            grain_end = post_rect.right - 1
            pygame.draw.line(tile_surface, wood_grain, (grain_start, grain_y), (grain_end, grain_y), 1)
        
        # Post shadows and highlights
        pygame.draw.line(tile_surface, wood_dark, 
                        (post_rect.right - 1, post_rect.top), 
                        (post_rect.right - 1, post_rect.bottom), 1)
        pygame.draw.line(tile_surface, wood_light, 
                        (post_rect.left, post_rect.top), 
                        (post_rect.left, post_rect.bottom), 1)
    
    # Layer 3: Gate panels (closed) - two panels meeting in the center
    gate_height = int(s_h * 0.5)
    gate_y = int(s_h * 0.25)
    gate_gap = 2  # Small gap between panels
    
    # Calculate panel dimensions
    available_width = right_post.left - left_post.right - 2  # 2 pixels padding
    panel_width = (available_width - gate_gap) // 2
    
    # Left gate panel
    left_panel = pygame.Rect(left_post.right + 1, gate_y, panel_width, gate_height)
    pygame.draw.rect(tile_surface, wood_light, left_panel)
    pygame.draw.rect(tile_surface, wood_dark, left_panel, 1)
    
    # Right gate panel
    right_panel_x = left_panel.right + gate_gap
    right_panel = pygame.Rect(right_panel_x, gate_y, panel_width, gate_height)
    pygame.draw.rect(tile_surface, wood_light, right_panel)
    pygame.draw.rect(tile_surface, wood_dark, right_panel, 1)
    
    # Layer 4: Vertical slats on gate panels
    slat_width = 2
    slat_spacing = max(1, panel_width // 4)
    
    for panel_rect in [left_panel, right_panel]:
        for slat_num in range(3):  # 3 slats per panel
            slat_x = panel_rect.left + (slat_num + 1) * slat_spacing
            if slat_x + slat_width <= panel_rect.right:
                slat_rect = pygame.Rect(slat_x, panel_rect.top + 2, slat_width, panel_rect.height - 4)
                pygame.draw.rect(tile_surface, wood_base, slat_rect)
                # Add slat shadow
                pygame.draw.line(tile_surface, wood_dark, 
                               (slat_rect.right, slat_rect.top), 
                               (slat_rect.right, slat_rect.bottom), 1)
    
    # Layer 5: Cross-braces on panels (diagonal support)
    brace_color = wood_dark
    for panel_rect in [left_panel, right_panel]:
        # Draw diagonal brace from top-left to bottom-right
        brace_start = (panel_rect.left + 2, panel_rect.top + 3)
        brace_end = (panel_rect.right - 2, panel_rect.bottom - 3)
        pygame.draw.line(tile_surface, brace_color, brace_start, brace_end, 1)
    
    # Layer 6: Hinges on left post
    hinge_width = 4
    hinge_height = 6
    hinge_y1 = gate_y + 4
    hinge_y2 = gate_y + gate_height - 10
    
    # Top hinge
    top_hinge = pygame.Rect(left_post.right - 2, hinge_y1, hinge_width, hinge_height)
    pygame.draw.rect(tile_surface, metal_color, top_hinge)
    pygame.draw.rect(tile_surface, metal_highlight, top_hinge, 1)
    
    # Bottom hinge
    bottom_hinge = pygame.Rect(left_post.right - 2, hinge_y2, hinge_width, hinge_height)
    pygame.draw.rect(tile_surface, metal_color, bottom_hinge)
    pygame.draw.rect(tile_surface, metal_highlight, bottom_hinge, 1)
    
    # Hinge pins
    pygame.draw.circle(tile_surface, metal_highlight, 
                      (left_post.right, hinge_y1 + hinge_height // 2), 1)
    pygame.draw.circle(tile_surface, metal_highlight, 
                      (left_post.right, hinge_y2 + hinge_height // 2), 1)
    
    # Layer 7: Gate handle/latch on right panel
    handle_x = right_panel.left + 3
    handle_y = gate_y + gate_height // 2
    
    # Handle plate
    handle_rect = pygame.Rect(handle_x - 1, handle_y - 2, 3, 4)
    pygame.draw.rect(tile_surface, metal_color, handle_rect)
    
    # Handle knob
    pygame.draw.circle(tile_surface, metal_highlight, (handle_x, handle_y), 2)
    pygame.draw.circle(tile_surface, metal_color, (handle_x, handle_y), 1)
    
    # Layer 8: Wood aging and weathering details
    # Weathering on posts
    for post_rect in [left_post, right_post]:
        for weather_spot in range(2):
            spot_x = post_rect.left + random.randint(1, post_rect.width - 2)
            spot_y = post_rect.top + random.randint(post_rect.height // 4, 3 * post_rect.height // 4)
            pygame.draw.circle(tile_surface, wood_weathered, (spot_x, spot_y), 1)
    
    # Weathering on gate panels
    for panel_rect in [left_panel, right_panel]:
        spot_x = panel_rect.left + random.randint(2, panel_rect.width - 3)
        spot_y = panel_rect.top + random.randint(panel_rect.height // 3, 2 * panel_rect.height // 3)
        pygame.draw.circle(tile_surface, wood_weathered, (spot_x, spot_y), 1)
    
    # Rust stains near hinges
    pygame.draw.circle(tile_surface, rust_color, 
                      (left_post.right + 1, hinge_y1 + 2), 1)
    
    # Layer 9: Environmental details
    # Small vegetation around posts
    moss_color = (60, 100, 60)
    for post_rect in [left_post, right_post]:
        for veg in range(3):
            veg_x = post_rect.centerx + random.randint(-post_width // 2, post_width // 2)
            veg_y = post_rect.bottom + random.randint(0, s_h - post_rect.bottom - 1)
            if 0 <= veg_x < s_w and 0 <= veg_y < s_h:
                veg_color = random.choice([moss_color, grass_dark])
                pygame.draw.circle(tile_surface, veg_color, (veg_x, veg_y), 1)

    return tile_surface


def get_wood_fence_gate_open_tile(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """
    Generate a detailed open wood fence gate tile procedurally.
    
    Creates an open gate showing:
    - Gate posts matching the fence design
    - Gate panels swung open to the sides
    - Visible hinges and hardware
    - Enhanced grass in the open area
    - Realistic shadows and depth
    """
    tile_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Enhanced wood color palette (matching fence)
    wood_base = (139, 69, 19)           # Saddle brown base
    wood_dark = (101, 67, 33)           # Dark brown shadows
    wood_light = (160, 82, 45)          # Light brown highlights
    wood_weathered = (120, 60, 30)      # Weathered wood
    wood_grain = (90, 50, 25)           # Grain lines
    
    # Metal hardware colors
    metal_color = (80, 80, 80)          # Dark gray metal
    metal_highlight = (120, 120, 120)   # Metal highlights
    rust_color = (120, 80, 60)          # Rust stains
    
    # Grass colors
    grass_base = (34, 139, 34)          # Forest green base
    grass_dark = (20, 100, 20)          # Dark grass shadows
    grass_light = (50, 160, 50)         # Light grass highlights
    grass_yellow = (60, 140, 30)        # Yellow-green variation
    grass_bright = (70, 180, 70)        # Bright grass in open area

    # Fill with varied grass background
    tile_surface.fill(grass_base)
    
    # Create consistent randomness for tiling
    random.seed(789)
    
    # Layer 1: Enhanced grass texture with more vegetation in open area
    for _ in range(45):
        grass_x = random.randint(0, s_w - 1)
        grass_y = random.randint(int(s_h * 0.6), s_h - 1)
        
        # More lush grass in the center (open gate area)
        if s_w // 4 <= grass_x <= 3 * s_w // 4:
            grass_shade = random.choice([grass_bright, grass_light, grass_yellow])
        else:
            grass_shade = random.choice([grass_dark, grass_light, grass_base])
        
        grass_size = random.randint(1, 2)
        pygame.draw.circle(tile_surface, grass_shade, (grass_x, grass_y), grass_size)
    
    # Layer 2: Gate posts (same as closed gate)
    post_width = max(3, s_w // 8)
    post_height = int(s_h * 0.75)
    post_y = int(s_h * 0.1)
    
    # Left gate post
    left_post = pygame.Rect(s_w // 8, post_y, post_width, post_height)
    pygame.draw.rect(tile_surface, wood_base, left_post)
    
    # Right gate post
    right_post = pygame.Rect(s_w - s_w // 8 - post_width, post_y, post_width, post_height)
    pygame.draw.rect(tile_surface, wood_base, right_post)
    
    # Add wood grain to posts
    for post_rect in [left_post, right_post]:
        for grain_line in range(3):
            grain_y = post_rect.y + (grain_line + 1) * post_rect.height // 4
            grain_start = post_rect.left + 1
            grain_end = post_rect.right - 1
            pygame.draw.line(tile_surface, wood_grain, (grain_start, grain_y), (grain_end, grain_y), 1)
        
        # Post shadows and highlights
        pygame.draw.line(tile_surface, wood_dark, 
                        (post_rect.right - 1, post_rect.top), 
                        (post_rect.right - 1, post_rect.bottom), 1)
        pygame.draw.line(tile_surface, wood_light, 
                        (post_rect.left, post_rect.top), 
                        (post_rect.left, post_rect.bottom), 1)
    
    # Layer 3: Open gate panels - swung to the sides, showing edges
    gate_height = int(s_h * 0.5)
    gate_y = int(s_h * 0.25)
    panel_edge_width = 3
    
    # Left gate panel (swung to the left, showing right edge)
    left_panel_edge = pygame.Rect(s_w // 16, gate_y + 5, panel_edge_width, gate_height - 10)
    pygame.draw.rect(tile_surface, wood_base, left_panel_edge)
    pygame.draw.rect(tile_surface, wood_dark, left_panel_edge, 1)
    
    # Right gate panel (swung to the right, showing left edge)
    right_panel_edge = pygame.Rect(s_w - s_w // 16 - panel_edge_width, gate_y + 5, panel_edge_width, gate_height - 10)
    pygame.draw.rect(tile_surface, wood_base, right_panel_edge)
    pygame.draw.rect(tile_surface, wood_dark, right_panel_edge, 1)
    
    # Add depth lines to show panel thickness
    pygame.draw.line(tile_surface, wood_light, 
                    (left_panel_edge.left, left_panel_edge.top), 
                    (left_panel_edge.left, left_panel_edge.bottom), 1)
    pygame.draw.line(tile_surface, wood_light, 
                    (right_panel_edge.right - 1, right_panel_edge.top), 
                    (right_panel_edge.right - 1, right_panel_edge.bottom), 1)
    
    # Layer 4: Hinges on left post (still visible)
    hinge_width = 4
    hinge_height = 6
    hinge_y1 = gate_y + 4
    hinge_y2 = gate_y + gate_height - 10
    
    # Top hinge
    top_hinge = pygame.Rect(left_post.right - 2, hinge_y1, hinge_width, hinge_height)
    pygame.draw.rect(tile_surface, metal_color, top_hinge)
    pygame.draw.rect(tile_surface, metal_highlight, top_hinge, 1)
    
    # Bottom hinge
    bottom_hinge = pygame.Rect(left_post.right - 2, hinge_y2, hinge_width, hinge_height)
    pygame.draw.rect(tile_surface, metal_color, bottom_hinge)
    pygame.draw.rect(tile_surface, metal_highlight, bottom_hinge, 1)
    
    # Hinge pins
    pygame.draw.circle(tile_surface, metal_highlight, 
                      (left_post.right, hinge_y1 + hinge_height // 2), 1)
    pygame.draw.circle(tile_surface, metal_highlight, 
                      (left_post.right, hinge_y2 + hinge_height // 2), 1)
    
    # Layer 5: Enhanced grass texture in the open passageway
    passageway_left = left_post.right
    passageway_right = right_post.left
    passageway_top = gate_y
    passageway_bottom = gate_y + gate_height
    
    for _ in range(20):
        pass_x = random.randint(passageway_left, passageway_right)
        pass_y = random.randint(passageway_top, passageway_bottom)
        
        # Varied grass types in the walkway
        grass_shade = random.choice([grass_bright, grass_light, grass_yellow, grass_base])
        grass_size = random.randint(1, 2)
        pygame.draw.circle(tile_surface, grass_shade, (pass_x, pass_y), grass_size)
    
    # Layer 6: Small wildflowers in the open area
    flower_colors = [(255, 255, 255), (255, 255, 100), (255, 200, 200)]  # White, yellow, pink
    for _ in range(4):
        flower_x = random.randint(passageway_left + 2, passageway_right - 2)
        flower_y = random.randint(passageway_top + gate_height // 2, passageway_bottom - 2)
        flower_color = random.choice(flower_colors)
        pygame.draw.circle(tile_surface, flower_color, (flower_x, flower_y), 1)
    
    # Layer 7: Wood aging and weathering details
    # Weathering on posts
    for post_rect in [left_post, right_post]:
        for weather_spot in range(2):
            spot_x = post_rect.left + random.randint(1, post_rect.width - 2)
            spot_y = post_rect.top + random.randint(post_rect.height // 4, 3 * post_rect.height // 4)
            pygame.draw.circle(tile_surface, wood_weathered, (spot_x, spot_y), 1)
    
    # Rust stains near hinges
    pygame.draw.circle(tile_surface, rust_color, 
                      (left_post.right + 1, hinge_y1 + 2), 1)
    
    # Layer 8: Environmental details
    # Small vegetation around posts
    moss_color = (60, 100, 60)
    for post_rect in [left_post, right_post]:
        for veg in range(3):
            veg_x = post_rect.centerx + random.randint(-post_width // 2, post_width // 2)
            veg_y = post_rect.bottom + random.randint(0, s_h - post_rect.bottom - 1)
            if 0 <= veg_x < s_w and 0 <= veg_y < s_h:
                veg_color = random.choice([moss_color, grass_dark])
                pygame.draw.circle(tile_surface, veg_color, (veg_x, veg_y), 1)
    
    # Layer 9: Shadows from open gate panels
    # Subtle shadows cast by the swung-open panels
    shadow_color = (20, 80, 20)  # Dark green shadow
    
    # Left panel shadow
    for shadow_line in range(3):
        shadow_x = left_panel_edge.right + shadow_line
        if shadow_x < s_w:
            pygame.draw.line(tile_surface, shadow_color, 
                           (shadow_x, left_panel_edge.top), 
                           (shadow_x, left_panel_edge.bottom), 1)
    
    # Right panel shadow  
    for shadow_line in range(3):
        shadow_x = right_panel_edge.left - shadow_line - 1
        if shadow_x >= 0:
            pygame.draw.line(tile_surface, shadow_color, 
                           (shadow_x, right_panel_edge.top), 
                           (shadow_x, right_panel_edge.bottom), 1)

    return tile_surface


def get_wood_walkway_tile(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a wood walkway tile procedurally."""
    tile_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    wood_color = (139, 69, 19)          # Saddle brown
    dark_wood_color = (101, 67, 33)     # Dark brown
    light_wood_color = (160, 82, 45)    # Light brown

    # Fill base
    tile_surface.fill(wood_color)

    # Draw wooden planks running horizontally
    plank_height = s_h // 5
    for i in range(5):
        plank_y = i * plank_height
        plank_rect = pygame.Rect(0, plank_y, s_w, plank_height - 1)

        # Alternate plank colors for variation
        color = light_wood_color if i % 2 == 0 else wood_color
        pygame.draw.rect(tile_surface, color, plank_rect)

        # Add wood grain lines
        grain_y = plank_y + plank_height // 2
        pygame.draw.line(tile_surface, dark_wood_color, (2, grain_y), (s_w - 2, grain_y), 1)

        # Add plank separation
        if i < 4:  # Don't draw line after last plank
            pygame.draw.line(tile_surface, dark_wood_color,
                           (0, plank_y + plank_height - 1),
                           (s_w, plank_y + plank_height - 1), 1)

    # Add some nail details
    import random
    random.seed(111)  # Consistent randomness

    for i in range(5):
        plank_y = i * plank_height + plank_height // 2
        # Left nail
        nail_x = s_w // 8
        pygame.draw.circle(tile_surface, dark_wood_color, (nail_x, plank_y), 1)
        # Right nail
        nail_x = 7 * s_w // 8
        pygame.draw.circle(tile_surface, dark_wood_color, (nail_x, plank_y), 1)

    return tile_surface


def get_magic_door_tile(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a magical door exit tile."""
    tile_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    door_color = (101, 67, 33)        # Dark brown door
    frame_color = (139, 69, 19)       # Lighter brown frame
    magic_color = (138, 43, 226)      # Blue violet magic
    magic_glow = (75, 0, 130)         # Indigo glow
    handle_color = (255, 215, 0)      # Gold handle
    rune_color = (255, 255, 255)      # White runes

    # Draw door frame
    frame_thickness = max(2, s_w // 16)
    pygame.draw.rect(tile_surface, frame_color, (0, 0, s_w, s_h))

    # Draw door interior
    door_rect = pygame.Rect(frame_thickness, frame_thickness,
                           s_w - 2 * frame_thickness, s_h - 2 * frame_thickness)
    pygame.draw.rect(tile_surface, door_color, door_rect)

    # Add wood grain texture
    grain_lines = 5
    for i in range(grain_lines):
        y_pos = door_rect.y + (i + 1) * door_rect.height // (grain_lines + 1)
        start_x = door_rect.x + 2
        end_x = door_rect.right - 2
        pygame.draw.line(tile_surface, (80, 50, 25), (start_x, y_pos), (end_x, y_pos), 1)

    # Draw magical glow around the door
    glow_rect = pygame.Rect(door_rect.x - 1, door_rect.y - 1,
                           door_rect.width + 2, door_rect.height + 2)
    pygame.draw.rect(tile_surface, magic_glow, glow_rect, 2)

    # Add magical sparkles/runes
    if s_w >= 24:
        # Draw mystical runes on the door
        rune_positions = [
            (door_rect.centerx - 4, door_rect.y + door_rect.height // 4),
            (door_rect.centerx + 4, door_rect.y + door_rect.height // 4),
            (door_rect.centerx, door_rect.y + 3 * door_rect.height // 4)
        ]

        for x, y in rune_positions:
            # Draw simple rune symbols
            pygame.draw.circle(tile_surface, rune_color, (x, y), 2)
            pygame.draw.line(tile_surface, rune_color, (x-2, y), (x+2, y), 1)
            pygame.draw.line(tile_surface, rune_color, (x, y-2), (x, y+2), 1)

    # Draw door handle
    handle_x = door_rect.right - door_rect.width // 4
    handle_y = door_rect.centery
    pygame.draw.circle(tile_surface, handle_color, (handle_x, handle_y), max(2, s_w // 16))

    # Add magical energy effect
    if s_w >= 32:
        # Draw swirling magical energy
        center_x, center_y = door_rect.centerx, door_rect.centery
        for i in range(3):
            angle_offset = i * 120  # 120 degrees apart
            import math
            for j in range(8):
                angle = (angle_offset + j * 45) * math.pi / 180
                radius = 8 + j
                x = center_x + int(radius * math.cos(angle))
                y = center_y + int(radius * math.sin(angle))
                if door_rect.collidepoint(x, y):
                    alpha = 255 - j * 30
                    magic_surface = pygame.Surface((2, 2), pygame.SRCALPHA)
                    magic_surface.fill((*magic_color, max(50, alpha)))
                    tile_surface.blit(magic_surface, (x-1, y-1))

    return tile_surface


def get_missing_asset_tile(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a 'missing asset' placeholder tile."""
    tile_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors
    bg_color = (255, 0, 255)      # Magenta background
    text_color = (255, 255, 255)  # White text
    border_color = (0, 0, 0)      # Black border

    # Fill background
    tile_surface.fill(bg_color)

    # Draw border
    pygame.draw.rect(tile_surface, border_color, (0, 0, s_w, s_h), 2)

    # Draw diagonal lines to make it more obvious
    pygame.draw.line(tile_surface, border_color, (0, 0), (s_w, s_h), 2)
    pygame.draw.line(tile_surface, border_color, (s_w, 0), (0, s_h), 2)

    # Add text if tile is large enough
    if s_w >= 32 and s_h >= 32:
        # Create font (use default pygame font)
        font = pygame.font.Font(None, min(s_w // 4, 12))

        # Render "MISSING" text
        text_surface = font.render("MISSING", True, text_color)
        text_rect = text_surface.get_rect(center=(s_w // 2, s_h // 2 - 4))
        tile_surface.blit(text_surface, text_rect)

        # Render "ASSET" text
        text_surface2 = font.render("ASSET", True, text_color)
        text_rect2 = text_surface2.get_rect(center=(s_w // 2, s_h // 2 + 4))
        tile_surface.blit(text_surface2, text_rect2)

    return tile_surface


def get_town_west_haven_portal_tile(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a custom portal tile for town_west_haven transitions."""
    tile_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Colors for a town-themed portal
    portal_color = (100, 149, 237)      # Cornflower blue (peaceful town color)
    inner_color = (173, 216, 230)       # Light blue
    glow_color = (255, 255, 255)        # White glow
    frame_color = (139, 69, 19)         # Brown wooden frame

    # Fill with grass background
    grass_color = (34, 139, 34)
    tile_surface.fill(grass_color)

    # Wooden frame (like a town gate)
    frame_thickness = max(2, s_w // 16)
    frame_rect = pygame.Rect(s_w*0.1, s_h*0.1, s_w*0.8, s_h*0.8)
    pygame.draw.rect(tile_surface, frame_color, frame_rect, frame_thickness)

    # Portal opening (oval shape)
    portal_rect = pygame.Rect(s_w*0.2, s_h*0.2, s_w*0.6, s_h*0.6)
    pygame.draw.ellipse(tile_surface, portal_color, portal_rect)

    # Inner glow effect
    inner_rect = pygame.Rect(s_w*0.3, s_h*0.3, s_w*0.4, s_h*0.4)
    pygame.draw.ellipse(tile_surface, inner_color, inner_rect)

    # Central bright spot
    center_rect = pygame.Rect(s_w*0.4, s_h*0.4, s_w*0.2, s_h*0.2)
    pygame.draw.ellipse(tile_surface, glow_color, center_rect)

    # Add some sparkle effects around the portal
    import math
    sparkle_color = (255, 255, 224)  # Light yellow sparkles
    center_x, center_y = s_w // 2, s_h // 2

    for i in range(8):
        angle = (i * math.pi * 2) / 8
        sparkle_x = center_x + int(math.cos(angle) * s_w * 0.35)
        sparkle_y = center_y + int(math.sin(angle) * s_h * 0.35)
        pygame.draw.circle(tile_surface, sparkle_color, (sparkle_x, sparkle_y), 1)

    # Add wooden posts on sides
    post_width = max(2, s_w // 16)
    left_post = pygame.Rect(s_w*0.05, s_h*0.05, post_width, s_h*0.9)
    right_post = pygame.Rect(s_w*0.95 - post_width, s_h*0.05, post_width, s_h*0.9)
    pygame.draw.rect(tile_surface, frame_color, left_post)
    pygame.draw.rect(tile_surface, frame_color, right_post)

    return tile_surface
