"""
Procedural NPC Asset Generation

This module contains functions to procedurally generate NPC sprites using Pygame.
This is the ONLY place in the codebase where NPC visual assets are created.
"""

import pygame
from typing import <PERSON>ple


def get_merchant_sprite(size: Tuple[int, int] = (64, 64)) -> pygame.Surface:
    """Generate a detailed Merchant NPC sprite procedurally."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size
    
    # Enhanced color palette
    skin_color = (222, 184, 135)       # Warm skin tone
    skin_shadow = (205, 160, 115)      # Darker skin for shadows
    shirt_color = (34, 139, 34)        # Rich green shirt
    shirt_highlight = (50, 180, 50)    # Lighter green highlight
    vest_color = (139, 69, 19)         # Brown leather vest
    vest_highlight = (160, 85, 35)     # Lighter brown highlight
    pants_color = (101, 67, 33)        # Dark brown pants
    pants_highlight = (120, 80, 45)    # Lighter brown highlight
    hair_color = (160, 82, 45)         # Rich brown hair
    hair_highlight = (180, 100, 60)    # Hair highlights
    bag_color = (80, 50, 30)           # Dark leather bag
    bag_highlight = (110, 70, 40)      # Bag highlights
    coin_color = (255, 215, 0)         # Gold coins
    belt_color = (101, 67, 33)         # Brown belt
    button_color = (255, 255, 255)     # White buttons
    eye_color = (139, 69, 19)          # Brown eyes
    boot_color = (60, 40, 20)          # Dark brown boots
    
    # Head with proper shading
    head_center = (int(s_w * 0.5), int(s_h * 0.22))
    head_radius = int(s_w * 0.14)
    pygame.draw.circle(sprite_surface, skin_color, head_center, head_radius)
    pygame.draw.circle(sprite_surface, skin_shadow, 
                      (head_center[0] + 2, head_center[1] + 2), head_radius - 2)
    pygame.draw.circle(sprite_surface, skin_color, 
                      (head_center[0] - 1, head_center[1] - 1), head_radius - 3)
    
    # Well-groomed hair with highlights
    hair_rect = pygame.Rect(head_center[0] - head_radius + 2, head_center[1] - head_radius + 1, 
                           (head_radius - 2) * 2, head_radius)
    pygame.draw.ellipse(sprite_surface, hair_color, hair_rect)
    hair_highlight_rect = pygame.Rect(hair_rect.left + 3, hair_rect.top + 2, 
                                     hair_rect.width - 6, hair_rect.height // 2)
    pygame.draw.ellipse(sprite_surface, hair_highlight, hair_highlight_rect)
    
    # Detailed facial features
    # Eyes with pupils and highlights
    eye_left = (head_center[0] - int(s_w * 0.035), head_center[1] - int(s_h * 0.015))
    eye_right = (head_center[0] + int(s_w * 0.035), head_center[1] - int(s_h * 0.015))
    eye_radius = int(s_w * 0.018)
    
    # Eye whites
    pygame.draw.circle(sprite_surface, (255, 255, 255), eye_left, eye_radius)
    pygame.draw.circle(sprite_surface, (255, 255, 255), eye_right, eye_radius)
    # Irises
    pygame.draw.circle(sprite_surface, eye_color, eye_left, int(eye_radius * 0.7))
    pygame.draw.circle(sprite_surface, eye_color, eye_right, int(eye_radius * 0.7))
    # Pupils
    pygame.draw.circle(sprite_surface, (0, 0, 0), eye_left, int(eye_radius * 0.4))
    pygame.draw.circle(sprite_surface, (0, 0, 0), eye_right, int(eye_radius * 0.4))
    # Eye highlights
    pygame.draw.circle(sprite_surface, (255, 255, 255), 
                      (eye_left[0] - 1, eye_left[1] - 1), 2)
    pygame.draw.circle(sprite_surface, (255, 255, 255), 
                      (eye_right[0] - 1, eye_right[1] - 1), 2)
    
    # Friendly smile
    smile_center = (head_center[0], head_center[1] + int(s_h * 0.035))
    smile_rect = pygame.Rect(smile_center[0] - int(s_w * 0.025), smile_center[1] - 2, 
                            int(s_w * 0.05), 4)
    pygame.draw.arc(sprite_surface, (160, 80, 80), smile_rect, 0, 3.14159, 2)
    
    # Mustache
    mustache_rect = pygame.Rect(head_center[0] - int(s_w * 0.02), head_center[1] + int(s_h * 0.015), 
                               int(s_w * 0.04), int(s_h * 0.015))
    pygame.draw.ellipse(sprite_surface, hair_color, mustache_rect)
    
    # Neck
    neck_rect = pygame.Rect(head_center[0] - int(s_w * 0.06), head_center[1] + head_radius - 3, 
                           int(s_w * 0.12), int(s_h * 0.08))
    pygame.draw.rect(sprite_surface, skin_color, neck_rect)
    
    # Body with merchant's vest over shirt
    body_rect = pygame.Rect(int(s_w * 0.32), int(s_h * 0.35), int(s_w * 0.36), int(s_h * 0.4))
    # Shirt (base layer)
    pygame.draw.rect(sprite_surface, shirt_color, body_rect)
    pygame.draw.rect(sprite_surface, shirt_highlight, 
                    (body_rect.left + 2, body_rect.top + 2, body_rect.width - 4, body_rect.height // 3))
    
    # Leather vest over shirt
    vest_rect = pygame.Rect(int(s_w * 0.34), int(s_h * 0.37), int(s_w * 0.32), int(s_h * 0.35))
    pygame.draw.rect(sprite_surface, vest_color, vest_rect)
    pygame.draw.rect(sprite_surface, vest_highlight, 
                    (vest_rect.left + 1, vest_rect.top + 1, vest_rect.width - 2, vest_rect.height // 4))
    
    # Vest buttons
    button_positions = [
        (vest_rect.centerx, vest_rect.top + int(s_h * 0.08)),
        (vest_rect.centerx, vest_rect.top + int(s_h * 0.15)),
        (vest_rect.centerx, vest_rect.top + int(s_h * 0.22))
    ]
    for btn_x, btn_y in button_positions:
        pygame.draw.circle(sprite_surface, button_color, (btn_x, btn_y), 2)
    
    # Arms with shirt sleeves
    arm_left = pygame.Rect(int(s_w * 0.22), int(s_h * 0.38), int(s_w * 0.1), int(s_h * 0.28))
    arm_right = pygame.Rect(int(s_w * 0.68), int(s_h * 0.38), int(s_w * 0.1), int(s_h * 0.28))
    # Shirt sleeves
    pygame.draw.rect(sprite_surface, shirt_color, arm_left)
    pygame.draw.rect(sprite_surface, shirt_color, arm_right)
    pygame.draw.rect(sprite_surface, shirt_highlight, 
                    (arm_left.left + 1, arm_left.top + 1, arm_left.width - 2, arm_left.height // 3))
    pygame.draw.rect(sprite_surface, shirt_highlight, 
                    (arm_right.left + 1, arm_right.top + 1, arm_right.width - 2, arm_right.height // 3))
    
    # Hands
    hand_left = (int(s_w * 0.27), int(s_h * 0.66))
    hand_right = (int(s_w * 0.73), int(s_h * 0.66))
    hand_radius = int(s_w * 0.035)
    pygame.draw.circle(sprite_surface, skin_color, hand_left, hand_radius)
    pygame.draw.circle(sprite_surface, skin_color, hand_right, hand_radius)
    
    # Belt with buckle
    belt_rect = pygame.Rect(int(s_w * 0.32), int(s_h * 0.62), int(s_w * 0.36), int(s_h * 0.05))
    pygame.draw.rect(sprite_surface, belt_color, belt_rect)
    buckle_rect = pygame.Rect(belt_rect.centerx - 4, belt_rect.centery - 3, 8, 6)
    pygame.draw.rect(sprite_surface, (169, 169, 169), buckle_rect)  # Silver buckle
    
    # Legs with detailed pants
    leg_left = pygame.Rect(int(s_w * 0.38), int(s_h * 0.67), int(s_w * 0.1), int(s_h * 0.28))
    leg_right = pygame.Rect(int(s_w * 0.52), int(s_h * 0.67), int(s_w * 0.1), int(s_h * 0.28))
    pygame.draw.rect(sprite_surface, pants_color, leg_left)
    pygame.draw.rect(sprite_surface, pants_color, leg_right)
    pygame.draw.rect(sprite_surface, pants_highlight, 
                    (leg_left.left + 1, leg_left.top + 1, leg_left.width - 2, leg_left.height // 4))
    pygame.draw.rect(sprite_surface, pants_highlight, 
                    (leg_right.left + 1, leg_right.top + 1, leg_right.width - 2, leg_right.height // 4))
    
    # Boots
    boot_left = pygame.Rect(int(s_w * 0.36), int(s_h * 0.9), int(s_w * 0.12), int(s_h * 0.08))
    boot_right = pygame.Rect(int(s_w * 0.52), int(s_h * 0.9), int(s_w * 0.12), int(s_h * 0.08))
    pygame.draw.ellipse(sprite_surface, boot_color, boot_left)
    pygame.draw.ellipse(sprite_surface, boot_color, boot_right)
    
    # Large merchant's satchel with detailed texture
    bag_main = pygame.Rect(int(s_w * 0.75), int(s_h * 0.45), int(s_w * 0.18), int(s_h * 0.25))
    pygame.draw.rect(sprite_surface, bag_color, bag_main)
    pygame.draw.rect(sprite_surface, bag_highlight, 
                    (bag_main.left + 1, bag_main.top + 1, bag_main.width - 2, bag_main.height // 4))
    
    # Bag strap
    strap_points = [
        (bag_main.left + 3, bag_main.top),
        (int(s_w * 0.6), int(s_h * 0.4)),
        (int(s_w * 0.55), int(s_h * 0.42)),
        (bag_main.left + 1, bag_main.top + 5)
    ]
    pygame.draw.polygon(sprite_surface, bag_color, strap_points)
    
    # Bag buckles and details
    pygame.draw.rect(sprite_surface, (169, 169, 169), 
                    (bag_main.left + 2, bag_main.top + 8, 4, 3))  # Top buckle
    pygame.draw.rect(sprite_surface, (169, 169, 169), 
                    (bag_main.left + 2, bag_main.bottom - 8, 4, 3))  # Bottom buckle
    
    # Coins spilling from bag
    coin_positions = [
        (bag_main.right - 3, bag_main.bottom - 5),
        (bag_main.right + 2, bag_main.bottom - 2),
        (bag_main.right - 1, bag_main.bottom + 1)
    ]
    for coin_x, coin_y in coin_positions:
        pygame.draw.circle(sprite_surface, coin_color, (coin_x, coin_y), 3)
        pygame.draw.circle(sprite_surface, (255, 235, 50), (coin_x - 1, coin_y - 1), 1)  # Coin highlight
    
    # Small pouch on belt
    pouch_rect = pygame.Rect(int(s_w * 0.25), int(s_h * 0.6), int(s_w * 0.08), int(s_h * 0.12))
    pygame.draw.ellipse(sprite_surface, bag_color, pouch_rect)
    pygame.draw.line(sprite_surface, belt_color, 
                    (pouch_rect.centerx, belt_rect.centery), 
                    (pouch_rect.centerx, pouch_rect.top), 2)  # Pouch string
    
    return sprite_surface


def get_armourer_sprite(size: Tuple[int, int] = (64, 64)) -> pygame.Surface:
    """Generate a detailed Armourer NPC sprite procedurally."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size
    
    # Enhanced color palette
    skin_color = (222, 184, 135)       # Warm skin tone
    skin_shadow = (205, 160, 115)      # Darker skin for shadows
    armor_color = (169, 169, 169)      # Polished steel armor
    armor_dark = (120, 120, 120)       # Darker steel for shadows
    armor_highlight = (200, 200, 200)  # Bright steel highlights
    pants_color = (101, 67, 33)        # Dark brown pants
    pants_highlight = (120, 80, 45)    # Lighter brown highlight
    hair_color = (105, 105, 105)       # Distinguished gray hair
    hair_highlight = (140, 140, 140)   # Gray hair highlights
    beard_color = (90, 90, 90)         # Darker gray beard
    hammer_handle = (139, 69, 19)      # Rich brown handle
    hammer_metal = (192, 192, 192)     # Bright steel hammerhead
    belt_color = (80, 50, 30)          # Dark leather belt
    boot_color = (60, 40, 20)          # Dark brown boots
    eye_color = (70, 130, 180)         # Steel blue eyes
    apron_color = (101, 67, 33)        # Leather work apron
    
    # Head with weathered features
    head_center = (int(s_w * 0.5), int(s_h * 0.22))
    head_radius = int(s_w * 0.14)
    pygame.draw.circle(sprite_surface, skin_color, head_center, head_radius)
    pygame.draw.circle(sprite_surface, skin_shadow, 
                      (head_center[0] + 2, head_center[1] + 2), head_radius - 2)
    pygame.draw.circle(sprite_surface, skin_color, 
                      (head_center[0] - 1, head_center[1] - 1), head_radius - 3)
    
    # Graying hair with texture
    hair_rect = pygame.Rect(head_center[0] - head_radius + 2, head_center[1] - head_radius + 1, 
                           (head_radius - 2) * 2, head_radius)
    pygame.draw.ellipse(sprite_surface, hair_color, hair_rect)
    hair_highlight_rect = pygame.Rect(hair_rect.left + 3, hair_rect.top + 2, 
                                     hair_rect.width - 6, hair_rect.height // 2)
    pygame.draw.ellipse(sprite_surface, hair_highlight, hair_highlight_rect)
    
    # Full beard
    beard_rect = pygame.Rect(head_center[0] - int(s_w * 0.08), head_center[1] + int(s_h * 0.02), 
                            int(s_w * 0.16), int(s_h * 0.12))
    pygame.draw.ellipse(sprite_surface, beard_color, beard_rect)
    beard_highlight_rect = pygame.Rect(beard_rect.left + 2, beard_rect.top + 1, 
                                      beard_rect.width - 4, beard_rect.height // 2)
    pygame.draw.ellipse(sprite_surface, hair_color, beard_highlight_rect)
    
    # Strong, experienced eyes
    eye_left = (head_center[0] - int(s_w * 0.035), head_center[1] - int(s_h * 0.02))
    eye_right = (head_center[0] + int(s_w * 0.035), head_center[1] - int(s_h * 0.02))
    eye_radius = int(s_w * 0.018)
    
    # Eye whites
    pygame.draw.circle(sprite_surface, (255, 255, 255), eye_left, eye_radius)
    pygame.draw.circle(sprite_surface, (255, 255, 255), eye_right, eye_radius)
    # Steel blue irises
    pygame.draw.circle(sprite_surface, eye_color, eye_left, int(eye_radius * 0.7))
    pygame.draw.circle(sprite_surface, eye_color, eye_right, int(eye_radius * 0.7))
    # Pupils
    pygame.draw.circle(sprite_surface, (0, 0, 0), eye_left, int(eye_radius * 0.4))
    pygame.draw.circle(sprite_surface, (0, 0, 0), eye_right, int(eye_radius * 0.4))
    # Eye highlights
    pygame.draw.circle(sprite_surface, (255, 255, 255), 
                      (eye_left[0] - 1, eye_left[1] - 1), 2)
    pygame.draw.circle(sprite_surface, (255, 255, 255), 
                      (eye_right[0] - 1, eye_right[1] - 1), 2)
    
    # Bushy eyebrows
    eyebrow_left = pygame.Rect(eye_left[0] - 5, eye_left[1] - 8, 10, 4)
    eyebrow_right = pygame.Rect(eye_right[0] - 5, eye_right[1] - 8, 10, 4)
    pygame.draw.ellipse(sprite_surface, hair_color, eyebrow_left)
    pygame.draw.ellipse(sprite_surface, hair_color, eyebrow_right)
    
    # Neck
    neck_rect = pygame.Rect(head_center[0] - int(s_w * 0.06), head_center[1] + head_radius - 3, 
                           int(s_w * 0.12), int(s_h * 0.08))
    pygame.draw.rect(sprite_surface, skin_color, neck_rect)
    
    # Muscular torso with armor vest
    body_rect = pygame.Rect(int(s_w * 0.32), int(s_h * 0.35), int(s_w * 0.36), int(s_h * 0.4))
    # Base armor vest
    pygame.draw.rect(sprite_surface, armor_color, body_rect)
    pygame.draw.rect(sprite_surface, armor_highlight, 
                    (body_rect.left + 2, body_rect.top + 2, body_rect.width - 4, body_rect.height // 3))
    pygame.draw.rect(sprite_surface, armor_dark, 
                    (body_rect.left + 1, body_rect.bottom - 5, body_rect.width - 2, 4))
    
    # Armor plate details
    plate_lines = [
        (body_rect.left + 5, body_rect.top + int(s_h * 0.1), body_rect.right - 5, body_rect.top + int(s_h * 0.1)),
        (body_rect.left + 5, body_rect.top + int(s_h * 0.2), body_rect.right - 5, body_rect.top + int(s_h * 0.2)),
        (body_rect.centerx, body_rect.top + 5, body_rect.centerx, body_rect.bottom - 5)
    ]
    for line in plate_lines:
        pygame.draw.line(sprite_surface, armor_dark, (line[0], line[1]), (line[2], line[3]), 1)
    
    # Leather work apron over armor
    apron_rect = pygame.Rect(int(s_w * 0.35), int(s_h * 0.45), int(s_w * 0.3), int(s_h * 0.3))
    pygame.draw.rect(sprite_surface, apron_color, apron_rect)
    
    # Apron straps
    strap_left = (apron_rect.left + 3, apron_rect.top)
    strap_right = (apron_rect.right - 3, apron_rect.top)
    neck_connection = (head_center[0], head_center[1] + head_radius + 5)
    pygame.draw.line(sprite_surface, apron_color, strap_left, neck_connection, 3)
    pygame.draw.line(sprite_surface, apron_color, strap_right, neck_connection, 3)
    
    # Muscular arms
    arm_left = pygame.Rect(int(s_w * 0.22), int(s_h * 0.38), int(s_w * 0.1), int(s_h * 0.28))
    arm_right = pygame.Rect(int(s_w * 0.68), int(s_h * 0.38), int(s_w * 0.1), int(s_h * 0.28))
    # Skin base
    pygame.draw.rect(sprite_surface, skin_color, arm_left)
    pygame.draw.rect(sprite_surface, skin_color, arm_right)
    # Muscle definition
    pygame.draw.rect(sprite_surface, skin_shadow, 
                    (arm_left.left + 1, arm_left.top + 5, arm_left.width - 2, arm_left.height // 3))
    pygame.draw.rect(sprite_surface, skin_shadow, 
                    (arm_right.left + 1, arm_right.top + 5, arm_right.width - 2, arm_right.height // 3))
    
    # Large hands (blacksmith hands)
    hand_left = (int(s_w * 0.27), int(s_h * 0.66))
    hand_right = (int(s_w * 0.73), int(s_h * 0.66))
    hand_radius = int(s_w * 0.04)  # Larger hands
    pygame.draw.circle(sprite_surface, skin_color, hand_left, hand_radius)
    pygame.draw.circle(sprite_surface, skin_color, hand_right, hand_radius)
    pygame.draw.circle(sprite_surface, skin_shadow, 
                      (hand_left[0] + 1, hand_left[1] + 1), hand_radius - 2)
    pygame.draw.circle(sprite_surface, skin_shadow, 
                      (hand_right[0] + 1, hand_right[1] + 1), hand_radius - 2)
    
    # Thick leather belt with tools
    belt_rect = pygame.Rect(int(s_w * 0.32), int(s_h * 0.62), int(s_w * 0.36), int(s_h * 0.06))
    pygame.draw.rect(sprite_surface, belt_color, belt_rect)
    buckle_rect = pygame.Rect(belt_rect.centerx - 4, belt_rect.centery - 3, 8, 6)
    pygame.draw.rect(sprite_surface, armor_color, buckle_rect)  # Metal buckle
    
    # Tool loops on belt
    tool_positions = [
        (belt_rect.left + 8, belt_rect.centery),
        (belt_rect.left + 16, belt_rect.centery),
        (belt_rect.right - 8, belt_rect.centery)
    ]
    for tool_x, tool_y in tool_positions:
        pygame.draw.circle(sprite_surface, belt_color, (tool_x, tool_y), 3)
        pygame.draw.circle(sprite_surface, armor_color, (tool_x, tool_y), 1)  # Metal tool handle
    
    # Legs with detailed pants
    leg_left = pygame.Rect(int(s_w * 0.38), int(s_h * 0.67), int(s_w * 0.1), int(s_h * 0.28))
    leg_right = pygame.Rect(int(s_w * 0.52), int(s_h * 0.67), int(s_w * 0.1), int(s_h * 0.28))
    pygame.draw.rect(sprite_surface, pants_color, leg_left)
    pygame.draw.rect(sprite_surface, pants_color, leg_right)
    pygame.draw.rect(sprite_surface, pants_highlight, 
                    (leg_left.left + 1, leg_left.top + 1, leg_left.width - 2, leg_left.height // 4))
    pygame.draw.rect(sprite_surface, pants_highlight, 
                    (leg_right.left + 1, leg_right.top + 1, leg_right.width - 2, leg_right.height // 4))
    
    # Heavy work boots
    boot_left = pygame.Rect(int(s_w * 0.36), int(s_h * 0.9), int(s_w * 0.12), int(s_h * 0.08))
    boot_right = pygame.Rect(int(s_w * 0.52), int(s_h * 0.9), int(s_w * 0.12), int(s_h * 0.08))
    pygame.draw.ellipse(sprite_surface, boot_color, boot_left)
    pygame.draw.ellipse(sprite_surface, boot_color, boot_right)
    # Boot reinforcement
    pygame.draw.line(sprite_surface, armor_color, 
                    (boot_left.left + 2, boot_left.centery), 
                    (boot_left.right - 2, boot_left.centery), 2)
    pygame.draw.line(sprite_surface, armor_color, 
                    (boot_right.left + 2, boot_right.centery), 
                    (boot_right.right - 2, boot_right.centery), 2)
    
    # Large blacksmith hammer (held in right hand)
    hammer_handle_rect = pygame.Rect(int(s_w * 0.75), int(s_h * 0.4), int(s_w * 0.04), int(s_h * 0.3))
    pygame.draw.rect(sprite_surface, hammer_handle, hammer_handle_rect)
    
    # Hammer head (large and detailed)
    hammer_head_rect = pygame.Rect(int(s_w * 0.72), int(s_h * 0.38), int(s_w * 0.1), int(s_h * 0.1))
    pygame.draw.rect(sprite_surface, hammer_metal, hammer_head_rect)
    pygame.draw.rect(sprite_surface, armor_highlight, 
                    (hammer_head_rect.left + 1, hammer_head_rect.top + 1, 
                     hammer_head_rect.width - 2, hammer_head_rect.height // 3))
    
    # Hammer striking face detail
    striking_face = pygame.Rect(hammer_head_rect.left + 2, hammer_head_rect.centery - 2, 
                               hammer_head_rect.width - 4, 4)
    pygame.draw.rect(sprite_surface, armor_dark, striking_face)
    
    # Small anvil icon on apron (guild symbol)
    anvil_rect = pygame.Rect(apron_rect.right - 12, apron_rect.top + 8, 8, 6)
    pygame.draw.rect(sprite_surface, armor_color, anvil_rect)
    pygame.draw.rect(sprite_surface, armor_dark, (anvil_rect.left + 1, anvil_rect.top + 1, 6, 2))
    
    return sprite_surface


def get_weaponsmith_sprite(size: Tuple[int, int] = (64, 64)) -> pygame.Surface:
    """Generate a detailed Weaponsmith NPC sprite procedurally."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size
    
    # Enhanced color palette
    skin_color = (222, 184, 135)       # Warm skin tone
    skin_shadow = (205, 160, 115)      # Darker skin for shadows
    apron_color = (101, 67, 33)        # Rich brown leather apron
    apron_highlight = (120, 80, 45)    # Lighter brown highlight
    shirt_color = (60, 60, 60)         # Dark gray work shirt
    shirt_highlight = (80, 80, 80)     # Lighter gray highlight
    pants_color = (40, 40, 40)         # Very dark gray pants
    pants_highlight = (60, 60, 60)     # Lighter gray highlight
    hair_color = (30, 20, 10)          # Very dark brown/black hair
    hair_highlight = (50, 35, 20)      # Brown highlights
    beard_color = (25, 15, 5)          # Dark beard
    sword_blade = (220, 220, 220)      # Bright polished steel
    sword_edge = (255, 255, 255)       # Razor sharp edge
    sword_hilt = (139, 69, 19)         # Brown leather grip
    sword_guard = (192, 192, 192)      # Steel crossguard
    belt_color = (80, 50, 30)          # Dark leather belt
    boot_color = (30, 20, 10)          # Very dark boots
    eye_color = (60, 60, 60)           # Dark gray eyes
    forge_glow = (255, 100, 0)         # Orange forge glow
    
    # Head with strong jawline
    head_center = (int(s_w * 0.5), int(s_h * 0.22))
    head_radius = int(s_w * 0.14)
    pygame.draw.circle(sprite_surface, skin_color, head_center, head_radius)
    pygame.draw.circle(sprite_surface, skin_shadow, 
                      (head_center[0] + 2, head_center[1] + 2), head_radius - 2)
    pygame.draw.circle(sprite_surface, skin_color, 
                      (head_center[0] - 1, head_center[1] - 1), head_radius - 3)
    
    # Dark hair (working man's practical cut)
    hair_rect = pygame.Rect(head_center[0] - head_radius + 2, head_center[1] - head_radius + 1, 
                           (head_radius - 2) * 2, int(head_radius * 0.8))
    pygame.draw.ellipse(sprite_surface, hair_color, hair_rect)
    hair_highlight_rect = pygame.Rect(hair_rect.left + 3, hair_rect.top + 2, 
                                     hair_rect.width - 6, hair_rect.height // 3)
    pygame.draw.ellipse(sprite_surface, hair_highlight, hair_highlight_rect)
    
    # Short practical beard
    beard_rect = pygame.Rect(head_center[0] - int(s_w * 0.06), head_center[1] + int(s_h * 0.03), 
                            int(s_w * 0.12), int(s_h * 0.08))
    pygame.draw.ellipse(sprite_surface, beard_color, beard_rect)
    
    # Intense, focused eyes
    eye_left = (head_center[0] - int(s_w * 0.035), head_center[1] - int(s_h * 0.02))
    eye_right = (head_center[0] + int(s_w * 0.035), head_center[1] - int(s_h * 0.02))
    eye_radius = int(s_w * 0.018)
    
    # Eye whites
    pygame.draw.circle(sprite_surface, (255, 255, 255), eye_left, eye_radius)
    pygame.draw.circle(sprite_surface, (255, 255, 255), eye_right, eye_radius)
    # Dark irises
    pygame.draw.circle(sprite_surface, eye_color, eye_left, int(eye_radius * 0.7))
    pygame.draw.circle(sprite_surface, eye_color, eye_right, int(eye_radius * 0.7))
    # Pupils
    pygame.draw.circle(sprite_surface, (0, 0, 0), eye_left, int(eye_radius * 0.4))
    pygame.draw.circle(sprite_surface, (0, 0, 0), eye_right, int(eye_radius * 0.4))
    # Intense highlights
    pygame.draw.circle(sprite_surface, (255, 255, 255), 
                      (eye_left[0] - 1, eye_left[1] - 1), 2)
    pygame.draw.circle(sprite_surface, (255, 255, 255), 
                      (eye_right[0] - 1, eye_right[1] - 1), 2)
    
    # Stern expression
    mouth_rect = pygame.Rect(head_center[0] - int(s_w * 0.02), head_center[1] + int(s_h * 0.04), 
                            int(s_w * 0.04), 2)
    pygame.draw.rect(sprite_surface, (120, 80, 80), mouth_rect)
    
    # Neck
    neck_rect = pygame.Rect(head_center[0] - int(s_w * 0.06), head_center[1] + head_radius - 3, 
                           int(s_w * 0.12), int(s_h * 0.08))
    pygame.draw.rect(sprite_surface, skin_color, neck_rect)
    
    # Work shirt base
    body_rect = pygame.Rect(int(s_w * 0.32), int(s_h * 0.35), int(s_w * 0.36), int(s_h * 0.4))
    pygame.draw.rect(sprite_surface, shirt_color, body_rect)
    pygame.draw.rect(sprite_surface, shirt_highlight, 
                    (body_rect.left + 2, body_rect.top + 2, body_rect.width - 4, body_rect.height // 4))
    
    # Leather work apron (prominent feature)
    apron_rect = pygame.Rect(int(s_w * 0.34), int(s_h * 0.4), int(s_w * 0.32), int(s_h * 0.35))
    pygame.draw.rect(sprite_surface, apron_color, apron_rect)
    pygame.draw.rect(sprite_surface, apron_highlight, 
                    (apron_rect.left + 1, apron_rect.top + 1, apron_rect.width - 2, apron_rect.height // 4))
    
    # Apron straps and neck loop
    strap_left = (apron_rect.left + 4, apron_rect.top)
    strap_right = (apron_rect.right - 4, apron_rect.top)
    neck_connection = (head_center[0], head_center[1] + head_radius + 5)
    pygame.draw.line(sprite_surface, apron_color, strap_left, neck_connection, 3)
    pygame.draw.line(sprite_surface, apron_color, strap_right, neck_connection, 3)
    
    # Belt with tools
    belt_rect = pygame.Rect(int(s_w * 0.32), int(s_h * 0.62), int(s_w * 0.36), int(s_h * 0.06))
    pygame.draw.rect(sprite_surface, belt_color, belt_rect)
    buckle_rect = pygame.Rect(belt_rect.centerx - 4, belt_rect.centery - 3, 8, 6)
    pygame.draw.rect(sprite_surface, sword_guard, buckle_rect)  # Metal buckle
    
    # Multiple small tools on belt
    tool_positions = [
        (belt_rect.left + 6, belt_rect.centery - 3, 2, 8),   # Small file
        (belt_rect.left + 12, belt_rect.centery - 4, 3, 10), # Small hammer
        (belt_rect.right - 10, belt_rect.centery - 3, 2, 8), # Chisel
        (belt_rect.right - 16, belt_rect.centery - 2, 4, 6)  # Small tongs
    ]
    for tool_rect in tool_positions:
        pygame.draw.rect(sprite_surface, sword_guard, tool_rect)
    
    # Muscular arms (from forge work)
    arm_left = pygame.Rect(int(s_w * 0.22), int(s_h * 0.38), int(s_w * 0.1), int(s_h * 0.28))
    arm_right = pygame.Rect(int(s_w * 0.68), int(s_h * 0.38), int(s_w * 0.1), int(s_h * 0.28))
    # Shirt sleeves (rolled up)
    pygame.draw.rect(sprite_surface, shirt_color, 
                    (arm_left.left, arm_left.top, arm_left.width, arm_left.height // 2))
    pygame.draw.rect(sprite_surface, shirt_color, 
                    (arm_right.left, arm_right.top, arm_right.width, arm_right.height // 2))
    # Exposed forearms
    pygame.draw.rect(sprite_surface, skin_color, 
                    (arm_left.left, arm_left.centery, arm_left.width, arm_left.height // 2))
    pygame.draw.rect(sprite_surface, skin_color, 
                    (arm_right.left, arm_right.centery, arm_right.width, arm_right.height // 2))
    # Muscle definition
    pygame.draw.rect(sprite_surface, skin_shadow, 
                    (arm_left.left + 1, arm_left.centery + 2, arm_left.width - 2, arm_left.height // 4))
    pygame.draw.rect(sprite_surface, skin_shadow, 
                    (arm_right.left + 1, arm_right.centery + 2, arm_right.width - 2, arm_right.height // 4))
    
    # Strong hands
    hand_left = (int(s_w * 0.27), int(s_h * 0.66))
    hand_right = (int(s_w * 0.73), int(s_h * 0.66))
    hand_radius = int(s_w * 0.04)
    pygame.draw.circle(sprite_surface, skin_color, hand_left, hand_radius)
    pygame.draw.circle(sprite_surface, skin_color, hand_right, hand_radius)
    pygame.draw.circle(sprite_surface, skin_shadow, 
                      (hand_left[0] + 1, hand_left[1] + 1), hand_radius - 2)
    pygame.draw.circle(sprite_surface, skin_shadow, 
                      (hand_right[0] + 1, hand_right[1] + 1), hand_radius - 2)
    
    # Legs with work pants
    leg_left = pygame.Rect(int(s_w * 0.38), int(s_h * 0.67), int(s_w * 0.1), int(s_h * 0.28))
    leg_right = pygame.Rect(int(s_w * 0.52), int(s_h * 0.67), int(s_w * 0.1), int(s_h * 0.28))
    pygame.draw.rect(sprite_surface, pants_color, leg_left)
    pygame.draw.rect(sprite_surface, pants_color, leg_right)
    pygame.draw.rect(sprite_surface, pants_highlight, 
                    (leg_left.left + 1, leg_left.top + 1, leg_left.width - 2, leg_left.height // 4))
    pygame.draw.rect(sprite_surface, pants_highlight, 
                    (leg_right.left + 1, leg_right.top + 1, leg_right.width - 2, leg_right.height // 4))
    
    # Heavy work boots
    boot_left = pygame.Rect(int(s_w * 0.36), int(s_h * 0.9), int(s_w * 0.12), int(s_h * 0.08))
    boot_right = pygame.Rect(int(s_w * 0.52), int(s_h * 0.9), int(s_w * 0.12), int(s_h * 0.08))
    pygame.draw.ellipse(sprite_surface, boot_color, boot_left)
    pygame.draw.ellipse(sprite_surface, boot_color, boot_right)
    
    # Masterwork sword (held or displayed)
    # Sword hilt and guard
    sword_hilt_rect = pygame.Rect(int(s_w * 0.76), int(s_h * 0.55), int(s_w * 0.06), int(s_h * 0.12))
    pygame.draw.rect(sprite_surface, sword_hilt, sword_hilt_rect)
    
    # Crossguard
    guard_rect = pygame.Rect(int(s_w * 0.72), int(s_h * 0.52), int(s_w * 0.14), int(s_h * 0.04))
    pygame.draw.rect(sprite_surface, sword_guard, guard_rect)
    pygame.draw.rect(sprite_surface, sword_blade, 
                    (guard_rect.left + 1, guard_rect.top + 1, guard_rect.width - 2, guard_rect.height - 2))
    
    # Blade (long and detailed)
    blade_rect = pygame.Rect(int(s_w * 0.78), int(s_h * 0.25), int(s_w * 0.04), int(s_h * 0.27))
    pygame.draw.rect(sprite_surface, sword_blade, blade_rect)
    # Fuller (blood groove)
    fuller_rect = pygame.Rect(blade_rect.centerx - 1, blade_rect.top + 3, 2, blade_rect.height - 6)
    pygame.draw.rect(sprite_surface, sword_guard, fuller_rect)
    # Sharp edges
    pygame.draw.line(sprite_surface, sword_edge, 
                    (blade_rect.left, blade_rect.top + 2), 
                    (blade_rect.left, blade_rect.bottom - 2), 1)
    pygame.draw.line(sprite_surface, sword_edge, 
                    (blade_rect.right - 1, blade_rect.top + 2), 
                    (blade_rect.right - 1, blade_rect.bottom - 2), 1)
    
    # Sword point
    point_coords = [
        (blade_rect.centerx, blade_rect.top),
        (blade_rect.left, blade_rect.top + 4),
        (blade_rect.right - 1, blade_rect.top + 4)
    ]
    pygame.draw.polygon(sprite_surface, sword_blade, point_coords)
    pygame.draw.polygon(sprite_surface, sword_edge, point_coords, 1)
    
    # Pommel
    pommel_center = (sword_hilt_rect.centerx, sword_hilt_rect.bottom + 3)
    pygame.draw.circle(sprite_surface, sword_guard, pommel_center, 4)
    pygame.draw.circle(sprite_surface, sword_blade, pommel_center, 2)
    
    # Forge glow reflection on face (subtle)
    glow_rect = pygame.Rect(head_center[0] - 3, head_center[1] - 3, 6, 6)
    glow_surface = pygame.Surface((6, 6), pygame.SRCALPHA)
    pygame.draw.circle(glow_surface, (*forge_glow, 30), (3, 3), 3)  # Transparent glow
    sprite_surface.blit(glow_surface, glow_rect)
    
    # Weapon quality mark on apron (guild symbol)
    mark_rect = pygame.Rect(apron_rect.right - 10, apron_rect.top + 6, 6, 8)
    pygame.draw.rect(sprite_surface, sword_blade, mark_rect)
    pygame.draw.line(sprite_surface, sword_edge, 
                    (mark_rect.centerx, mark_rect.top + 1), 
                    (mark_rect.centerx, mark_rect.bottom - 1), 1)
    
    return sprite_surface
    pygame.draw.rect(sprite_surface, apron_color, body_rect)
    
    # Arms
    arm_left = pygame.Rect(s_w * 0.25, s_h * 0.4, s_w * 0.1, s_h * 0.25)
    arm_right = pygame.Rect(s_w * 0.65, s_h * 0.4, s_w * 0.1, s_h * 0.25)
    pygame.draw.rect(sprite_surface, body_color, arm_left)
    pygame.draw.rect(sprite_surface, body_color, arm_right)
    
    # Legs (pants)
    leg_left = pygame.Rect(s_w * 0.4, s_h * 0.7, s_w * 0.08, s_h * 0.25)
    leg_right = pygame.Rect(s_w * 0.52, s_h * 0.7, s_w * 0.08, s_h * 0.25)
    pygame.draw.rect(sprite_surface, pants_color, leg_left)
    pygame.draw.rect(sprite_surface, pants_color, leg_right)
    
    # Sword (held or displayed)
    sword_blade = pygame.Rect(s_w * 0.75, s_h * 0.3, s_w * 0.03, s_h * 0.3)
    sword_hilt = pygame.Rect(s_w * 0.73, s_h * 0.6, s_w * 0.07, s_h * 0.05)
    pygame.draw.rect(sprite_surface, sword_color, sword_blade)
    pygame.draw.rect(sprite_surface, (101, 67, 33), sword_hilt)
    
    return sprite_surface


def get_innkeeper_sprite(size: Tuple[int, int] = (64, 64)) -> pygame.Surface:
    """Generate a detailed Innkeeper NPC sprite procedurally."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size
    
    # Enhanced color palette
    skin_color = (222, 184, 135)       # Warm skin tone
    skin_shadow = (205, 160, 115)      # Darker skin for shadows
    apron_color = (255, 255, 255)      # Clean white apron
    apron_shadow = (240, 240, 240)     # Slight apron shadow
    shirt_color = (34, 139, 34)        # Forest green shirt
    shirt_highlight = (50, 180, 50)    # Lighter green highlight
    pants_color = (101, 67, 33)        # Rich brown pants
    pants_highlight = (120, 80, 45)    # Lighter brown highlight
    hair_color = (160, 82, 45)         # Warm brown hair
    hair_highlight = (180, 100, 60)    # Hair highlights
    eye_color = (34, 139, 34)          # Friendly green eyes
    belt_color = (80, 50, 30)          # Dark leather belt
    boot_color = (60, 40, 20)          # Dark brown boots
    mug_color = (139, 69, 19)          # Wooden mug
    beer_color = (218, 165, 32)        # Golden beer
    foam_color = (255, 255, 255)       # White beer foam
    
    # Head with kindly features
    head_center = (int(s_w * 0.5), int(s_h * 0.22))
    head_radius = int(s_w * 0.14)
    pygame.draw.circle(sprite_surface, skin_color, head_center, head_radius)
    pygame.draw.circle(sprite_surface, skin_shadow, 
                      (head_center[0] + 2, head_center[1] + 2), head_radius - 2)
    pygame.draw.circle(sprite_surface, skin_color, 
                      (head_center[0] - 1, head_center[1] - 1), head_radius - 3)
    
    # Cheerful hair with slight receding hairline
    hair_rect = pygame.Rect(head_center[0] - head_radius + 4, head_center[1] - head_radius + 1, 
                           (head_radius - 4) * 2, int(head_radius * 0.9))
    pygame.draw.ellipse(sprite_surface, hair_color, hair_rect)
    hair_highlight_rect = pygame.Rect(hair_rect.left + 3, hair_rect.top + 2, 
                                     hair_rect.width - 6, hair_rect.height // 2)
    pygame.draw.ellipse(sprite_surface, hair_highlight, hair_highlight_rect)
    
    # Friendly eyes with smile lines
    eye_left = (head_center[0] - int(s_w * 0.035), head_center[1] - int(s_h * 0.015))
    eye_right = (head_center[0] + int(s_w * 0.035), head_center[1] - int(s_h * 0.015))
    eye_radius = int(s_w * 0.02)  # Slightly larger, friendlier eyes
    
    # Eye whites
    pygame.draw.circle(sprite_surface, (255, 255, 255), eye_left, eye_radius)
    pygame.draw.circle(sprite_surface, (255, 255, 255), eye_right, eye_radius)
    # Green irises
    pygame.draw.circle(sprite_surface, eye_color, eye_left, int(eye_radius * 0.7))
    pygame.draw.circle(sprite_surface, eye_color, eye_right, int(eye_radius * 0.7))
    # Pupils
    pygame.draw.circle(sprite_surface, (0, 0, 0), eye_left, int(eye_radius * 0.4))
    pygame.draw.circle(sprite_surface, (0, 0, 0), eye_right, int(eye_radius * 0.4))
    # Eye highlights
    pygame.draw.circle(sprite_surface, (255, 255, 255), 
                      (eye_left[0] - 1, eye_left[1] - 1), 2)
    pygame.draw.circle(sprite_surface, (255, 255, 255), 
                      (eye_right[0] - 1, eye_right[1] - 1), 2)
    
    # Smile lines (crow's feet)
    smile_line_coords = [
        ((eye_left[0] + 6, eye_left[1] - 2), (eye_left[0] + 8, eye_left[1] + 1)),
        ((eye_left[0] + 6, eye_left[1] + 2), (eye_left[0] + 8, eye_left[1] + 3)),
        ((eye_right[0] - 6, eye_right[1] - 2), (eye_right[0] - 8, eye_right[1] + 1)),
        ((eye_right[0] - 6, eye_right[1] + 2), (eye_right[0] - 8, eye_right[1] + 3))
    ]
    for start, end in smile_line_coords:
        pygame.draw.line(sprite_surface, skin_shadow, start, end, 1)
    
    # Wide, genuine smile
    smile_center = (head_center[0], head_center[1] + int(s_h * 0.04))
    smile_rect = pygame.Rect(smile_center[0] - int(s_w * 0.04), smile_center[1] - 2, 
                            int(s_w * 0.08), 6)
    pygame.draw.arc(sprite_surface, (180, 100, 100), smile_rect, 0, 3.14159, 2)
    
    # Rosy cheeks
    cheek_left = (head_center[0] - int(s_w * 0.06), head_center[1] + int(s_h * 0.015))
    cheek_right = (head_center[0] + int(s_w * 0.06), head_center[1] + int(s_h * 0.015))
    cheek_surface = pygame.Surface((8, 6), pygame.SRCALPHA)
    pygame.draw.ellipse(cheek_surface, (255, 150, 150, 40), (0, 0, 8, 6))
    sprite_surface.blit(cheek_surface, (cheek_left[0] - 4, cheek_left[1] - 3))
    sprite_surface.blit(cheek_surface, (cheek_right[0] - 4, cheek_right[1] - 3))
    
    # Neck
    neck_rect = pygame.Rect(head_center[0] - int(s_w * 0.06), head_center[1] + head_radius - 3, 
                           int(s_w * 0.12), int(s_h * 0.08))
    pygame.draw.rect(sprite_surface, skin_color, neck_rect)
    
    # Body with shirt
    body_rect = pygame.Rect(int(s_w * 0.32), int(s_h * 0.35), int(s_w * 0.36), int(s_h * 0.4))
    pygame.draw.rect(sprite_surface, shirt_color, body_rect)
    pygame.draw.rect(sprite_surface, shirt_highlight, 
                    (body_rect.left + 2, body_rect.top + 2, body_rect.width - 4, body_rect.height // 3))
    
    # Clean white apron (prominent feature)
    apron_rect = pygame.Rect(int(s_w * 0.35), int(s_h * 0.4), int(s_w * 0.3), int(s_h * 0.35))
    pygame.draw.rect(sprite_surface, apron_color, apron_rect)
    pygame.draw.rect(sprite_surface, apron_shadow, 
                    (apron_rect.left + 1, apron_rect.bottom - 5, apron_rect.width - 2, 4))
    
    # Apron straps
    strap_left = (apron_rect.left + 4, apron_rect.top)
    strap_right = (apron_rect.right - 4, apron_rect.top)
    neck_connection = (head_center[0], head_center[1] + head_radius + 5)
    pygame.draw.line(sprite_surface, apron_color, strap_left, neck_connection, 3)
    pygame.draw.line(sprite_surface, apron_color, strap_right, neck_connection, 3)
    
    # Apron pocket
    pocket_rect = pygame.Rect(apron_rect.left + 6, apron_rect.top + int(s_h * 0.15), 
                             apron_rect.width - 12, int(s_h * 0.08))
    pygame.draw.rect(sprite_surface, apron_shadow, pocket_rect, 1)
    
    # Arms
    arm_left = pygame.Rect(int(s_w * 0.22), int(s_h * 0.38), int(s_w * 0.1), int(s_h * 0.28))
    arm_right = pygame.Rect(int(s_w * 0.68), int(s_h * 0.38), int(s_w * 0.1), int(s_h * 0.28))
    # Shirt sleeves (rolled up slightly)
    pygame.draw.rect(sprite_surface, shirt_color, arm_left)
    pygame.draw.rect(sprite_surface, shirt_color, arm_right)
    pygame.draw.rect(sprite_surface, shirt_highlight, 
                    (arm_left.left + 1, arm_left.top + 1, arm_left.width - 2, arm_left.height // 3))
    pygame.draw.rect(sprite_surface, shirt_highlight, 
                    (arm_right.left + 1, arm_right.top + 1, arm_right.width - 2, arm_right.height // 3))
    
    # Hands
    hand_left = (int(s_w * 0.27), int(s_h * 0.66))
    hand_right = (int(s_w * 0.73), int(s_h * 0.66))
    hand_radius = int(s_w * 0.035)
    pygame.draw.circle(sprite_surface, skin_color, hand_left, hand_radius)
    pygame.draw.circle(sprite_surface, skin_color, hand_right, hand_radius)
    
    # Belt
    belt_rect = pygame.Rect(int(s_w * 0.32), int(s_h * 0.62), int(s_w * 0.36), int(s_h * 0.05))
    pygame.draw.rect(sprite_surface, belt_color, belt_rect)
    buckle_rect = pygame.Rect(belt_rect.centerx - 4, belt_rect.centery - 3, 8, 6)
    pygame.draw.rect(sprite_surface, (169, 169, 169), buckle_rect)  # Silver buckle
    
    # Legs with pants
    leg_left = pygame.Rect(int(s_w * 0.38), int(s_h * 0.67), int(s_w * 0.1), int(s_h * 0.28))
    leg_right = pygame.Rect(int(s_w * 0.52), int(s_h * 0.67), int(s_w * 0.1), int(s_h * 0.28))
    pygame.draw.rect(sprite_surface, pants_color, leg_left)
    pygame.draw.rect(sprite_surface, pants_color, leg_right)
    pygame.draw.rect(sprite_surface, pants_highlight, 
                    (leg_left.left + 1, leg_left.top + 1, leg_left.width - 2, leg_left.height // 4))
    pygame.draw.rect(sprite_surface, pants_highlight, 
                    (leg_right.left + 1, leg_right.top + 1, leg_right.width - 2, leg_right.height // 4))
    
    # Comfortable boots
    boot_left = pygame.Rect(int(s_w * 0.36), int(s_h * 0.9), int(s_w * 0.12), int(s_h * 0.08))
    boot_right = pygame.Rect(int(s_w * 0.52), int(s_h * 0.9), int(s_w * 0.12), int(s_h * 0.08))
    pygame.draw.ellipse(sprite_surface, boot_color, boot_left)
    pygame.draw.ellipse(sprite_surface, boot_color, boot_right)
    
    # Large beer tankard (held in right hand)
    mug_body = pygame.Rect(int(s_w * 0.75), int(s_h * 0.5), int(s_w * 0.12), int(s_h * 0.18))
    pygame.draw.rect(sprite_surface, mug_color, mug_body)
    
    # Beer inside mug
    beer_rect = pygame.Rect(mug_body.left + 1, mug_body.top + 3, mug_body.width - 2, mug_body.height - 4)
    pygame.draw.rect(sprite_surface, beer_color, beer_rect)
    
    # Foam on top
    foam_rect = pygame.Rect(beer_rect.left, beer_rect.top - 2, beer_rect.width, 4)
    pygame.draw.ellipse(sprite_surface, foam_color, foam_rect)
    
    # Mug handle
    handle_center = (mug_body.right + 3, mug_body.centery)
    pygame.draw.circle(sprite_surface, mug_color, handle_center, 4, 2)  # Hollow circle for handle
    
    # Mug bands (decorative rings)
    pygame.draw.line(sprite_surface, (101, 51, 0), 
                    (mug_body.left, mug_body.top + 8), 
                    (mug_body.right, mug_body.top + 8), 1)
    pygame.draw.line(sprite_surface, (101, 51, 0), 
                    (mug_body.left, mug_body.bottom - 8), 
                    (mug_body.right, mug_body.bottom - 8), 1)
    
    # Bar towel on belt/apron
    towel_rect = pygame.Rect(int(s_w * 0.25), int(s_h * 0.58), int(s_w * 0.06), int(s_h * 0.12))
    pygame.draw.rect(sprite_surface, (255, 255, 255), towel_rect)
    pygame.draw.rect(sprite_surface, apron_shadow, towel_rect, 1)
    
    # Small key ring on belt (for inn rooms)
    key_ring_center = (belt_rect.left + 8, belt_rect.centery)
    pygame.draw.circle(sprite_surface, (192, 192, 192), key_ring_center, 3, 1)  # Ring
    # Small keys
    key_positions = [
        (key_ring_center[0] - 2, key_ring_center[1] + 4),
        (key_ring_center[0] + 1, key_ring_center[1] + 5),
        (key_ring_center[0] + 3, key_ring_center[1] + 3)
    ]
    for key_x, key_y in key_positions:
        pygame.draw.line(sprite_surface, (192, 192, 192), 
                        (key_x, key_y), (key_x, key_y + 3), 1)
        pygame.draw.circle(sprite_surface, (192, 192, 192), (key_x, key_y + 3), 1)
    
    return sprite_surface


def get_commoner_sprite(size: Tuple[int, int] = (64, 64)) -> pygame.Surface:
    """Generate a detailed Commoner NPC sprite procedurally."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Enhanced color palette (humble but clean appearance)
    skin_color = (222, 184, 135)       # Warm skin tone
    skin_shadow = (205, 160, 115)      # Darker skin for shadows
    shirt_color = (70, 130, 180)       # Steel blue shirt
    shirt_highlight = (100, 150, 200)  # Lighter blue highlight
    shirt_shadow = (50, 100, 140)      # Darker blue shadow
    pants_color = (101, 67, 33)        # Brown work pants
    pants_highlight = (120, 80, 45)    # Lighter brown highlight
    hair_color = (160, 82, 45)         # Common brown hair
    hair_highlight = (180, 100, 60)    # Hair highlights
    eye_color = (101, 67, 33)          # Brown eyes
    belt_color = (80, 50, 30)          # Simple leather belt
    boot_color = (60, 40, 20)          # Worn brown boots
    patch_color = (90, 110, 150)       # Fabric patches
    
    # Head with everyday features
    head_center = (int(s_w * 0.5), int(s_h * 0.22))
    head_radius = int(s_w * 0.13)
    pygame.draw.circle(sprite_surface, skin_color, head_center, head_radius)
    pygame.draw.circle(sprite_surface, skin_shadow, 
                      (head_center[0] + 1, head_center[1] + 2), head_radius - 2)
    pygame.draw.circle(sprite_surface, skin_color, 
                      (head_center[0] - 1, head_center[1] - 1), head_radius - 3)

    # Simple, practical hairstyle
    hair_rect = pygame.Rect(head_center[0] - head_radius + 3, head_center[1] - head_radius + 2, 
                           (head_radius - 3) * 2, int(head_radius * 0.9))
    pygame.draw.ellipse(sprite_surface, hair_color, hair_rect)
    hair_highlight_rect = pygame.Rect(hair_rect.left + 2, hair_rect.top + 2, 
                                     hair_rect.width - 4, hair_rect.height // 3)
    pygame.draw.ellipse(sprite_surface, hair_highlight, hair_highlight_rect)

    # Kind, ordinary eyes
    eye_left = (head_center[0] - int(s_w * 0.03), head_center[1] - int(s_h * 0.015))
    eye_right = (head_center[0] + int(s_w * 0.03), head_center[1] - int(s_h * 0.015))
    eye_radius = int(s_w * 0.017)
    
    # Eye whites
    pygame.draw.circle(sprite_surface, (255, 255, 255), eye_left, eye_radius)
    pygame.draw.circle(sprite_surface, (255, 255, 255), eye_right, eye_radius)
    # Brown irises
    pygame.draw.circle(sprite_surface, eye_color, eye_left, int(eye_radius * 0.7))
    pygame.draw.circle(sprite_surface, eye_color, eye_right, int(eye_radius * 0.7))
    # Pupils
    pygame.draw.circle(sprite_surface, (0, 0, 0), eye_left, int(eye_radius * 0.4))
    pygame.draw.circle(sprite_surface, (0, 0, 0), eye_right, int(eye_radius * 0.4))
    # Small highlights
    pygame.draw.circle(sprite_surface, (255, 255, 255), 
                      (eye_left[0] - 1, eye_left[1] - 1), 1)
    pygame.draw.circle(sprite_surface, (255, 255, 255), 
                      (eye_right[0] - 1, eye_right[1] - 1), 1)

    # Neutral expression
    mouth_rect = pygame.Rect(head_center[0] - int(s_w * 0.015), head_center[1] + int(s_h * 0.035), 
                            int(s_w * 0.03), 2)
    pygame.draw.rect(sprite_surface, (160, 120, 120), mouth_rect)

    # Neck
    neck_rect = pygame.Rect(head_center[0] - int(s_w * 0.055), head_center[1] + head_radius - 3, 
                           int(s_w * 0.11), int(s_h * 0.08))
    pygame.draw.rect(sprite_surface, skin_color, neck_rect)

    # Simple work shirt (well-worn but clean)
    body_rect = pygame.Rect(int(s_w * 0.33), int(s_h * 0.35), int(s_w * 0.34), int(s_h * 0.38))
    pygame.draw.rect(sprite_surface, shirt_color, body_rect)
    pygame.draw.rect(sprite_surface, shirt_highlight, 
                    (body_rect.left + 2, body_rect.top + 2, body_rect.width - 4, body_rect.height // 3))
    pygame.draw.rect(sprite_surface, shirt_shadow, 
                    (body_rect.left + 1, body_rect.bottom - 6, body_rect.width - 2, 5))
    
    # Small patches on shirt (signs of wear and repair)
    patch1 = pygame.Rect(body_rect.left + 5, body_rect.top + int(s_h * 0.12), 6, 4)
    patch2 = pygame.Rect(body_rect.right - 8, body_rect.top + int(s_h * 0.08), 5, 5)
    pygame.draw.rect(sprite_surface, patch_color, patch1)
    pygame.draw.rect(sprite_surface, patch_color, patch2)

    # Simple arms
    arm_left = pygame.Rect(int(s_w * 0.23), int(s_h * 0.38), int(s_w * 0.1), int(s_h * 0.26))
    arm_right = pygame.Rect(int(s_w * 0.67), int(s_h * 0.38), int(s_w * 0.1), int(s_h * 0.26))
    pygame.draw.rect(sprite_surface, shirt_color, arm_left)
    pygame.draw.rect(sprite_surface, shirt_color, arm_right)
    pygame.draw.rect(sprite_surface, shirt_highlight, 
                    (arm_left.left + 1, arm_left.top + 1, arm_left.width - 2, arm_left.height // 3))
    pygame.draw.rect(sprite_surface, shirt_highlight, 
                    (arm_right.left + 1, arm_right.top + 1, arm_right.width - 2, arm_right.height // 3))

    # Working hands
    hand_left = (int(s_w * 0.28), int(s_h * 0.64))
    hand_right = (int(s_w * 0.72), int(s_h * 0.64))
    hand_radius = int(s_w * 0.032)
    pygame.draw.circle(sprite_surface, skin_color, hand_left, hand_radius)
    pygame.draw.circle(sprite_surface, skin_color, hand_right, hand_radius)

    # Simple belt
    belt_rect = pygame.Rect(int(s_w * 0.33), int(s_h * 0.61), int(s_w * 0.34), int(s_h * 0.04))
    pygame.draw.rect(sprite_surface, belt_color, belt_rect)
    buckle_rect = pygame.Rect(belt_rect.centerx - 3, belt_rect.centery - 2, 6, 4)
    pygame.draw.rect(sprite_surface, (120, 120, 120), buckle_rect)  # Simple metal buckle

    # Work pants with wear marks
    leg_left = pygame.Rect(int(s_w * 0.39), int(s_h * 0.65), int(s_w * 0.09), int(s_h * 0.3))
    leg_right = pygame.Rect(int(s_w * 0.52), int(s_h * 0.65), int(s_w * 0.09), int(s_h * 0.3))
    pygame.draw.rect(sprite_surface, pants_color, leg_left)
    pygame.draw.rect(sprite_surface, pants_color, leg_right)
    pygame.draw.rect(sprite_surface, pants_highlight, 
                    (leg_left.left + 1, leg_left.top + 1, leg_left.width - 2, leg_left.height // 4))
    pygame.draw.rect(sprite_surface, pants_highlight, 
                    (leg_right.left + 1, leg_right.top + 1, leg_right.width - 2, leg_right.height // 4))

    # Knee patches (practical repairs)
    knee_patch_left = pygame.Rect(leg_left.left + 2, leg_left.top + int(s_h * 0.15), 5, 4)
    knee_patch_right = pygame.Rect(leg_right.left + 2, leg_right.top + int(s_h * 0.15), 5, 4)
    pygame.draw.rect(sprite_surface, (120, 80, 45), knee_patch_left)
    pygame.draw.rect(sprite_surface, (120, 80, 45), knee_patch_right)

    # Well-worn but serviceable boots
    boot_left = pygame.Rect(int(s_w * 0.37), int(s_h * 0.9), int(s_w * 0.11), int(s_h * 0.08))
    boot_right = pygame.Rect(int(s_w * 0.52), int(s_h * 0.9), int(s_w * 0.11), int(s_h * 0.08))
    pygame.draw.ellipse(sprite_surface, boot_color, boot_left)
    pygame.draw.ellipse(sprite_surface, boot_color, boot_right)
    
    # Boot wear marks
    pygame.draw.line(sprite_surface, (40, 25, 10), 
                    (boot_left.left + 2, boot_left.centery), 
                    (boot_left.right - 2, boot_left.centery), 1)
    pygame.draw.line(sprite_surface, (40, 25, 10), 
                    (boot_right.left + 2, boot_right.centery), 
                    (boot_right.right - 2, boot_right.centery), 1)

    # Small personal item (pendant or simple jewelry)
    pendant_center = (int(s_w * 0.5), int(s_h * 0.42))
    pygame.draw.circle(sprite_surface, (169, 169, 169), pendant_center, 2)
    pygame.draw.line(sprite_surface, (80, 80, 80), 
                    (pendant_center[0], neck_rect.bottom), pendant_center, 1)

    return sprite_surface


def get_guard_sprite(size: Tuple[int, int] = (64, 64)) -> pygame.Surface:
    """Generate a detailed Guard NPC sprite procedurally."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Enhanced color palette (professional guard uniform)
    skin_color = (222, 184, 135)       # Warm skin tone
    skin_shadow = (205, 160, 115)      # Darker skin for shadows
    armor_color = (169, 169, 169)      # Polished chainmail/plate
    armor_dark = (120, 120, 120)       # Darker armor shadows
    armor_highlight = (200, 200, 200)  # Bright armor highlights
    tabard_color = (139, 0, 0)         # Deep red tabard (town colors)
    tabard_highlight = (180, 30, 30)   # Lighter red highlight
    pants_color = (60, 60, 60)         # Dark gray/black pants
    pants_highlight = (80, 80, 80)     # Lighter gray highlight
    hair_color = (101, 67, 33)         # Military brown hair
    hair_highlight = (120, 80, 45)     # Hair highlights
    eye_color = (70, 130, 180)         # Alert blue eyes
    sword_blade = (220, 220, 220)      # Bright polished steel
    sword_hilt = (139, 69, 19)         # Brown leather grip
    sword_guard = (192, 192, 192)      # Steel crossguard
    shield_color = (139, 69, 19)       # Brown wood shield base
    shield_metal = (169, 169, 169)     # Metal shield reinforcement
    belt_color = (80, 50, 30)          # Dark leather belt
    boot_color = (30, 20, 10)          # Black military boots
    
    # Head with disciplined features
    head_center = (int(s_w * 0.5), int(s_h * 0.22))
    head_radius = int(s_w * 0.13)
    pygame.draw.circle(sprite_surface, skin_color, head_center, head_radius)
    pygame.draw.circle(sprite_surface, skin_shadow, 
                      (head_center[0] + 2, head_center[1] + 2), head_radius - 2)
    pygame.draw.circle(sprite_surface, skin_color, 
                      (head_center[0] - 1, head_center[1] - 1), head_radius - 3)

    # Short, practical military haircut
    hair_rect = pygame.Rect(head_center[0] - int(head_radius * 0.8), head_center[1] - head_radius + 2,
                           int(head_radius * 1.6), int(head_radius * 0.7))
    pygame.draw.ellipse(sprite_surface, hair_color, hair_rect)
    hair_highlight_rect = pygame.Rect(hair_rect.left + 2, hair_rect.top + 1, 
                                     hair_rect.width - 4, hair_rect.height // 3)
    pygame.draw.ellipse(sprite_surface, hair_highlight, hair_highlight_rect)

    # Alert, watchful eyes
    eye_left = (head_center[0] - int(s_w * 0.03), head_center[1] - int(s_h * 0.015))
    eye_right = (head_center[0] + int(s_w * 0.03), head_center[1] - int(s_h * 0.015))
    eye_radius = int(s_w * 0.018)
    
    # Eye whites
    pygame.draw.circle(sprite_surface, (255, 255, 255), eye_left, eye_radius)
    pygame.draw.circle(sprite_surface, (255, 255, 255), eye_right, eye_radius)
    # Blue irises (alert and focused)
    pygame.draw.circle(sprite_surface, eye_color, eye_left, int(eye_radius * 0.7))
    pygame.draw.circle(sprite_surface, eye_color, eye_right, int(eye_radius * 0.7))
    # Pupils
    pygame.draw.circle(sprite_surface, (0, 0, 0), eye_left, int(eye_radius * 0.4))
    pygame.draw.circle(sprite_surface, (0, 0, 0), eye_right, int(eye_radius * 0.4))
    # Sharp highlights
    pygame.draw.circle(sprite_surface, (255, 255, 255), 
                      (eye_left[0] - 1, eye_left[1] - 1), 2)
    pygame.draw.circle(sprite_surface, (255, 255, 255), 
                      (eye_right[0] - 1, eye_right[1] - 1), 2)

    # Stern eyebrows
    eyebrow_left = pygame.Rect(eye_left[0] - 6, eye_left[1] - 7, 12, 3)
    eyebrow_right = pygame.Rect(eye_right[0] - 6, eye_right[1] - 7, 12, 3)
    pygame.draw.ellipse(sprite_surface, hair_color, eyebrow_left)
    pygame.draw.ellipse(sprite_surface, hair_color, eyebrow_right)

    # Serious, disciplined expression
    mouth_rect = pygame.Rect(head_center[0] - int(s_w * 0.02), head_center[1] + int(s_h * 0.04), 
                            int(s_w * 0.04), 2)
    pygame.draw.rect(sprite_surface, (140, 100, 100), mouth_rect)

    # Strong jawline enhancement
    jaw_points = [
        (head_center[0] - int(s_w * 0.08), head_center[1] + int(s_h * 0.08)),
        (head_center[0], head_center[1] + int(s_h * 0.12)),
        (head_center[0] + int(s_w * 0.08), head_center[1] + int(s_h * 0.08))
    ]
    for i in range(len(jaw_points) - 1):
        pygame.draw.line(sprite_surface, skin_shadow, jaw_points[i], jaw_points[i + 1], 1)

    # Neck
    neck_rect = pygame.Rect(head_center[0] - int(s_w * 0.06), head_center[1] + head_radius - 3, 
                           int(s_w * 0.12), int(s_h * 0.08))
    pygame.draw.rect(sprite_surface, skin_color, neck_rect)

    # Chainmail/armor shirt base
    body_rect = pygame.Rect(int(s_w * 0.32), int(s_h * 0.35), int(s_w * 0.36), int(s_h * 0.4))
    pygame.draw.rect(sprite_surface, armor_color, body_rect)
    pygame.draw.rect(sprite_surface, armor_highlight, 
                    (body_rect.left + 2, body_rect.top + 2, body_rect.width - 4, body_rect.height // 3))
    pygame.draw.rect(sprite_surface, armor_dark, 
                    (body_rect.left + 1, body_rect.bottom - 6, body_rect.width - 2, 5))

    # Chainmail texture (small overlapping circles to simulate mail links)
    for row in range(3):
        for col in range(4):
            mail_x = body_rect.left + 4 + col * 6
            mail_y = body_rect.top + 8 + row * 4
            pygame.draw.circle(sprite_surface, armor_dark, (mail_x, mail_y), 2, 1)

    # Red tabard over armor (with town heraldry)
    tabard_rect = pygame.Rect(int(s_w * 0.36), int(s_h * 0.38), int(s_w * 0.28), int(s_h * 0.35))
    pygame.draw.rect(sprite_surface, tabard_color, tabard_rect)
    pygame.draw.rect(sprite_surface, tabard_highlight, 
                    (tabard_rect.left + 1, tabard_rect.top + 1, tabard_rect.width - 2, tabard_rect.height // 4))

    # Town emblem on tabard (simple shield design)
    emblem_center = (tabard_rect.centerx, tabard_rect.top + int(s_h * 0.12))
    emblem_points = [
        (emblem_center[0], emblem_center[1] - 6),
        (emblem_center[0] - 4, emblem_center[1] - 2),
        (emblem_center[0] - 4, emblem_center[1] + 4),
        (emblem_center[0], emblem_center[1] + 8),
        (emblem_center[0] + 4, emblem_center[1] + 4),
        (emblem_center[0] + 4, emblem_center[1] - 2)
    ]
    pygame.draw.polygon(sprite_surface, (255, 215, 0), emblem_points)  # Gold emblem
    pygame.draw.polygon(sprite_surface, (200, 170, 0), emblem_points, 1)  # Gold outline

    # Armored arms
    arm_left = pygame.Rect(int(s_w * 0.22), int(s_h * 0.38), int(s_w * 0.1), int(s_h * 0.28))
    arm_right = pygame.Rect(int(s_w * 0.68), int(s_h * 0.38), int(s_w * 0.1), int(s_h * 0.28))
    pygame.draw.rect(sprite_surface, armor_color, arm_left)
    pygame.draw.rect(sprite_surface, armor_color, arm_right)
    pygame.draw.rect(sprite_surface, armor_highlight, 
                    (arm_left.left + 1, arm_left.top + 1, arm_left.width - 2, arm_left.height // 3))
    pygame.draw.rect(sprite_surface, armor_highlight, 
                    (arm_right.left + 1, arm_right.top + 1, arm_right.width - 2, arm_right.height // 3))

    # Gauntleted hands
    hand_left = (int(s_w * 0.27), int(s_h * 0.66))
    hand_right = (int(s_w * 0.73), int(s_h * 0.66))
    hand_radius = int(s_w * 0.035)
    pygame.draw.circle(sprite_surface, armor_color, hand_left, hand_radius)
    pygame.draw.circle(sprite_surface, armor_color, hand_right, hand_radius)
    pygame.draw.circle(sprite_surface, armor_highlight, 
                      (hand_left[0] - 1, hand_left[1] - 1), hand_radius - 2)
    pygame.draw.circle(sprite_surface, armor_highlight, 
                      (hand_right[0] - 1, hand_right[1] - 1), hand_radius - 2)

    # Military belt with equipment
    belt_rect = pygame.Rect(int(s_w * 0.32), int(s_h * 0.62), int(s_w * 0.36), int(s_h * 0.06))
    pygame.draw.rect(sprite_surface, belt_color, belt_rect)
    buckle_rect = pygame.Rect(belt_rect.centerx - 4, belt_rect.centery - 3, 8, 6)
    pygame.draw.rect(sprite_surface, armor_color, buckle_rect)  # Metal buckle

    # Belt pouches and attachments
    pouch_positions = [
        (belt_rect.left + 6, belt_rect.centery - 2, 8, 6),   # Money pouch
        (belt_rect.right - 14, belt_rect.centery - 3, 6, 8), # Key pouch
    ]
    for pouch_rect in pouch_positions:
        pygame.draw.rect(sprite_surface, belt_color, pouch_rect)
        pygame.draw.rect(sprite_surface, (60, 35, 20), pouch_rect, 1)

    # Armored legs
    leg_left = pygame.Rect(int(s_w * 0.38), int(s_h * 0.67), int(s_w * 0.1), int(s_h * 0.28))
    leg_right = pygame.Rect(int(s_w * 0.52), int(s_h * 0.67), int(s_w * 0.1), int(s_h * 0.28))
    pygame.draw.rect(sprite_surface, pants_color, leg_left)
    pygame.draw.rect(sprite_surface, pants_color, leg_right)
    pygame.draw.rect(sprite_surface, pants_highlight, 
                    (leg_left.left + 1, leg_left.top + 1, leg_left.width - 2, leg_left.height // 4))
    pygame.draw.rect(sprite_surface, pants_highlight, 
                    (leg_right.left + 1, leg_right.top + 1, leg_right.width - 2, leg_right.height // 4))

    # Shin guards
    shin_left = pygame.Rect(leg_left.left + 1, leg_left.top + int(s_h * 0.1), leg_left.width - 2, int(s_h * 0.15))
    shin_right = pygame.Rect(leg_right.left + 1, leg_right.top + int(s_h * 0.1), leg_right.width - 2, int(s_h * 0.15))
    pygame.draw.rect(sprite_surface, armor_color, shin_left)
    pygame.draw.rect(sprite_surface, armor_color, shin_right)

    # Military boots
    boot_left = pygame.Rect(int(s_w * 0.36), int(s_h * 0.9), int(s_w * 0.12), int(s_h * 0.08))
    boot_right = pygame.Rect(int(s_w * 0.52), int(s_h * 0.9), int(s_w * 0.12), int(s_h * 0.08))
    pygame.draw.ellipse(sprite_surface, boot_color, boot_left)
    pygame.draw.ellipse(sprite_surface, boot_color, boot_right)
    
    # Boot buckles and straps
    pygame.draw.line(sprite_surface, armor_color, 
                    (boot_left.left + 2, boot_left.centery), 
                    (boot_left.right - 2, boot_left.centery), 2)
    pygame.draw.line(sprite_surface, armor_color, 
                    (boot_right.left + 2, boot_right.centery), 
                    (boot_right.right - 2, boot_right.centery), 2)

    # Sword at side (sheathed)
    scabbard_rect = pygame.Rect(int(s_w * 0.18), int(s_h * 0.5), int(s_w * 0.04), int(s_h * 0.25))
    pygame.draw.rect(sprite_surface, belt_color, scabbard_rect)
    pygame.draw.rect(sprite_surface, armor_color, 
                    (scabbard_rect.left + 1, scabbard_rect.top + 1, scabbard_rect.width - 2, 4))  # Metal fittings

    # Sword hilt visible
    hilt_rect = pygame.Rect(scabbard_rect.left - 1, scabbard_rect.top - 8, scabbard_rect.width + 2, 8)
    pygame.draw.rect(sprite_surface, sword_hilt, hilt_rect)
    
    # Crossguard
    guard_rect = pygame.Rect(hilt_rect.left - 2, hilt_rect.top + 6, hilt_rect.width + 4, 2)
    pygame.draw.rect(sprite_surface, sword_guard, guard_rect)

    # Shield on back/arm
    shield_main = pygame.Rect(int(s_w * 0.75), int(s_h * 0.4), int(s_w * 0.12), int(s_h * 0.2))
    pygame.draw.ellipse(sprite_surface, shield_color, shield_main)
    
    # Shield metal rim
    pygame.draw.ellipse(sprite_surface, shield_metal, shield_main, 2)
    
    # Shield boss (center reinforcement)
    boss_center = (shield_main.centerx, shield_main.centery)
    pygame.draw.circle(sprite_surface, shield_metal, boss_center, 4)
    pygame.draw.circle(sprite_surface, armor_highlight, boss_center, 2)

    # Shield strap
    pygame.draw.line(sprite_surface, belt_color, 
                    (shield_main.left + 2, shield_main.top + 4), 
                    (arm_right.centerx, arm_right.centery), 3)

    return sprite_surface


def get_mayor_sprite(size: Tuple[int, int] = (64, 64)) -> pygame.Surface:
    """Generate a detailed Mayor NPC sprite procedurally."""
    sprite_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Enhanced color palette (formal, distinguished attire)
    skin_color = (222, 184, 135)       # Warm skin tone
    skin_shadow = (205, 160, 115)      # Darker skin for shadows
    suit_color = (25, 25, 112)         # Midnight blue formal suit
    suit_highlight = (40, 40, 140)     # Lighter blue highlight
    suit_shadow = (15, 15, 80)         # Darker blue shadow
    vest_color = (220, 220, 220)       # Light gray vest
    vest_shadow = (200, 200, 200)      # Vest shadow
    pants_color = (25, 25, 112)        # Matching blue formal pants
    pants_highlight = (40, 40, 140)    # Pants highlight
    hair_color = (105, 105, 105)       # Distinguished gray hair
    hair_highlight = (140, 140, 140)   # Silver hair highlights
    chain_color = (255, 215, 0)        # Gold chain of office
    chain_highlight = (255, 235, 50)   # Bright gold highlight
    eye_color = (70, 130, 180)         # Wise blue eyes
    boot_color = (0, 0, 0)             # Black formal shoes
    button_color = (255, 215, 0)       # Gold buttons
    bow_tie_color = (139, 0, 0)        # Deep red bow tie
    
    # Head with distinguished features
    head_center = (int(s_w * 0.5), int(s_h * 0.22))
    head_radius = int(s_w * 0.14)
    pygame.draw.circle(sprite_surface, skin_color, head_center, head_radius)
    pygame.draw.circle(sprite_surface, skin_shadow, 
                      (head_center[0] + 2, head_center[1] + 2), head_radius - 2)
    pygame.draw.circle(sprite_surface, skin_color, 
                      (head_center[0] - 1, head_center[1] - 1), head_radius - 3)

    # Distinguished gray hair (well-groomed)
    hair_rect = pygame.Rect(head_center[0] - head_radius + 2, head_center[1] - head_radius + 1, 
                           (head_radius - 2) * 2, int(head_radius * 0.9))
    pygame.draw.ellipse(sprite_surface, hair_color, hair_rect)
    hair_highlight_rect = pygame.Rect(hair_rect.left + 3, hair_rect.top + 2, 
                                     hair_rect.width - 6, hair_rect.height // 2)
    pygame.draw.ellipse(sprite_surface, hair_highlight, hair_highlight_rect)

    # Receding hairline (dignified aging)
    hairline_rect = pygame.Rect(head_center[0] - int(head_radius * 0.6), head_center[1] - head_radius + 3, 
                               int(head_radius * 1.2), int(head_radius * 0.4))
    pygame.draw.ellipse(sprite_surface, skin_color, hairline_rect)

    # Well-trimmed mustache
    mustache_rect = pygame.Rect(head_center[0] - int(s_w * 0.025), head_center[1] + int(s_h * 0.02), 
                               int(s_w * 0.05), int(s_h * 0.015))
    pygame.draw.ellipse(sprite_surface, hair_color, mustache_rect)

    # Wise, authoritative eyes
    eye_left = (head_center[0] - int(s_w * 0.035), head_center[1] - int(s_h * 0.015))
    eye_right = (head_center[0] + int(s_w * 0.035), head_center[1] - int(s_h * 0.015))
    eye_radius = int(s_w * 0.019)
    
    # Eye whites
    pygame.draw.circle(sprite_surface, (255, 255, 255), eye_left, eye_radius)
    pygame.draw.circle(sprite_surface, (255, 255, 255), eye_right, eye_radius)
    # Blue irises (wise and steady)
    pygame.draw.circle(sprite_surface, eye_color, eye_left, int(eye_radius * 0.7))
    pygame.draw.circle(sprite_surface, eye_color, eye_right, int(eye_radius * 0.7))
    # Pupils
    pygame.draw.circle(sprite_surface, (0, 0, 0), eye_left, int(eye_radius * 0.4))
    pygame.draw.circle(sprite_surface, (0, 0, 0), eye_right, int(eye_radius * 0.4))
    # Eye highlights
    pygame.draw.circle(sprite_surface, (255, 255, 255), 
                      (eye_left[0] - 1, eye_left[1] - 1), 2)
    pygame.draw.circle(sprite_surface, (255, 255, 255), 
                      (eye_right[0] - 1, eye_right[1] - 1), 2)

    # Smile lines and character wrinkles
    smile_line_coords = [
        ((eye_left[0] + 7, eye_left[1] + 2), (eye_left[0] + 9, eye_left[1] + 4)),
        ((eye_right[0] - 7, eye_right[1] + 2), (eye_right[0] - 9, eye_right[1] + 4))
    ]
    for start, end in smile_line_coords:
        pygame.draw.line(sprite_surface, skin_shadow, start, end, 1)

    # Dignified smile
    smile_center = (head_center[0], head_center[1] + int(s_h * 0.04))
    smile_rect = pygame.Rect(smile_center[0] - int(s_w * 0.03), smile_center[1] - 2, 
                            int(s_w * 0.06), 5)
    pygame.draw.arc(sprite_surface, (160, 120, 120), smile_rect, 0, 3.14159, 2)

    # Neck with formal collar
    neck_rect = pygame.Rect(head_center[0] - int(s_w * 0.06), head_center[1] + head_radius - 3, 
                           int(s_w * 0.12), int(s_h * 0.08))
    pygame.draw.rect(sprite_surface, skin_color, neck_rect)

    # Formal suit jacket
    body_rect = pygame.Rect(int(s_w * 0.32), int(s_h * 0.35), int(s_w * 0.36), int(s_h * 0.42))
    pygame.draw.rect(sprite_surface, suit_color, body_rect)
    pygame.draw.rect(sprite_surface, suit_highlight, 
                    (body_rect.left + 2, body_rect.top + 2, body_rect.width - 4, body_rect.height // 3))
    pygame.draw.rect(sprite_surface, suit_shadow, 
                    (body_rect.left + 1, body_rect.bottom - 8, body_rect.width - 2, 7))

    # Suit lapels
    lapel_left = [
        (body_rect.left + 2, body_rect.top + 5),
        (body_rect.left + 8, body_rect.top + 2),
        (body_rect.left + 12, body_rect.top + 15),
        (body_rect.left + 6, body_rect.top + 18)
    ]
    lapel_right = [
        (body_rect.right - 2, body_rect.top + 5),
        (body_rect.right - 8, body_rect.top + 2),
        (body_rect.right - 12, body_rect.top + 15),
        (body_rect.right - 6, body_rect.top + 18)
    ]
    pygame.draw.polygon(sprite_surface, suit_highlight, lapel_left)
    pygame.draw.polygon(sprite_surface, suit_highlight, lapel_right)

    # Formal vest
    vest_rect = pygame.Rect(int(s_w * 0.37), int(s_h * 0.4), int(s_w * 0.26), int(s_h * 0.28))
    pygame.draw.rect(sprite_surface, vest_color, vest_rect)
    pygame.draw.rect(sprite_surface, vest_shadow, 
                    (vest_rect.left + 1, vest_rect.bottom - 4, vest_rect.width - 2, 3))

    # Vest buttons (gold)
    vest_button_positions = [
        (vest_rect.centerx, vest_rect.top + int(s_h * 0.06)),
        (vest_rect.centerx, vest_rect.top + int(s_h * 0.12)),
        (vest_rect.centerx, vest_rect.top + int(s_h * 0.18)),
        (vest_rect.centerx, vest_rect.top + int(s_h * 0.24))
    ]
    for btn_x, btn_y in vest_button_positions:
        pygame.draw.circle(sprite_surface, button_color, (btn_x, btn_y), 2)
        pygame.draw.circle(sprite_surface, chain_highlight, (btn_x - 1, btn_y - 1), 1)

    # Bow tie
    bow_tie_center = (int(s_w * 0.5), int(s_h * 0.38))
    bow_tie_left = pygame.Rect(bow_tie_center[0] - 6, bow_tie_center[1] - 2, 5, 4)
    bow_tie_right = pygame.Rect(bow_tie_center[0] + 1, bow_tie_center[1] - 2, 5, 4)
    bow_tie_knot = pygame.Rect(bow_tie_center[0] - 1, bow_tie_center[1] - 2, 2, 4)
    pygame.draw.ellipse(sprite_surface, bow_tie_color, bow_tie_left)
    pygame.draw.ellipse(sprite_surface, bow_tie_color, bow_tie_right)
    pygame.draw.rect(sprite_surface, (100, 0, 0), bow_tie_knot)

    # Formal suit arms
    arm_left = pygame.Rect(int(s_w * 0.22), int(s_h * 0.38), int(s_w * 0.1), int(s_h * 0.28))
    arm_right = pygame.Rect(int(s_w * 0.68), int(s_h * 0.38), int(s_w * 0.1), int(s_h * 0.28))
    pygame.draw.rect(sprite_surface, suit_color, arm_left)
    pygame.draw.rect(sprite_surface, suit_color, arm_right)
    pygame.draw.rect(sprite_surface, suit_highlight, 
                    (arm_left.left + 1, arm_left.top + 1, arm_left.width - 2, arm_left.height // 3))
    pygame.draw.rect(sprite_surface, suit_highlight, 
                    (arm_right.left + 1, arm_right.top + 1, arm_right.width - 2, arm_right.height // 3))

    # Formal cufflinks
    cuff_left = (arm_left.centerx, arm_left.bottom - 5)
    cuff_right = (arm_right.centerx, arm_right.bottom - 5)
    pygame.draw.circle(sprite_surface, button_color, cuff_left, 2)
    pygame.draw.circle(sprite_surface, button_color, cuff_right, 2)

    # Well-manicured hands
    hand_left = (int(s_w * 0.27), int(s_h * 0.66))
    hand_right = (int(s_w * 0.73), int(s_h * 0.66))
    hand_radius = int(s_w * 0.035)
    pygame.draw.circle(sprite_surface, skin_color, hand_left, hand_radius)
    pygame.draw.circle(sprite_surface, skin_color, hand_right, hand_radius)

    # Formal belt (barely visible under vest)
    belt_rect = pygame.Rect(int(s_w * 0.36), int(s_h * 0.65), int(s_w * 0.28), int(s_h * 0.03))
    pygame.draw.rect(sprite_surface, (0, 0, 0), belt_rect)  # Black formal belt

    # Formal trousers
    leg_left = pygame.Rect(int(s_w * 0.38), int(s_h * 0.68), int(s_w * 0.1), int(s_h * 0.27))
    leg_right = pygame.Rect(int(s_w * 0.52), int(s_h * 0.68), int(s_w * 0.1), int(s_h * 0.27))
    pygame.draw.rect(sprite_surface, pants_color, leg_left)
    pygame.draw.rect(sprite_surface, pants_color, leg_right)
    pygame.draw.rect(sprite_surface, pants_highlight, 
                    (leg_left.left + 1, leg_left.top + 1, leg_left.width - 2, leg_left.height // 4))
    pygame.draw.rect(sprite_surface, pants_highlight, 
                    (leg_right.left + 1, leg_right.top + 1, leg_right.width - 2, leg_right.height // 4))

    # Trouser creases (formal tailoring)
    pygame.draw.line(sprite_surface, suit_shadow, 
                    (leg_left.centerx, leg_left.top), 
                    (leg_left.centerx, leg_left.bottom), 1)
    pygame.draw.line(sprite_surface, suit_shadow, 
                    (leg_right.centerx, leg_right.top), 
                    (leg_right.centerx, leg_right.bottom), 1)

    # Polished formal shoes
    shoe_left = pygame.Rect(int(s_w * 0.36), int(s_h * 0.9), int(s_w * 0.12), int(s_h * 0.08))
    shoe_right = pygame.Rect(int(s_w * 0.52), int(s_h * 0.9), int(s_w * 0.12), int(s_h * 0.08))
    pygame.draw.ellipse(sprite_surface, boot_color, shoe_left)
    pygame.draw.ellipse(sprite_surface, boot_color, shoe_right)
    
    # Shoe shine highlights
    pygame.draw.ellipse(sprite_surface, (60, 60, 60), 
                       (shoe_left.left + 2, shoe_left.top + 1, shoe_left.width - 4, shoe_left.height // 3))
    pygame.draw.ellipse(sprite_surface, (60, 60, 60), 
                       (shoe_right.left + 2, shoe_right.top + 1, shoe_right.width - 4, shoe_right.height // 3))

    # Chain of office (most prominent feature)
    chain_start_left = (int(s_w * 0.42), int(s_h * 0.35))
    chain_start_right = (int(s_w * 0.58), int(s_h * 0.35))
    chain_low_point = (int(s_w * 0.5), int(s_h * 0.45))
    
    # Chain links (detailed)
    chain_segments = 8
    for i in range(chain_segments + 1):
        t = i / chain_segments
        if t <= 0.5:
            # Left side of chain
            x = int(chain_start_left[0] + t * 2 * (chain_low_point[0] - chain_start_left[0]))
            y = int(chain_start_left[1] + (t * 2) ** 2 * (chain_low_point[1] - chain_start_left[1]))
        else:
            # Right side of chain
            t_right = (t - 0.5) * 2
            x = int(chain_low_point[0] + t_right * (chain_start_right[0] - chain_low_point[0]))
            y = int(chain_low_point[1] + (1 - t_right) ** 2 * (chain_start_right[1] - chain_low_point[1]))
        
        pygame.draw.circle(sprite_surface, chain_color, (x, y), 2)
        pygame.draw.circle(sprite_surface, chain_highlight, (x - 1, y - 1), 1)

    # Ornate pendant/medallion
    pendant_center = (int(s_w * 0.5), int(s_h * 0.45))
    pygame.draw.circle(sprite_surface, chain_color, pendant_center, 6)
    pygame.draw.circle(sprite_surface, chain_highlight, 
                      (pendant_center[0] - 1, pendant_center[1] - 1), 4)
    
    # Town seal on pendant (simplified heraldic design)
    seal_points = [
        (pendant_center[0], pendant_center[1] - 3),
        (pendant_center[0] - 2, pendant_center[1] - 1),
        (pendant_center[0] - 3, pendant_center[1] + 2),
        (pendant_center[0], pendant_center[1] + 3),
        (pendant_center[0] + 3, pendant_center[1] + 2),
        (pendant_center[0] + 2, pendant_center[1] - 1)
    ]
    pygame.draw.polygon(sprite_surface, (200, 170, 0), seal_points)
    pygame.draw.circle(sprite_surface, chain_highlight, pendant_center, 1)

    # Pocket watch chain (subtle detail)
    watch_chain_start = (vest_rect.left + 4, vest_rect.top + int(s_h * 0.08))
    watch_chain_end = (int(s_w * 0.25), int(s_h * 0.58))
    pygame.draw.line(sprite_surface, chain_color, watch_chain_start, watch_chain_end, 1)
    pygame.draw.circle(sprite_surface, chain_color, watch_chain_end, 2)

    # Mayoral ring (barely visible on hand)
    pygame.draw.circle(sprite_surface, chain_color, 
                      (hand_right[0] - 2, hand_right[1] - 3), 1)

    return sprite_surface


# Icon generation functions for map editor
def get_merchant_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a Merchant icon for map editor."""
    icon_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Simple merchant bag icon
    bag_rect = pygame.Rect(s_w * 0.2, s_h * 0.3, s_w * 0.6, s_h * 0.5)
    pygame.draw.rect(icon_surface, (101, 67, 33), bag_rect)

    # Coins
    pygame.draw.circle(icon_surface, (255, 215, 0), (int(s_w * 0.3), int(s_h * 0.4)), int(s_w * 0.08))
    pygame.draw.circle(icon_surface, (255, 215, 0), (int(s_w * 0.5), int(s_h * 0.5)), int(s_w * 0.08))
    pygame.draw.circle(icon_surface, (255, 215, 0), (int(s_w * 0.7), int(s_h * 0.6)), int(s_w * 0.08))

    return icon_surface


def get_armourer_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate an Armourer icon for map editor."""
    icon_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Shield icon
    shield_rect = pygame.Rect(s_w * 0.25, s_h * 0.2, s_w * 0.5, s_h * 0.6)
    pygame.draw.ellipse(icon_surface, (169, 169, 169), shield_rect)

    # Cross on shield
    pygame.draw.line(icon_surface, (255, 255, 255),
                    (s_w * 0.5, s_h * 0.3), (s_w * 0.5, s_h * 0.7), 3)
    pygame.draw.line(icon_surface, (255, 255, 255),
                    (s_w * 0.35, s_h * 0.5), (s_w * 0.65, s_h * 0.5), 3)

    return icon_surface


def get_weaponsmith_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a Weaponsmith icon for map editor."""
    icon_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Sword icon
    blade_rect = pygame.Rect(s_w * 0.45, s_h * 0.1, s_w * 0.1, s_h * 0.6)
    hilt_rect = pygame.Rect(s_w * 0.35, s_h * 0.7, s_w * 0.3, s_h * 0.08)
    pygame.draw.rect(icon_surface, (192, 192, 192), blade_rect)
    pygame.draw.rect(icon_surface, (101, 67, 33), hilt_rect)

    return icon_surface


def get_innkeeper_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate an Innkeeper icon for map editor."""
    icon_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Mug/tankard icon
    mug_rect = pygame.Rect(s_w * 0.3, s_h * 0.3, s_w * 0.4, s_h * 0.5)
    handle_rect = pygame.Rect(s_w * 0.7, s_h * 0.45, s_w * 0.1, s_h * 0.2)
    pygame.draw.rect(icon_surface, (160, 82, 45), mug_rect)
    pygame.draw.rect(icon_surface, (160, 82, 45), handle_rect)

    # Foam on top
    foam_rect = pygame.Rect(s_w * 0.32, s_h * 0.25, s_w * 0.36, s_h * 0.1)
    pygame.draw.ellipse(icon_surface, (255, 255, 255), foam_rect)

    return icon_surface


def get_commoner_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a Commoner icon for map editor."""
    icon_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Simple person silhouette
    head_center = (s_w * 0.5, s_h * 0.3)
    head_radius = int(s_w * 0.15)
    pygame.draw.circle(icon_surface, (70, 130, 180), head_center, head_radius)

    body_rect = pygame.Rect(s_w * 0.4, s_h * 0.45, s_w * 0.2, s_h * 0.35)
    pygame.draw.rect(icon_surface, (70, 130, 180), body_rect)

    return icon_surface


def get_guard_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a Guard icon for map editor."""
    icon_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Helmet icon
    helmet_rect = pygame.Rect(s_w * 0.25, s_h * 0.2, s_w * 0.5, s_h * 0.4)
    pygame.draw.ellipse(icon_surface, (169, 169, 169), helmet_rect)

    # Visor
    visor_rect = pygame.Rect(s_w * 0.3, s_h * 0.35, s_w * 0.4, s_h * 0.1)
    pygame.draw.rect(icon_surface, (60, 60, 60), visor_rect)

    # Plume
    plume_points = [(s_w * 0.5, s_h * 0.2), (s_w * 0.45, s_h * 0.1), (s_w * 0.55, s_h * 0.1)]
    pygame.draw.polygon(icon_surface, (255, 0, 0), plume_points)

    return icon_surface


def get_mayor_icon(size: Tuple[int, int] = (32, 32)) -> pygame.Surface:
    """Generate a Mayor icon for map editor."""
    icon_surface = pygame.Surface(size, pygame.SRCALPHA)
    s_w, s_h = size

    # Crown/ceremonial hat icon
    crown_rect = pygame.Rect(s_w * 0.2, s_h * 0.2, s_w * 0.6, s_h * 0.3)
    pygame.draw.rect(icon_surface, (255, 215, 0), crown_rect)  # Gold crown

    # Crown points
    point1 = (s_w * 0.3, s_h * 0.2)
    point2 = (s_w * 0.25, s_h * 0.1)
    point3 = (s_w * 0.35, s_h * 0.1)
    pygame.draw.polygon(icon_surface, (255, 215, 0), [point1, point2, point3])

    point4 = (s_w * 0.5, s_h * 0.2)
    point5 = (s_w * 0.45, s_h * 0.05)
    point6 = (s_w * 0.55, s_h * 0.05)
    pygame.draw.polygon(icon_surface, (255, 215, 0), [point4, point5, point6])

    point7 = (s_w * 0.7, s_h * 0.2)
    point8 = (s_w * 0.65, s_h * 0.1)
    point9 = (s_w * 0.75, s_h * 0.1)
    pygame.draw.polygon(icon_surface, (255, 215, 0), [point7, point8, point9])

    # Ceremonial sash
    sash_rect = pygame.Rect(s_w * 0.1, s_h * 0.6, s_w * 0.8, s_h * 0.15)
    pygame.draw.rect(icon_surface, (25, 25, 112), sash_rect)  # Blue sash

    return icon_surface
