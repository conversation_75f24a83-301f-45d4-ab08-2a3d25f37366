"""
Tile Rotation Utilities

This module provides utilities for determining tile rotation based on neighboring tiles
and rotation snap settings.
"""

from typing import Dict, Any, List, Optional, Tuple
import pygame


def get_tile_rotation(
    tile_x: int, 
    tile_y: int, 
    tile_definition: Dict[str, Any], 
    level_tiles: List[List[str]], 
    merged_legend: Dict[str, Dict[str, Any]]
) -> float:
    """
    Determine the rotation angle for a tile based on its neighbors and rotation snap settings.
    
    Args:
        tile_x: X coordinate of the tile
        tile_y: Y coordinate of the tile
        tile_definition: The tile's definition from the legend
        level_tiles: 2D array of tile asset IDs
        merged_legend: Combined legend with tile definitions
        
    Returns:
        Rotation angle in degrees (0, 90, 180, or 270)
    """
    # Get rotation snap properties
    properties = tile_definition.get('properties', {})
    rotation_snap = properties.get('rotation_snap', 'none')
    rotation_snap_tile = properties.get('rotation_snap_tile')
    
    # If no rotation snap, return 0
    if rotation_snap == 'none' or not rotation_snap_tile:
        return 0.0
    
    # Check neighboring tiles
    neighbors = _get_neighboring_tiles(tile_x, tile_y, level_tiles, merged_legend)
    
    # Determine rotation based on snap type
    if rotation_snap == 'horizontal':
        return _calculate_horizontal_rotation(neighbors, rotation_snap_tile)
    elif rotation_snap == 'vertical':
        return _calculate_vertical_rotation(neighbors, rotation_snap_tile)
    
    return 0.0


def _get_neighboring_tiles(
    tile_x: int, 
    tile_y: int, 
    level_tiles: List[List[str]], 
    merged_legend: Dict[str, Dict[str, Any]]
) -> Dict[str, Optional[str]]:
    """
    Get the tile types of neighboring tiles.
    
    Args:
        tile_x: X coordinate of the tile
        tile_y: Y coordinate of the tile
        level_tiles: 2D array of tile asset IDs
        merged_legend: Combined legend with tile definitions
        
    Returns:
        Dictionary with neighbor directions as keys and tile types as values
    """
    neighbors = {
        'north': None,
        'south': None,
        'east': None,
        'west': None
    }
    
    # Check bounds and get neighbor tile types
    height = len(level_tiles)
    width = len(level_tiles[0]) if height > 0 else 0
    
    # North (up)
    if tile_y > 0:
        asset_id = level_tiles[tile_y - 1][tile_x]
        neighbors['north'] = _get_tile_type_from_asset_id(asset_id, merged_legend)
    
    # South (down)
    if tile_y < height - 1:
        asset_id = level_tiles[tile_y + 1][tile_x]
        neighbors['south'] = _get_tile_type_from_asset_id(asset_id, merged_legend)
    
    # East (right)
    if tile_x < width - 1:
        asset_id = level_tiles[tile_y][tile_x + 1]
        neighbors['east'] = _get_tile_type_from_asset_id(asset_id, merged_legend)
    
    # West (left)
    if tile_x > 0:
        asset_id = level_tiles[tile_y][tile_x - 1]
        neighbors['west'] = _get_tile_type_from_asset_id(asset_id, merged_legend)
    
    return neighbors


def _get_tile_type_from_asset_id(asset_id: str, merged_legend: Dict[str, Dict[str, Any]]) -> Optional[str]:
    """
    Get the tile type from an asset ID by looking it up in the merged legend.
    
    Args:
        asset_id: The asset ID to look up
        merged_legend: Combined legend with tile definitions
        
    Returns:
        The tile type, or None if not found
    """
    for symbol, definition in merged_legend.items():
        if definition.get('asset_id') == asset_id:
            return definition.get('type')
    return None


def _calculate_horizontal_rotation(neighbors: Dict[str, Optional[str]], snap_tile_type: str) -> float:
    """
    Calculate rotation for horizontal snap (roads).
    Roads should rotate 90 degrees if there are matching tiles to the left or right.
    
    Args:
        neighbors: Dictionary of neighboring tile types
        snap_tile_type: The tile type to snap to
        
    Returns:
        Rotation angle in degrees
    """
    has_left = neighbors['west'] == snap_tile_type
    has_right = neighbors['east'] == snap_tile_type
    
    # If there are horizontal neighbors, keep default orientation (0 degrees)
    # If there are no horizontal neighbors but vertical ones, rotate 90 degrees
    if has_left or has_right:
        return 0.0
    
    has_up = neighbors['north'] == snap_tile_type
    has_down = neighbors['south'] == snap_tile_type
    
    if has_up or has_down:
        return 90.0
    
    return 0.0


def _calculate_vertical_rotation(neighbors: Dict[str, Optional[str]], snap_tile_type: str) -> float:
    """
    Calculate rotation for vertical snap (fences, gates).
    Fences should rotate 90 degrees if there are matching tiles above or below.
    
    Args:
        neighbors: Dictionary of neighboring tile types
        snap_tile_type: The tile type to snap to
        
    Returns:
        Rotation angle in degrees
    """
    has_up = neighbors['north'] == snap_tile_type
    has_down = neighbors['south'] == snap_tile_type
    
    # If there are vertical neighbors, keep default orientation (0 degrees)
    # If there are no vertical neighbors but horizontal ones, rotate 90 degrees
    if has_up or has_down:
        return 0.0
    
    has_left = neighbors['west'] == snap_tile_type
    has_right = neighbors['east'] == snap_tile_type
    
    if has_left or has_right:
        return 90.0
    
    return 0.0


def rotate_surface(surface: pygame.Surface, angle: float) -> pygame.Surface:
    """
    Rotate a pygame surface by the given angle.
    
    Args:
        surface: The surface to rotate
        angle: Rotation angle in degrees
        
    Returns:
        The rotated surface
    """
    if angle == 0.0:
        return surface
    
    return pygame.transform.rotate(surface, angle)


def get_rotated_asset_cache_key(asset_id: str, size: Tuple[int, int], rotation: float) -> str:
    """
    Generate a cache key for a rotated asset.
    
    Args:
        asset_id: The base asset ID
        size: The asset size
        rotation: The rotation angle
        
    Returns:
        A unique cache key for the rotated asset
    """
    return f"{asset_id}_{size[0]}x{size[1]}_rot{rotation}"
