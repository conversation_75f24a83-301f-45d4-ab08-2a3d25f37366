"""
Level Repository Implementation

This module implements the ILevelRepository interface for loading level data.
"""

from typing import List, Dict, Any
from pathlib import Path

from src.application.interfaces import ILevelRepository, LevelLayoutData
from .map_parser import MapParser


class LevelRepository(ILevelRepository):
    """
    Concrete implementation of ILevelRepository.
    
    This class discovers and loads level data from the levels directory structure.
    """
    
    def __init__(self, levels_directory: str, base_legend_path: str):
        """
        Initialize the level repository.
        
        Args:
            levels_directory: Path to the directory containing level packages
            base_legend_path: Path to the base legend YAML file
        """
        self.levels_dir = Path(levels_directory)
        self.map_parser = MapParser(base_legend_path)
        self._level_cache: Dict[str, LevelLayoutData] = {}
    
    def load_level(self, level_id: str) -> LevelLayoutData:
        """
        Load level layout data by ID.
        
        Args:
            level_id: The ID of the level to load
            
        Returns:
            LevelLayoutData for the requested level
            
        Raises:
            FileNotFoundError: If the level directory or map file is not found
            ValueError: If the level data is invalid
        """
        # Check cache first
        if level_id in self._level_cache:
            return self._level_cache[level_id]
        
        # Find the level directory
        level_dir = self.levels_dir / level_id
        if not level_dir.exists():
            raise FileNotFoundError(f"Level directory not found: {level_dir}")
        
        # Find the map file (look for any .map file in the directory)
        map_files = list(level_dir.glob("*.map"))
        if not map_files:
            raise FileNotFoundError(f"No .map files found in level directory: {level_dir}")
        
        if len(map_files) > 1:
            # If multiple map files, try to find one that matches the level ID
            matching_files = [f for f in map_files if f.stem == level_id]
            if matching_files:
                map_file = matching_files[0]
            else:
                # Use the first one found
                map_file = map_files[0]
        else:
            map_file = map_files[0]
        
        # Load and parse the map file
        try:
            layout_data = self.map_parser.parse_map_file(str(map_file))
            
            # Cache the result
            self._level_cache[level_id] = layout_data
            
            return layout_data
            
        except Exception as e:
            raise ValueError(f"Failed to load level '{level_id}': {e}") from e
    
    def get_available_levels(self) -> List[str]:
        """
        Get a list of all available level IDs.
        
        Returns:
            List of level IDs that can be loaded
        """
        level_ids = []
        
        if not self.levels_dir.exists():
            return level_ids
        
        # Look for directories that contain .map files
        for item in self.levels_dir.iterdir():
            if item.is_dir():
                # Check if this directory contains any .map files
                map_files = list(item.glob("*.map"))
                if map_files:
                    level_ids.append(item.name)
        
        return sorted(level_ids)
    
    def get_level_info(self, level_id: str) -> Dict[str, Any]:
        """
        Get metadata information about a level without fully loading it.
        
        Args:
            level_id: The level ID to get info for
            
        Returns:
            Dictionary containing level metadata
        """
        level_dir = self.levels_dir / level_id
        
        if not level_dir.exists():
            return {"error": "Level directory not found"}
        
        info = {
            "level_id": level_id,
            "directory": str(level_dir),
            "exists": True,
        }
        
        # Check for map files
        map_files = list(level_dir.glob("*.map"))
        info["map_files"] = [f.name for f in map_files]
        info["has_map"] = len(map_files) > 0
        
        # Check for level config
        config_file = level_dir / "level_config.py"
        info["has_config"] = config_file.exists()
        
        # Check for events file
        events_file = level_dir / "events.py"
        info["has_events"] = events_file.exists()
        
        # If we can load the level, get basic stats
        if info["has_map"]:
            try:
                layout_data = self.load_level(level_id)
                info["width"] = layout_data.width
                info["height"] = layout_data.height
                info["entity_count"] = len(layout_data.entities)
                
                # Count entities by type
                entity_types = {}
                for entity in layout_data.entities:
                    entity_type = entity.get("type", "unknown")
                    entity_types[entity_type] = entity_types.get(entity_type, 0) + 1
                info["entities_by_type"] = entity_types
                
            except Exception as e:
                info["load_error"] = str(e)
        
        return info
    
    def clear_cache(self) -> None:
        """Clear the level cache."""
        self._level_cache.clear()

    def get_merged_legend_for_level(self, level_id: str) -> Dict[str, Dict[str, Any]]:
        """
        Get the merged legend for a specific level.

        Args:
            level_id: The ID of the level

        Returns:
            The merged legend (base + level overrides) for the level
        """
        # Load the level to ensure the merged legend is available
        self.load_level(level_id)
        return self.map_parser.get_last_merged_legend()
    
    def validate_level(self, level_id: str) -> List[str]:
        """
        Validate a level and return any issues found.
        
        Args:
            level_id: The level ID to validate
            
        Returns:
            List of validation issues (empty if valid)
        """
        issues = []
        
        level_dir = self.levels_dir / level_id
        if not level_dir.exists():
            issues.append(f"Level directory does not exist: {level_dir}")
            return issues
        
        # Check for map files
        map_files = list(level_dir.glob("*.map"))
        if not map_files:
            issues.append("No .map files found in level directory")
            return issues
        
        # Validate each map file
        for map_file in map_files:
            map_issues = self.map_parser.validate_map_file(str(map_file))
            issues.extend([f"{map_file.name}: {issue}" for issue in map_issues])
        
        return issues
    
    def reload_level(self, level_id: str) -> LevelLayoutData:
        """
        Force reload a level, bypassing the cache.
        
        Args:
            level_id: The level ID to reload
            
        Returns:
            Freshly loaded LevelLayoutData
        """
        # Remove from cache if present
        if level_id in self._level_cache:
            del self._level_cache[level_id]
        
        # Load fresh copy
        return self.load_level(level_id)
