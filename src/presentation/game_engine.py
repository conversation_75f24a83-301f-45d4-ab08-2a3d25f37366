"""
Game Engine - Presentation Layer

This module contains the main game engine that drives the presentation layer.
It handles the game loop, input processing, and coordinates between use cases and rendering.
"""

import pygame
import sys
import time
from typing import Optional
from pathlib import Path

from src.application import (
    MovePlayerUseCase, PlayerAttackUseCase, BuildLevelUseCase,
    InteractWithNPCUseCase, BuyFromNPCUseCase, SellToNPCUseCase,
    UpdateWanderingEntitiesUseCase, TileInteractionUseCase,
    GameStateData, IEventBus, IAssetManager, ILevelRepository,
    IAudioPlayer, ISaveGameRepository
)
from src.application.interfaces import LevelLayoutData
from src.application.animation_manager import AnimationManager
from src.infrastructure.rendering import PygameRenderer
from src.infrastructure.logging import get_logger
from src.game_core import Direction, Vector2, Position, screen_to_direction_vector
from src.game_core.entities import NPC
from src.game_core.config import get_config
from .ui import (
    InventoryUI<PERSON>ontroller, <PERSON><PERSON><PERSON><PERSON><PERSON>roller, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SaveSlotUI<PERSON><PERSON>roller, HelpUIController,
    NewGameUIController, GameState
)


class GameEngine:
    """
    Main game engine that manages the game loop and coordinates all systems.
    
    This is the presentation layer that drives the entire application.
    """
    
    def __init__(
        self,
        event_bus: IEventBus,
        asset_manager: IAssetManager,
        level_repository: ILevelRepository,
        move_player_use_case: MovePlayerUseCase,
        attack_use_case: PlayerAttackUseCase,
        build_level_use_case: BuildLevelUseCase,
        interact_npc_use_case: InteractWithNPCUseCase,
        buy_from_npc_use_case: BuyFromNPCUseCase,
        sell_to_npc_use_case: SellToNPCUseCase,
        update_wandering_entities_use_case: UpdateWanderingEntitiesUseCase,
        tile_interaction_use_case: TileInteractionUseCase,
        audio_player: IAudioPlayer,
        save_repository: ISaveGameRepository,
        quest_manager=None,
        target_fps: int = None
    ):
        """
        Initialize the game engine.
        
        Args:
            event_bus: Event bus for system communication
            asset_manager: Asset manager for loading sprites
            level_repository: Repository for loading levels
            move_player_use_case: Use case for player movement
            attack_use_case: Use case for player attacks
            build_level_use_case: Use case for building levels
            audio_player: Audio player for sound effects and music
            save_repository: Repository for saving/loading game state
            target_fps: Target frames per second
        """
        self.event_bus = event_bus
        self.asset_manager = asset_manager
        self.level_repository = level_repository
        self.move_player_use_case = move_player_use_case
        self.attack_use_case = attack_use_case
        self.build_level_use_case = build_level_use_case
        self.interact_npc_use_case = interact_npc_use_case
        self.buy_from_npc_use_case = buy_from_npc_use_case
        self.sell_to_npc_use_case = sell_to_npc_use_case
        self.update_wandering_entities_use_case = update_wandering_entities_use_case
        self.tile_interaction_use_case = tile_interaction_use_case
        self.audio_player = audio_player
        self.save_repository = save_repository
        self.quest_manager = quest_manager

        # Get configuration
        self.config = get_config()
        
        # Use configured FPS or provided value
        self.target_fps = target_fps if target_fps is not None else self.config.rendering.target_fps
        
        # Initialize logger
        self.logger = get_logger(__name__)
        
        # Initialize renderer
        self.renderer = PygameRenderer(asset_manager)

        # Initialize animation manager
        self.animation_manager = AnimationManager(event_bus)
        self.animation_manager.update_config(self.config.__dict__)

        # Initialize UI controllers with screen dimensions
        screen_width = self.config.rendering.screen_width
        screen_height = self.config.rendering.screen_height
        self.inventory_ui = InventoryUIController(event_bus, asset_manager, screen_width, screen_height)
        self.dialog_ui = DialogUIController(continue_callback=self._continue_npc_dialog)
        self.store_ui = StoreUIController(buy_from_npc_use_case, sell_to_npc_use_case)

        # Initialize new UI controllers
        self.settings_ui = SettingsUIController(save_repository)
        self.save_slot_ui = SaveSlotUIController(save_repository)
        self.help_ui = HelpUIController()
        self.new_game_ui = NewGameUIController(save_repository)

        # Set up UI callbacks
        self._setup_ui_callbacks()
        
        # Initialize UI states
        self.settings_ui.update_fullscreen_state(self.renderer.fullscreen)

        # Game state
        self.game_state: Optional[GameStateData] = None
        self.running = False
        self.paused = False  # Game pauses when UI is open
        self.clock = pygame.time.Clock()

        # Game flow state
        self.current_game_state = GameState.MENU
        self.current_save_slot: Optional[str] = None

        # Input state
        self.keys_pressed = set()
        self.mouse_pos = (0, 0)
        self.mouse_clicked = False

        # Movement timing - for footstep sounds
        self.last_move_time = 0.0

        # Track attacked monsters for AI behavior
        self.attacked_monsters_this_frame = []

        # Level event handlers
        self.current_level_event_handlers = None

    def _setup_ui_callbacks(self) -> None:
        """Set up callbacks for UI controllers."""
        # Settings UI callbacks
        self.settings_ui.on_new_game = self._handle_new_game_request
        self.settings_ui.on_load_game = self._handle_load_game_request
        self.settings_ui.on_save_game = self._handle_save_game
        self.settings_ui.on_toggle_fullscreen = self._handle_toggle_fullscreen
        self.settings_ui.on_exit_game = self._handle_exit_game

        # Save slot UI callbacks
        self.save_slot_ui.on_slot_selected = self._handle_save_slot_selected
        self.save_slot_ui.on_cancel = self._handle_save_slot_cancel

        # Help UI callbacks
        self.help_ui.on_close = self._handle_help_close

        # New game UI callbacks
        self.new_game_ui.on_new_game_created = self._handle_new_game_created
        self.new_game_ui.on_cancel = self._handle_new_game_cancel

        # Track tile interactions to prevent immediate re-triggering
        self.last_interacted_tile = None  # (x, y) tuple of the last tile that triggered an interaction

        # Subscribe to events
        self._setup_event_handlers()
    
    def run(self) -> None:
        """Main game loop."""
        self.logger.info("Starting game engine...")

        # Pygame is already initialized in main.py and renderer constructor
        # Just set the display caption
        pygame.display.set_caption("Treebeard's Revenge")

        # Show settings UI on startup if configured
        if self.config.settings_ui.show_on_startup:
            self.current_game_state = GameState.MENU
            self.settings_ui.show_settings(GameState.MENU)
        else:
            # Load initial level directly
            try:
                starting_map = self.config.game.starting_map
                self._load_level(starting_map)
                self.current_game_state = GameState.PLAYING
            except Exception as e:
                self.logger.error(f"Failed to load initial level: {e}")
                # Create a minimal test level
                self._create_test_level()
                self.current_game_state = GameState.PLAYING

        self.running = True
        self.logger.info("Entering main game loop...")
        
        while self.running:
            self.logger.debug(f"Game loop iteration. Running: {self.running}")
            dt = self.clock.tick(self.target_fps) / 1000.0  # Delta time in seconds

            # Handle events
            self._handle_events()

            # Update pause state based on UI status
            self._update_pause_state()

            # Update game logic (only if not paused)
            if not self.paused:
                self._update(dt)

            # Always update UI (even when paused)
            if self.game_state:
                self.inventory_ui.update(dt, self.game_state)

            # Update NPC UI controllers
            self.dialog_ui.update(dt)
            self.store_ui.update(dt)

            # Render
            if self.game_state:
                # Get current combined animation transform (player + weapon)
                combined_transform = self.animation_manager.get_current_combined_animation_transform()

                # Render with combined animation transform
                self.renderer.render_frame(self.game_state, combined_transform)

                # Render inventory UI on top
                self.inventory_ui.render(self.renderer.screen, self.game_state)
            else:
                # Clear screen when no game state (e.g., in menu)
                self.renderer.screen.fill((30, 35, 45))  # Dark blue-grey background

            # Render NPC UI controllers (always render, they handle their own visibility)
            self.dialog_ui.render(self.renderer.screen)
            self.store_ui.render(self.renderer.screen)

            # Render settings and menu UI controllers on top of everything
            self.settings_ui.render(self.renderer.screen)
            self.new_game_ui.render(self.renderer.screen)
            self.save_slot_ui.render(self.renderer.screen)
            self.help_ui.render(self.renderer.screen)

            # Render pause indicator if paused
            if self.paused and self.game_state:
                self._render_pause_indicator()

            # Update display after all rendering is complete
            pygame.display.flip()
        
        self.logger.info("Game engine stopped.")
    
    def stop(self) -> None:
        """Stop the game engine and clean up resources."""
        self.running = False

        # Stop all audio
        try:
            self.audio_player.stop_music()
            self.audio_player.stop_all_ambient()
        except Exception as e:
            self.logger.warning(f"Failed to stop audio: {e}")

    def _should_pause_game(self) -> bool:
        """Check if the game should be paused due to open UI."""
        return (self.inventory_ui.is_open or
                self.dialog_ui.is_dialog_visible() or
                self.store_ui.is_store_visible() or
                self.settings_ui.is_settings_visible() or
                self.save_slot_ui.is_visible() or
                self.help_ui.is_visible() or
                self.new_game_ui.is_visible())

    def _update_pause_state(self) -> None:
        """Update the game's pause state based on UI status."""
        should_pause = self._should_pause_game()

        if should_pause != self.paused:
            self.paused = should_pause
            if self.paused:
                self.logger.debug("Game paused - UI is open")
            else:
                self.logger.debug("Game unpaused - UI closed")

        # Update game state based on UI visibility
        if self.settings_ui.is_settings_visible():
            if self.current_game_state == GameState.PLAYING:
                self.current_game_state = GameState.PAUSED
        elif self.current_game_state == GameState.PAUSED and not self.paused:
            self.current_game_state = GameState.PLAYING

    def _render_pause_indicator(self) -> None:
        """Render a subtle pause indicator when the game is paused."""
        if not self.renderer or not self.renderer.screen:
            return

        # Create a semi-transparent overlay
        screen_width = self.renderer.screen.get_width()
        screen_height = self.renderer.screen.get_height()

        # Subtle darkening overlay
        overlay = pygame.Surface((screen_width, screen_height), pygame.SRCALPHA)
        overlay.fill((0, 0, 0, 30))  # Very subtle dark overlay
        self.renderer.screen.blit(overlay, (0, 0))

        # Small "PAUSED" text in corner
        font = pygame.font.Font(None, 36)
        pause_text = font.render("PAUSED", True, (255, 255, 255, 180))
        text_rect = pause_text.get_rect()
        text_rect.topright = (screen_width - 20, 20)

        # Semi-transparent background for text
        bg_rect = text_rect.inflate(20, 10)
        bg_surface = pygame.Surface(bg_rect.size, pygame.SRCALPHA)
        bg_surface.fill((0, 0, 0, 100))
        self.renderer.screen.blit(bg_surface, bg_rect)

        # Render the text
        self.renderer.screen.blit(pause_text, text_rect)
    
    def _handle_events(self) -> None:
        """Handle Pygame events and convert them to game commands."""
        self.mouse_clicked = False
        
        for event in pygame.event.get():
            self.logger.debug(f"Processing event: {event}")
            if event.type == pygame.QUIT:
                self.logger.info("pygame.QUIT event received, stopping game.")
                self.running = False

            # Handle UI events first (they can consume events) - Priority order: Settings > New Game > Save Slots > Help > Store > Dialog > Inventory
            elif event.type == pygame.KEYDOWN:
                # Check UI controllers in priority order
                if self.settings_ui.is_settings_visible():
                    if self.settings_ui.handle_event(event):
                        continue  # Event was consumed
                elif self.new_game_ui.is_visible():
                    if self.new_game_ui.handle_event(event):
                        continue  # Event was consumed
                elif self.save_slot_ui.is_visible():
                    if self.save_slot_ui.handle_event(event):
                        continue  # Event was consumed
                elif self.help_ui.is_visible():
                    if self.help_ui.handle_event(event):
                        continue  # Event was consumed
                elif self.store_ui.is_store_visible() and self.game_state:
                    if self.store_ui.handle_event(event, self.game_state):
                        continue  # Event was consumed
                elif self.dialog_ui.is_dialog_visible():
                    if self.dialog_ui.handle_event(event):
                        continue  # Event was consumed

            if event.type == pygame.KEYDOWN:
                self.keys_pressed.add(event.key)
                
                # Handle immediate key presses
                if event.key == pygame.K_ESCAPE:
                    self._handle_escape_key()
                    continue
                
                elif event.key == pygame.K_F11:
                    # Toggle fullscreen
                    self.renderer.toggle_fullscreen()
                
                elif event.key == pygame.K_r:
                    # Reload current level
                    if self.game_state and self.game_state.current_level_id:
                        self.logger.info("Reloading level...")
                        self._load_level(self.game_state.current_level_id)
                
                elif event.key == pygame.K_F5:
                    # Quick save
                    if self.game_state:
                        self.logger.info("Quick saving...")
                        self.save_repository.save_game(self.game_state, "quicksave")
                        self.audio_player.play_sound("ui_save")
                
                elif event.key == pygame.K_F9:
                    # Quick load
                    self.logger.info("Quick loading...")
                    loaded_state = self.save_repository.load_game("quicksave")
                    if loaded_state:
                        self.game_state = loaded_state
                        self.audio_player.play_sound("ui_load")
                    else:
                        self.logger.warning("No quicksave found!")
                        self.audio_player.play_sound("ui_error")

                elif event.key == pygame.K_i:
                    # Toggle inventory
                    self.inventory_ui.toggle_inventory()
                    self.logger.debug(f"Inventory toggled: {'open' if self.inventory_ui.is_open else 'closed'}")

                elif event.key == pygame.K_e:
                    # Interact with NPCs and tiles
                    if not self.paused and self.game_state:
                        self._handle_interaction()
            
            elif event.type == pygame.KEYUP:
                self.keys_pressed.discard(event.key)
            
            elif event.type == pygame.MOUSEBUTTONDOWN:
                # Handle UI mouse events first (in priority order)
                if self.settings_ui.is_settings_visible():
                    self.settings_ui.handle_event(event)
                elif self.new_game_ui.is_visible():
                    self.new_game_ui.handle_event(event)
                elif self.save_slot_ui.is_visible():
                    self.save_slot_ui.handle_event(event)
                elif self.help_ui.is_visible():
                    self.help_ui.handle_event(event)
                elif self.store_ui.is_store_visible() and self.game_state:
                    self.store_ui.handle_event(event, self.game_state)
                elif self.dialog_ui.is_dialog_visible():
                    self.dialog_ui.handle_event(event)
                elif self.inventory_ui.is_open and self.game_state:
                    self.inventory_ui.handle_mouse_down(event.pos, event.button, self.game_state)
                    # Don't register clicks for attacks when UI is open
                elif event.button == 1:  # Left click (only when UI is closed)
                    self.mouse_clicked = True

            elif event.type == pygame.MOUSEBUTTONUP:
                # Handle inventory UI mouse events
                if self.inventory_ui.is_open and self.game_state:
                    self.game_state = self.inventory_ui.handle_mouse_up(event.pos, event.button, self.game_state)

            elif event.type == pygame.MOUSEMOTION:
                self.mouse_pos = event.pos

                # Handle inventory UI mouse motion
                if self.inventory_ui.is_open and self.game_state:
                    self.inventory_ui.handle_mouse_motion(event.pos, self.game_state)
    
    def _update(self, dt: float) -> None:
        """Update game logic."""
        if not self.game_state:
            return

        # Clear attacked monsters from previous frame
        self.attacked_monsters_this_frame = []
        
        # Handle movement input (WASD) - continuous movement (only when not paused)
        if not self.paused:
            movement_direction = self._get_movement_direction()
            if movement_direction and self.game_state.player:
                # Execute movement with delta time for smooth pixel-based movement
                old_game_state = self.game_state
                self.game_state = self.move_player_use_case.execute(self.game_state, movement_direction, dt)

                # Check for level transitions after movement
                if self.game_state.player and old_game_state.player:
                    # Always check for transitions (not just when moving to different tile)
                    # This ensures we catch transitions even if the player is standing on an exit
                    self._check_level_transition()

                # Play footstep sound occasionally (not every frame)
                current_time = time.time()
                if (current_time - self.last_move_time) >= 0.3:  # Play sound every 300ms during movement
                    self.audio_player.play_sound("footstep", 0.3)
                    self.last_move_time = current_time

        # Handle attack input (mouse click) - only when not paused and not clicking on UI
        if self.mouse_clicked and self.game_state.player and not self.paused:
            # Get player position in screen coordinates
            player_world_pos = (self.game_state.player.position.x, self.game_state.player.position.y)
            player_screen_pos = self.renderer.world_to_screen(player_world_pos)

            # Calculate attack direction
            direction_vector = screen_to_direction_vector(player_screen_pos, self.mouse_pos)

            # Execute attack and get list of attacked monsters
            self.game_state, self.attacked_monsters_this_frame = self.attack_use_case.execute(self.game_state, direction_vector)

            # Play attack sound
            self.audio_player.play_sound("attack_sword", 0.7)

        # Update wandering entities (NPCs and monsters) - only when not paused
        if not self.paused:
            self.game_state = self.update_wandering_entities_use_case.execute(
                self.game_state, dt, self.config, self.attacked_monsters_this_frame
            )

        # Update animation manager - always update (even when paused for smooth transitions)
        self.animation_manager.update_animation(dt)

        # Update inventory UI
        self.inventory_ui.update(dt, self.game_state)

        # Update NPC UI controllers
        self.dialog_ui.update(dt)
        self.store_ui.update(dt)
    
    def _get_movement_direction(self) -> Optional[Direction]:
        """Get movement direction from keyboard input."""
        # Check for WASD keys
        up = pygame.K_w in self.keys_pressed
        down = pygame.K_s in self.keys_pressed  
        left = pygame.K_a in self.keys_pressed
        right = pygame.K_d in self.keys_pressed
        
        # Handle diagonal movement
        if up and right:
            return Direction.NORTHEAST
        elif up and left:
            return Direction.NORTHWEST
        elif down and right:
            return Direction.SOUTHEAST
        elif down and left:
            return Direction.SOUTHWEST
        elif up:
            return Direction.NORTH
        elif down:
            return Direction.SOUTH
        elif left:
            return Direction.WEST
        elif right:
            return Direction.EAST
        
        return None
    
    def _load_level_data(self, level_id: str, existing_game_state: Optional[GameStateData] = None) -> GameStateData:
        """
        Unified level loading function that handles all scenarios.

        Args:
            level_id: The level to load
            existing_game_state: Optional existing game state to merge with (for save game loading)

        Returns:
            GameStateData with properly loaded level and entities
        """
        try:
            self.logger.info(f"Loading level data: {level_id}")

            # Load level layout data
            layout_data = self.level_repository.load_level(level_id)

            # Apply custom tile assets from level config before building game state
            self._apply_custom_tile_assets(layout_data, level_id)

            # Build fresh game state from level data (includes NPCs from level config)
            fresh_game_state = self.build_level_use_case.execute(layout_data, level_id)

            # If we have existing game state (from save game), merge it with fresh level data
            if existing_game_state:
                self.logger.info("Merging saved game state with fresh level data")

                # Use the saved player, but update position if needed
                player = existing_game_state.player

                # Use fresh monsters and NPCs from level files (like level transitions)
                # This ensures consistent behavior and prevents stale entity states
                merged_monsters = fresh_game_state.monsters.copy()
                merged_npcs = fresh_game_state.npcs.copy()

                # Keep saved item states (items can be picked up/dropped by player)
                merged_items = existing_game_state.items.copy()

                # Create merged game state
                game_state = GameStateData(
                    player=player,
                    monsters=merged_monsters,
                    items=merged_items,
                    npcs=merged_npcs,
                    current_level_id=level_id,
                    collision_map=layout_data.collision_map,
                    level_tiles=layout_data.tiles,
                    tile_states=existing_game_state.tile_states.copy() if hasattr(existing_game_state, 'tile_states') else {},
                    quest_data=existing_game_state.quest_data.copy() if hasattr(existing_game_state, 'quest_data') and existing_game_state.quest_data else {}
                )

                # Load quest data into quest manager if available
                if self.quest_manager and hasattr(existing_game_state, 'quest_data') and existing_game_state.quest_data:
                    self.quest_manager.load_quests_data(existing_game_state.quest_data)
            else:
                # No existing state - use fresh game state
                game_state = GameStateData(
                    player=fresh_game_state.player,
                    monsters=fresh_game_state.monsters,
                    items=fresh_game_state.items,
                    npcs=fresh_game_state.npcs,
                    current_level_id=fresh_game_state.current_level_id,
                    collision_map=fresh_game_state.collision_map,
                    level_tiles=layout_data.tiles,
                    tile_states=fresh_game_state.tile_states.copy() if hasattr(fresh_game_state, 'tile_states') else {},
                    quest_data=fresh_game_state.quest_data.copy() if hasattr(fresh_game_state, 'quest_data') and fresh_game_state.quest_data else {}
                )

            self.logger.info(f"✓ Level data loaded successfully: {level_id}")
            self.logger.info(f"  - Size: {layout_data.width}x{layout_data.height}")
            self.logger.info(f"  - Entities: {len(layout_data.entities)}")
            self.logger.info(f"  - NPCs: {len(game_state.npcs)}")
            if game_state.player:
                self.logger.info(f"  - Player at: {game_state.player.position}")

            return game_state

        except Exception as e:
            self.logger.error(f"Failed to load level data '{level_id}': {e}")
            raise

    def _load_level(self, level_id: str) -> None:
        """Load a level by ID (legacy method - now uses unified loading)."""
        try:
            # Use unified loading function
            self.game_state = self._load_level_data(level_id)

            # Preload assets for this level
            self.asset_manager.preload_level_assets(level_id)

            # Load level config and start audio
            self._load_level_audio(level_id)

            # Clear tile interaction tracking when loading a new level
            # This prevents spawning on an exit tile from immediately triggering a transition
            self.last_interacted_tile = None

            # Initialize level event handlers
            self._initialize_level_event_handlers(level_id)

            # Update event handlers with current game state
            self._update_event_handlers_game_state()

        except Exception as e:
            self.logger.error(f"Failed to load level '{level_id}': {e}")
            raise
    
    def _create_test_level(self) -> None:
        """Create a minimal test level for when no levels are available."""
        self.logger.info("Creating test level...")
        
        from src.application.interfaces import LevelLayoutData
        from src.game_core import Position
        
        # Create a simple 5x5 test level
        tiles = [
            ["tile.wall.stone", "tile.wall.stone", "tile.wall.stone", "tile.wall.stone", "tile.wall.stone"],
            ["tile.wall.stone", "tile.floor.dirt", "tile.floor.dirt", "tile.floor.dirt", "tile.wall.stone"],
            ["tile.wall.stone", "tile.floor.dirt", "tile.floor.dirt", "tile.floor.dirt", "tile.wall.stone"],
            ["tile.wall.stone", "tile.floor.dirt", "tile.floor.dirt", "tile.floor.dirt", "tile.wall.stone"],
            ["tile.wall.stone", "tile.wall.stone", "tile.wall.stone", "tile.wall.stone", "tile.wall.stone"],
        ]
        
        collision_map = [
            [True, True, True, True, True],
            [True, False, False, False, True],
            [True, False, False, False, True],
            [True, False, False, False, True],
            [True, True, True, True, True],
        ]
        
        entities = [
            {
                "type": "player",
                "x": 2,
                "y": 2,
                "asset_id": "player.hero"
            },
            {
                "type": "monster",
                "id": "test_goblin",
                "name": "Test Goblin",
                "x": 3,
                "y": 3,
                "asset_id": "monster.goblin.grunt.side",
                "hp": 25,
                "strength": 6,
                "defense": 2,
                "speed": 5,
                "monster_type": "goblin",
                "experience_reward": 10
            }
        ]
        
        layout_data = LevelLayoutData(
            width=5,
            height=5,
            tiles=tiles,
            entities=entities,
            collision_map=collision_map,
            metadata={"name": "Test Level"}
        )
        
        # Build game state
        self.game_state = self.build_level_use_case.execute(layout_data, "test_level")
        
        # Store tile data for rendering
        if hasattr(self.game_state, '__dict__'):
            self.game_state.__dict__['level_tiles'] = tiles
        
        self.logger.info("✓ Test level created")
    
    def _setup_event_handlers(self) -> None:
        """Set up event handlers for game events."""

        def on_player_moved(event):
            pass  # Renderer handles this automatically

        def on_player_attacked(event):
            """Handle player attack events to trigger animations."""
            if self.game_state and self.game_state.player:
                direction_vector = event.direction_vector
                self.animation_manager.start_attack_animation(self.game_state.player, direction_vector)

        def on_entity_damaged(event):
            self.logger.info(f"Entity {event.data['target_id']} took {event.data['damage']} damage!")
            self.audio_player.play_sound("hit_damage", 0.6)

        def on_entity_defeated(event):
            self.logger.info(f"Entity {event.data['entity_id']} was defeated!")
            self.audio_player.play_sound("defeat", 0.8)

        def on_spawn_monster(event):
            """Handle monster spawning events from quests."""
            monster = event.data.get("monster")
            if monster and self.game_state:
                # Add monster to game state
                new_monsters = self.game_state.monsters.copy()
                new_monsters[monster.id] = monster

                # Update game state
                from src.application.interfaces import GameStateData
                self.game_state = GameStateData(
                    player=self.game_state.player,
                    monsters=new_monsters,
                    items=self.game_state.items,
                    npcs=self.game_state.npcs,
                    current_level_id=self.game_state.current_level_id,
                    collision_map=self.game_state.collision_map,
                    level_tiles=self.game_state.level_tiles,
                    tile_states=self.game_state.tile_states,
                    quest_data=self.game_state.quest_data
                )

                # Update event handlers with current game state
                self._update_event_handlers_game_state()

                self.logger.info(f"Spawned monster {monster.id} from quest event")

        def on_quest_reward(event):
            """Handle quest reward events."""
            if self.game_state and self.game_state.player:
                gold = event.data.get("gold", 0)
                experience = event.data.get("experience", 0)

                # Add gold to player inventory (using proper item ID "gold_coin")
                if gold > 0:
                    current_gold = self.game_state.player.inventory.get("gold_coin", 0)
                    self.game_state.player.inventory["gold_coin"] = current_gold + gold
                    self.logger.info(f"Player received {gold} gold coins")

                # Add experience to player
                if experience > 0:
                    self.game_state.player.experience += experience
                    self.logger.info(f"Player received {experience} experience")

        def on_npc_interaction(event):
            """Handle NPC interaction events for quest system."""
            npc_id = event.data.get("npc_id")
            if npc_id and self.game_state and npc_id in self.game_state.npcs:
                npc = self.game_state.npcs[npc_id]
                npc_tile_x, npc_tile_y = npc.position.to_tile_coords(128)

                # Create enhanced event with position data
                from src.game_core.events import GameEvent
                enhanced_event = GameEvent(
                    event_type="npc_interaction",
                    timestamp=event.timestamp,
                    data={
                        "npc_id": npc_id,
                        "npc_position": (npc_tile_x, npc_tile_y),
                        "npc_type": npc.npc_type
                    }
                )
                self.event_bus.publish(enhanced_event)

        # Subscribe to events
        self.event_bus.subscribe("player_moved", on_player_moved)
        self.event_bus.subscribe("player_attacked", on_player_attacked)
        self.event_bus.subscribe("entity_damaged", on_entity_damaged)
        self.event_bus.subscribe("entity_defeated", on_entity_defeated)
        self.event_bus.subscribe("spawn_monster", on_spawn_monster)
        self.event_bus.subscribe("quest_reward", on_quest_reward)

        # Handle NPC interactions for quest system (simplified - dialog handled by DialogOverrideManager)
        original_interact_npc = self.interact_npc_use_case.execute
        def enhanced_interact_npc(game_state, npc_id):
            # Use the standard interaction logic (now includes DialogOverrideManager)
            result = original_interact_npc(game_state, npc_id)

            # Emit NPC interaction event for quest system
            from src.game_core.events import GameEvent
            on_npc_interaction(GameEvent(
                event_type="npc_interaction_internal",
                timestamp=time.time(),
                data={"npc_id": npc_id}
            ))
            return result
        self.interact_npc_use_case.execute = enhanced_interact_npc
    
    def _load_level_audio(self, level_id: str) -> None:
        """Load level configuration and start audio."""
        try:
            # Import the level config dynamically
            level_module = __import__(f"src.levels.{level_id}.level_config", fromlist=["LEVEL_CONFIG"])
            level_config = getattr(level_module, "LEVEL_CONFIG", {})
            
            self.logger.info(f"Loading audio for level: {level_id}")
            
            # Stop any currently playing audio
            self.audio_player.stop_music()
            self.audio_player.stop_all_ambient()
            
            # Start background music if specified
            if "background_music" in level_config:
                music_name = level_config["background_music"]
                self.logger.info(f"Starting background music: {music_name}")
                self.audio_player.play_music(music_name, loop=True)
            
            # Start ambient sounds if specified
            if "ambient_sounds" in level_config:
                ambient_sounds = level_config["ambient_sounds"]
                if ambient_sounds:
                    self.logger.info(f"Starting ambient sounds: {ambient_sounds}")
                    for sound_name in ambient_sounds:
                        # Play each ambient sound at a moderate volume
                        self.audio_player.play_ambient(sound_name, volume=0.3)
                        
        except Exception as e:
            self.logger.warning(f"Failed to load audio for level '{level_id}': {e}")
            # Audio failure shouldn't prevent level loading
            pass

    def _initialize_level_event_handlers(self, level_id: str) -> None:
        """
        Initialize level-specific event handlers.
        
        Convention: Each level can optionally have an events.py file with a class named:
        - {LevelName}EventHandlers (e.g., TownCaledonEventHandlers for town_caledon)
        - Or a generic EventHandlers class as fallback
        
        The class should accept (event_bus, quest_manager) as constructor parameters
        and have register_handlers() and unregister_handlers() methods.
        """
        try:
            # Unregister previous level handlers
            if self.current_level_event_handlers:
                if hasattr(self.current_level_event_handlers, 'unregister_handlers'):
                    self.current_level_event_handlers.unregister_handlers()
                self.current_level_event_handlers = None

            # Try to dynamically load event handlers for this level
            try:
                # Import the level's events module
                events_module = __import__(f"src.levels.{level_id}.events", fromlist=["EventHandlers"])
                
                # Look for a class named EventHandlers or {LevelName}EventHandlers
                handler_class = None
                
                # Try the standardized class name pattern first
                level_name = ''.join(word.capitalize() for word in level_id.split('_'))
                standardized_class_name = f"{level_name}EventHandlers"
                
                if hasattr(events_module, standardized_class_name):
                    handler_class = getattr(events_module, standardized_class_name)
                elif hasattr(events_module, "EventHandlers"):
                    # Fallback to generic EventHandlers class
                    handler_class = getattr(events_module, "EventHandlers")
                
                if handler_class:
                    self.current_level_event_handlers = handler_class(
                        self.event_bus,
                        self.quest_manager,
                        self.game_state
                    )
                    self.current_level_event_handlers.register_handlers()
                    self.logger.info(f"Initialized event handlers for level: {level_id}")
                else:
                    self.logger.debug(f"No event handler class found for level: {level_id}")
                    
            except ImportError:
                # No events module found for this level - that's okay
                self.logger.debug(f"No events module found for level: {level_id}")
            except Exception as e:
                self.logger.warning(f"Error loading event handlers for level '{level_id}': {e}")

        except Exception as e:
            self.logger.warning(f"Failed to initialize event handlers for level '{level_id}': {e}")
            # Event handler failure shouldn't prevent level loading
            pass

    def _update_event_handlers_game_state(self) -> None:
        """Update the game state reference in current level event handlers."""
        if self.current_level_event_handlers and hasattr(self.current_level_event_handlers, 'set_game_state'):
            self.current_level_event_handlers.set_game_state(self.game_state)

    def _check_level_transition(self) -> None:
        """Check if the player is on an exit tile and trigger level transition."""
        if not self.game_state or not self.game_state.level_tiles or not self.game_state.player:
            return

        # Instead of just checking the center tile position, check all corners of the player's bounding box
        # This matches the collision detection logic and ensures consistent behavior
        player = self.game_state.player
        player_pos = player.position
        player_size = player.size
        width, height = player_size

        # Define the four corners of the player's bounding box (same as collision detection)
        corners = [
            player_pos,                                      # Top-left
            Position(player_pos.x + width - 1, player_pos.y),       # Top-right
            Position(player_pos.x, player_pos.y + height - 1),      # Bottom-left
            Position(player_pos.x + width - 1, player_pos.y + height - 1) # Bottom-right
        ]

        # Check each corner for transition tiles
        for corner in corners:
            tile_x, tile_y = corner.to_tile_coords(128)  # Use tile size from config

            # Check bounds
            if (tile_y < 0 or tile_y >= len(self.game_state.level_tiles) or
                tile_x < 0 or tile_x >= len(self.game_state.level_tiles[0])):
                continue

            # Get the tile asset ID at this corner position
            tile_asset_id = self.game_state.level_tiles[tile_y][tile_x]

            # Check if it's an exit tile (any exit asset)
            if tile_asset_id and tile_asset_id.startswith("tile.exit."):
                # Check if this is the same tile we last interacted with
                current_tile = (tile_x, tile_y)
                if self.last_interacted_tile == current_tile:
                    # Player is still on the same exit tile, don't re-trigger
                    return

                # We need to determine which exit number this is
                exit_number = self._get_exit_number_from_map(tile_x, tile_y)
                if exit_number and exit_number.isdigit():
                    # Mark this tile as interacted with before triggering transition
                    self.last_interacted_tile = current_tile
                    self._trigger_level_transition(exit_number)
                    return  # Exit early once we find a transition

        # If we get here, none of the player's corners are on an exit tile
        # Clear the interaction tracking to allow re-triggering when the player returns
        if self.last_interacted_tile is not None:
            self.last_interacted_tile = None

    def _trigger_level_transition(self, exit_number: str) -> None:
        """Trigger a level transition based on the exit number."""
        if not self.game_state or not self.game_state.current_level_id:
            return

        try:
            # Load the current level's configuration
            module_name = f"src.levels.{self.game_state.current_level_id}.level_config"
            level_module = __import__(module_name, fromlist=["LEVEL_CONFIG"])
            level_config = getattr(level_module, "LEVEL_CONFIG", {})

            # Check if this exit is configured
            exits = level_config.get("exits", {})
            exit_key = str(exit_number)  # Convert to string since config uses string keys
            if exit_key not in exits:
                self.logger.warning(f"Exit {exit_number} not configured for level {self.game_state.current_level_id}")
                return

            exit_config = exits[exit_key]
            target_map = exit_config.get("target_map")
            spawn_point = exit_config.get("spawn_point", "0")

            if not target_map:
                self.logger.warning(f"No target map configured for exit {exit_number}")
                return

            self.logger.info(f"Transitioning from {self.game_state.current_level_id} to {target_map} via exit {exit_number}")

            # Load the target level
            self._load_level_with_spawn_point(target_map, spawn_point)

        except Exception as e:
            self.logger.error(f"Failed to trigger level transition: {e}")

    def _get_exit_number_from_map(self, tile_x: int, tile_y: int) -> Optional[str]:
        """Get the exit number from the original map file at the given position."""
        if not self.game_state or not self.game_state.current_level_id:
            return None

        try:
            # Find the map file using absolute path from project root
            # Get the project root directory (where main.py is located)
            import os
            current_file = Path(__file__).resolve()
            project_root = current_file.parent.parent.parent  # Go up from src/presentation/game_engine.py to project root

            level_dir = project_root / "src" / "levels" / self.game_state.current_level_id

            map_files = list(level_dir.glob("*.map"))

            if not map_files:
                self.logger.warning(f"No map file found for level {self.game_state.current_level_id}")
                return None

            map_file_path = map_files[0]

            with open(map_file_path, 'r') as f:
                content = f.read()

            # Split into YAML header and ASCII body (same logic as map parser)
            if '---' in content:
                yaml_part, ascii_part = content.split('---', 1)
                grid_lines = [line.rstrip('\n\r') for line in ascii_part.strip().split('\n') if line.strip()]
            else:
                # No YAML header, treat entire content as ASCII
                grid_lines = [line.rstrip('\n\r') for line in content.strip().split('\n') if line.strip()]

            # Check if the position is within bounds and get the character
            if 0 <= tile_y < len(grid_lines) and 0 <= tile_x < len(grid_lines[tile_y]):
                char = grid_lines[tile_y][tile_x]
                # Check if it's a digit (exit number)
                if char.isdigit():
                    return char

            return None

        except Exception as e:
            self.logger.error(f"Failed to read exit number from map: {e}")
            return None

    def _load_level_with_spawn_point(self, level_id: str, spawn_point: str) -> None:
        """Load a level and spawn the player at a specific spawn point."""
        try:
            # Preserve the existing player before loading the new level
            existing_player = self.game_state.player if self.game_state else None

            # Load fresh level data without merging existing state
            # This ensures monsters, items, and NPCs are loaded from the new level
            self.game_state = self._load_level_data(level_id)

            # Preload assets for this level
            self.asset_manager.preload_level_assets(level_id)

            # Load level config and start audio
            self._load_level_audio(level_id)

            # Clear tile interaction tracking when loading a new level
            self.last_interacted_tile = None

            # Update event handlers with current game state
            self._update_event_handlers_game_state()

            # Find spawn point by reading the raw map file and looking for the spawn point character
            spawn_positions = self._find_spawn_point_positions(level_id, spawn_point)

            if spawn_positions:
                # Pick a random spawn position if multiple exist
                import random
                spawn_x, spawn_y = random.choice(spawn_positions)

                # Convert tile coordinates to pixel coordinates (centered in tile)
                pixel_x = spawn_x * 128 + 64  # 128 is tile size, 64 centers it
                pixel_y = spawn_y * 128 + 64

                # Update player position using existing player data from transition
                if existing_player:
                    from src.game_core import Position, Player
                    from src.application.interfaces import GameStateData
                    new_position = Position(pixel_x, pixel_y)

                    # Create updated player with preserved data and new position
                    updated_player = Player(
                        id=existing_player.id,
                        name=existing_player.name,
                        position=new_position,
                        asset_id=existing_player.asset_id,
                        stats=existing_player.stats,
                        size=existing_player.size,
                        level=existing_player.level,
                        experience=existing_player.experience,
                        head_equipment=existing_player.head_equipment,
                        chest_equipment=existing_player.chest_equipment,
                        legs_equipment=existing_player.legs_equipment,
                        boots_equipment=existing_player.boots_equipment,
                        main_hand_weapon=existing_player.main_hand_weapon,
                        off_hand_equipment=existing_player.off_hand_equipment,
                        inventory=existing_player.inventory,
                        inventory_max_size=existing_player.inventory_max_size
                    )

                    # Update game state with new player position
                    self.game_state = GameStateData(
                        player=updated_player,
                        monsters=self.game_state.monsters,
                        items=self.game_state.items,
                        npcs=self.game_state.npcs,
                        current_level_id=self.game_state.current_level_id,
                        collision_map=self.game_state.collision_map,
                        level_tiles=self.game_state.level_tiles
                    )

                    # Update event handlers with current game state
                    self._update_event_handlers_game_state()

                    # Clear tile interaction tracking after spawning
                    # This prevents the spawn tile from immediately triggering a transition
                    self.last_interacted_tile = (spawn_x, spawn_y)

                    self.logger.info(f"Player spawned at spawn point {spawn_point} ({spawn_x}, {spawn_y})")
            else:
                self.logger.warning(f"Spawn point {spawn_point} not found in level {level_id}")

        except Exception as e:
            self.logger.error(f"Failed to load level with spawn point: {e}")

    def _apply_custom_tile_assets(self, layout_data: LevelLayoutData, level_id: str) -> None:
        """Apply custom tile assets from level config to transition tiles."""
        try:
            self.logger.info(f"Applying custom tile assets for level '{level_id}'")

            # Load the level configuration
            try:
                level_module = __import__(f"src.levels.{level_id}.level_config", fromlist=["LEVEL_CONFIG"])
                level_config = getattr(level_module, "LEVEL_CONFIG", {})
            except ImportError:
                self.logger.info(f"No level config found for level '{level_id}', skipping custom tile assets")
                return

            # Get exits configuration
            exits = level_config.get("exits", {})
            if not exits:
                self.logger.info(f"No exits configuration found for level '{level_id}'")
                return

            self.logger.info(f"Found {len(exits)} exits in level config: {list(exits.keys())}")

            # Find all transition tiles and apply custom assets
            transition_tiles_found = 0
            for y in range(layout_data.height):
                for x in range(layout_data.width):
                    if (y < len(layout_data.tiles) and x < len(layout_data.tiles[y])):
                        tile_asset = layout_data.tiles[y][x]

                        # Check if this is a transition tile
                        if tile_asset and tile_asset.startswith("tile.exit."):
                            transition_tiles_found += 1
                            self.logger.info(f"Found transition tile at ({x}, {y}): {tile_asset}")

                            # Find the exit number for this position by checking the original map file
                            exit_number = self._get_exit_number_from_map_position(x, y, level_id)
                            self.logger.info(f"Exit number at ({x}, {y}): {exit_number}")

                            if exit_number and str(exit_number) in exits:
                                exit_config = exits[str(exit_number)]
                                custom_asset = exit_config.get("tile_asset", "exit_portal")
                                self.logger.info(f"Exit {exit_number} custom asset: {custom_asset}")

                                # Apply custom tile asset
                                if custom_asset != "exit_portal":
                                    new_asset_id = f"tile.exit.{custom_asset}"

                                    # Verify the asset exists, fallback to missing asset if not
                                    try:
                                        from src.infrastructure.assets.registry import is_asset_registered
                                        if is_asset_registered(new_asset_id):
                                            layout_data.tiles[y][x] = new_asset_id
                                            self.logger.info(f"✓ Applied custom tile asset '{new_asset_id}' to exit {exit_number} at ({x}, {y})")
                                        else:
                                            layout_data.tiles[y][x] = "tile.missing_asset"
                                            self.logger.warning(f"Custom tile asset '{new_asset_id}' not found, using missing asset placeholder")
                                    except Exception as e:
                                        self.logger.warning(f"Error checking asset '{new_asset_id}': {e}, using missing asset placeholder")
                                        layout_data.tiles[y][x] = "tile.missing_asset"
                                else:
                                    self.logger.info(f"Exit {exit_number} using default portal asset")
                            else:
                                self.logger.info(f"Exit number {exit_number} not found in config or no exit number found")

            self.logger.info(f"Custom tile asset application complete. Found {transition_tiles_found} transition tiles.")

        except Exception as e:
            self.logger.warning(f"Failed to apply custom tile assets for level '{level_id}': {e}")
            import traceback
            self.logger.warning(f"Traceback: {traceback.format_exc()}")

    def _get_exit_number_from_map_position(self, x: int, y: int, level_id: str) -> Optional[int]:
        """Get the exit number from the original map file at the given position."""
        try:
            # Use the same logic as the existing _get_exit_number_from_map method
            # but for a specific position
            import os
            from pathlib import Path

            # Find the map file using absolute path from project root
            # Get the project root directory (where main.py is located)
            current_file = Path(__file__).resolve()
            project_root = current_file.parent.parent.parent  # Go up from src/presentation/game_engine.py to project root

            level_dir = project_root / "src" / "levels" / level_id

            map_files = list(level_dir.glob("*.map"))

            if not map_files:
                self.logger.warning(f"No map file found for level {level_id}")
                return None

            map_file_path = map_files[0]

            # Read the map file and extract the ASCII part
            with open(map_file_path, 'r') as f:
                content = f.read()

            if '---' in content:
                ascii_part = content.split('---', 1)[1].strip()
                ascii_lines = ascii_part.split('\n')

                # Check the character at the given position
                if y < len(ascii_lines) and x < len(ascii_lines[y]):
                    char = ascii_lines[y][x]
                    if char.isdigit():
                        self.logger.info(f"Found exit number {char} at position ({x}, {y}) in {map_file_path}")
                        return int(char)

        except Exception as e:
            self.logger.warning(f"Failed to get exit number from map position ({x}, {y}) in level '{level_id}': {e}")

        return None

    def _find_spawn_point_positions(self, level_id: str, spawn_point: str) -> list:
        """Find all positions of a specific spawn point character in the map."""
        try:
            # Find the map file using the same logic as LevelRepository
            from pathlib import Path
            import os
            current_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            level_dir = Path(current_dir) / "src" / "levels" / level_id

            # Find any .map file in the directory (same logic as LevelRepository)
            map_files = list(level_dir.glob("*.map"))
            if not map_files:
                raise FileNotFoundError(f"No .map files found in level directory: {level_dir}")

            if len(map_files) > 1:
                # If multiple map files, try to find one that matches the level ID
                matching_files = [f for f in map_files if f.stem == level_id]
                if matching_files:
                    map_file_path = str(matching_files[0])
                else:
                    # Use the first one found
                    map_file_path = str(map_files[0])
            else:
                map_file_path = str(map_files[0])

            # Read the map file
            with open(map_file_path, 'r', encoding='utf-8') as file:
                content = file.read()

            # Split into YAML header and ASCII body (same logic as map parser)
            if '---' in content:
                yaml_part, ascii_part = content.split('---', 1)
                grid_lines = [line.rstrip('\n\r') for line in ascii_part.strip().split('\n') if line.strip()]
            else:
                # No YAML header, treat entire content as ASCII
                grid_lines = [line.rstrip('\n\r') for line in content.strip().split('\n') if line.strip()]

            # Find all positions where the spawn point character appears
            spawn_positions = []
            for y, line in enumerate(grid_lines):
                for x, char in enumerate(line):
                    if char == spawn_point:
                        spawn_positions.append((x, y))
                        self.logger.info(f"SPAWN POINT: Found spawn point '{spawn_point}' at ({x}, {y})")

            self.logger.info(f"SPAWN POINT: Found {len(spawn_positions)} positions for spawn point '{spawn_point}'")
            return spawn_positions

        except Exception as e:
            self.logger.error(f"Failed to find spawn point positions: {e}")
            return []

    def _handle_interaction(self) -> None:
        """Handle player interaction with nearby NPCs and tiles, prioritizing the closest one."""
        if not self.game_state or not self.game_state.player:
            return

        player = self.game_state.player
        player_tile_x, player_tile_y = player.position.to_tile_coords(128) # 128 is tile_size

        # Find all possible interactions and their distances
        possible_interactions = []

        # Check for NPCs in adjacent tiles (including diagonal)
        for dx in [-1, 0, 1]:
            for dy in [-1, 0, 1]:
                check_x = player_tile_x + dx
                check_y = player_tile_y + dy

                # Find NPC at this position
                for npc_id, npc in self.game_state.npcs.items():
                    npc_tile_x, npc_tile_y = npc.position.to_tile_coords(128)

                    if npc_tile_x == check_x and npc_tile_y == check_y:
                        # Calculate distance from player to NPC (in pixels for more precision)
                        distance = self._calculate_interaction_distance(player.position, npc.position)
                        possible_interactions.append({
                            'type': 'npc',
                            'id': npc_id,
                            'distance': distance,
                            'position': (check_x, check_y)
                        })

                # Find items at this position
                for item_id, item in self.game_state.items.items():
                    item_tile_x, item_tile_y = item.position.to_tile_coords(128)

                    if item_tile_x == check_x and item_tile_y == check_y:
                        # Calculate distance from player to item (in pixels for more precision)
                        distance = self._calculate_interaction_distance(player.position, item.position)
                        possible_interactions.append({
                            'type': 'item',
                            'id': item_id,
                            'distance': distance,
                            'position': (check_x, check_y)
                        })

        # Check for interactive tiles in adjacent tiles
        for dx in [-1, 0, 1]:
            for dy in [-1, 0, 1]:
                check_x = player_tile_x + dx
                check_y = player_tile_y + dy

                # Check if this tile is interactive
                if self._is_tile_interactive(check_x, check_y):
                    # Calculate distance from player to tile center
                    tile_center_pos = Position.from_tile_coords(check_x, check_y, 128)
                    distance = self._calculate_interaction_distance(player.position, tile_center_pos)
                    possible_interactions.append({
                        'type': 'tile',
                        'position': (check_x, check_y),
                        'distance': distance
                    })

        # If we found any interactions, choose the closest one
        if possible_interactions:
            # Sort by distance (closest first)
            possible_interactions.sort(key=lambda x: x['distance'])
            closest = possible_interactions[0]

            # Interact with the closest object
            if closest['type'] == 'npc':
                self._interact_with_npc(closest['id'])
            elif closest['type'] == 'item':
                self._pickup_item(closest['id'])
            elif closest['type'] == 'tile':
                tile_x, tile_y = closest['position']
                self._interact_with_tile(tile_x, tile_y)

    def _calculate_interaction_distance(self, pos1: Position, pos2: Position) -> float:
        """Calculate the distance between two positions for interaction prioritization."""
        dx = pos1.x - pos2.x
        dy = pos1.y - pos2.y
        return (dx * dx + dy * dy) ** 0.5

    def _handle_npc_interaction(self) -> None:
        """Handle player interaction with nearby NPCs."""
        if not self.game_state or not self.game_state.player:
            return

        player = self.game_state.player
        player_tile_x, player_tile_y = player.position.to_tile_coords(128) # 128 is tile_size

        # Check for NPCs in adjacent tiles (including diagonal)
        for dx in [-1, 0, 1]:
            for dy in [-1, 0, 1]:
                check_x = player_tile_x + dx
                check_y = player_tile_y + dy

                # Find NPC at this position
                for npc_id, npc in self.game_state.npcs.items():
                    npc_tile_x, npc_tile_y = npc.position.to_tile_coords(128)

                    if npc_tile_x == check_x and npc_tile_y == check_y:
                        # Found an NPC to interact with
                        self._interact_with_npc(npc_id)
                        return  # Only interact with one NPC at a time

    def _is_tile_interactive(self, tile_x: int, tile_y: int) -> bool:
        """Check if a tile at the given coordinates is interactive."""
        if not self.game_state or not self.game_state.level_tiles:
            return False

        # Check bounds
        if (tile_y < 0 or tile_y >= len(self.game_state.level_tiles) or
            tile_x < 0 or tile_x >= len(self.game_state.level_tiles[0])):
            return False

        # Check if this tile has a state (which means it's interactive)
        tile_key = f"{tile_x},{tile_y}"
        if (hasattr(self.game_state, 'tile_states') and
            tile_key in self.game_state.tile_states):
            tile_state = self.game_state.tile_states[tile_key]
            if tile_state.get('can_interact', False):
                return True

        # Get current tile asset ID
        current_asset_id = self.game_state.level_tiles[tile_y][tile_x]

        # Check if this asset ID corresponds to an interactive tile
        # We need to check the base legend for this, using the method that handles both open and closed states
        if hasattr(self.tile_interaction_use_case, 'map_parser'):
            tile_def = self.tile_interaction_use_case._get_base_tile_definition_for_interactive_tile(current_asset_id)
            return tile_def and tile_def.get('properties', {}).get('can_interact', False)

        return False

    def _interact_with_tile(self, tile_x: int, tile_y: int) -> None:
        """Interact with a tile at the given coordinates."""
        try:
            # Execute tile interaction
            self.game_state = self.tile_interaction_use_case.execute(self.game_state, tile_x, tile_y)

            # Play interaction sound
            self.audio_player.play_sound("ui_interact", 0.7)

            self.logger.debug(f"Interacted with tile at ({tile_x}, {tile_y})")

        except Exception as e:
            self.logger.error(f"Failed to interact with tile at ({tile_x}, {tile_y}): {e}")

    def _interact_with_npc(self, npc_id: str) -> None:
        """Interact with a specific NPC."""
        try:
            interaction_result = self.interact_npc_use_case.execute(self.game_state, npc_id)

            if interaction_result["interaction_type"] == "dialog":
                # Check if this is a conversation with more lines
                from src.game_core.dialog_manager import get_dialog_manager
                dialog_manager = get_dialog_manager()
                has_more = dialog_manager.is_conversation_active(npc_id)

                # Show dialog
                self.dialog_ui.show_dialog(
                    npc_name=interaction_result["npc_name"],
                    npc_type=interaction_result["npc_type"],
                    npc_id=npc_id,
                    dialog_text=interaction_result["dialog"],
                    has_more_dialog=has_more
                )
                self.audio_player.play_sound("ui_dialog", 0.5)

            elif interaction_result["interaction_type"] == "store":
                # Show store interface
                npc = self.game_state.npcs[npc_id]
                player_gold = self.game_state.player.inventory.get("gold_coin", 0)

                self.store_ui.show_store(
                    npc_id=npc_id,
                    npc_name=interaction_result["npc_name"],
                    npc_type=interaction_result["npc_type"],
                    npc_inventory=interaction_result["inventory"],
                    player_inventory=self.game_state.player.inventory,
                    player_gold=player_gold
                )

                # Set up transaction callback
                def on_transaction_complete(updated_game_state):
                    self.game_state = updated_game_state

                self.store_ui.set_transaction_callback(on_transaction_complete)
                self.audio_player.play_sound("ui_store", 0.5)

        except Exception as e:
            self.logger.error(f"Failed to interact with NPC {npc_id}: {e}")
            # Show a generic error dialog
            self.dialog_ui.show_dialog(
                npc_name="System",
                npc_type="error",
                npc_id="system",
                dialog_text="Unable to interact with this character right now.",
                has_more_dialog=False
            )

    def _continue_npc_dialog(self, npc_id: str) -> None:
        """Continue dialog with an NPC."""
        try:
            # Get the dialog manager to continue the conversation
            from src.game_core.dialog_manager import get_dialog_manager
            dialog_manager = get_dialog_manager()

            dialog_text, has_more = dialog_manager.continue_conversation(npc_id)

            if dialog_text:  # If there's more dialog to show
                npc = self.game_state.npcs[npc_id]
                self.dialog_ui.show_dialog(
                    npc_name=npc.name,
                    npc_type=npc.npc_type,
                    npc_id=npc_id,
                    dialog_text=dialog_text,
                    has_more_dialog=has_more
                )
            else:
                # Conversation ended, hide dialog
                self.dialog_ui.hide_dialog()

        except Exception as e:
            self.logger.error(f"Failed to continue dialog with NPC {npc_id}: {e}")
            self.dialog_ui.hide_dialog()

    def _pickup_item(self, item_id: str) -> None:
        """Pick up an item from the world."""
        try:
            from src.application.use_cases import PickupItemUseCase

            # Execute item pickup
            pickup_use_case = PickupItemUseCase(self.event_bus)
            old_game_state = self.game_state
            self.game_state = pickup_use_case.execute(self.game_state, item_id)

            # Check if pickup was successful (item was removed from world)
            if item_id not in self.game_state.items and item_id in old_game_state.items:
                # Play pickup sound
                self.audio_player.play_sound("pickup", 0.7)

                # Get item name for logging
                item_entity = old_game_state.items[item_id]
                self.logger.info(f"Picked up {item_entity.name} x{item_entity.stack_size}")
            else:
                # Pickup failed (probably inventory full)
                self.logger.info("Cannot pick up item - inventory may be full")

        except Exception as e:
            self.logger.error(f"Failed to pick up item '{item_id}': {e}")

    def _handle_escape_key(self) -> None:
        """Handle context-aware escape key behavior."""
        self.logger.info("Escape key pressed.")

        # Context-aware escape key handling
        if self.settings_ui.is_settings_visible():
            # In settings UI: close settings, return to game (if active) or exit (if no game)
            if self.current_game_state == GameState.PLAYING:
                self.settings_ui.hide_settings()
                self.current_game_state = GameState.PLAYING
            else:
                # No game active, exit
                self.running = False
        elif self.new_game_ui.is_visible():
            # In new game UI: close new game UI
            self.new_game_ui.hide_new_game()
        elif self.save_slot_ui.is_visible():
            # In save slots UI: close save slots
            self.save_slot_ui.hide_save_slots()
        elif self.help_ui.is_visible():
            # In help UI: close help
            self.help_ui.hide_help()
        elif self.store_ui.is_store_visible():
            # In store UI: close store
            self.store_ui.hide_store()
        elif self.dialog_ui.is_dialog_visible():
            # In dialog UI: close dialog
            self.dialog_ui.hide_dialog()
        elif self.inventory_ui.is_open:
            # In inventory UI: close inventory
            self.inventory_ui.toggle_inventory()
        else:
            # In game: pause/show settings (don't exit fullscreen when UI is open)
            if self.current_game_state == GameState.PLAYING:
                # Pause game and show settings
                self.current_game_state = GameState.PAUSED
                self.settings_ui.show_settings(GameState.PAUSED, self.current_save_slot)
            elif self.renderer.fullscreen:
                # Only exit fullscreen if no UI is open
                self.logger.info("Exiting fullscreen.")
                self.renderer.toggle_fullscreen()
            else:
                # No game active, exit
                self.running = False

    def _handle_new_game_request(self) -> None:
        """Handle new game request by showing new game UI."""
        self.logger.info("Showing new game creation UI...")
        self.new_game_ui.show_new_game()

    def _handle_new_game_created(self, slot_name: str, game_name: str) -> None:
        """Handle new game creation with slot and name."""
        self.logger.info(f"Creating new game '{game_name}' in slot '{slot_name}'...")
        try:
            # Load initial level
            starting_map = self.config.game.starting_map
            self._load_level(starting_map)

            # Set player name if we have a game state
            if self.game_state and self.game_state.player:
                self.game_state.player.name = game_name

            self.current_game_state = GameState.PLAYING
            self.current_save_slot = slot_name

            # Auto-save the new game
            if self.game_state:
                self.save_repository.save_game(self.game_state, slot_name)
                self.audio_player.play_sound("ui_save")
                self.logger.info(f"New game '{game_name}' created and saved to slot '{slot_name}'")

            self.settings_ui.hide_settings()
            self.new_game_ui.hide_new_game()

        except Exception as e:
            self.logger.error(f"Failed to create new game: {e}")
            # Create a minimal test level as fallback
            self._create_test_level()

            # Set player name if we have a game state
            if self.game_state and self.game_state.player:
                self.game_state.player.name = game_name

            self.current_game_state = GameState.PLAYING
            self.current_save_slot = slot_name

            # Auto-save the new game
            if self.game_state:
                self.save_repository.save_game(self.game_state, slot_name)
                self.audio_player.play_sound("ui_save")
                self.logger.info(f"New game '{game_name}' created (fallback) and saved to slot '{slot_name}'")

            self.settings_ui.hide_settings()
            self.new_game_ui.hide_new_game()

    def _handle_new_game_cancel(self) -> None:
        """Handle new game creation cancellation."""
        self.new_game_ui.hide_new_game()

    def _handle_load_game_request(self, slot_name: str) -> None:
        """Handle load game request by showing save slot UI."""
        self.settings_ui.hide_settings()  # Hide settings UI to avoid event conflicts
        self.save_slot_ui.show_save_slots("load", self.current_save_slot)

    def _handle_save_slot_selected(self, slot_name: str) -> None:
        """Handle save slot selection."""
        if self.save_slot_ui.slot_data.mode == "load":
            self._handle_load_game(slot_name)
        else:
            self._handle_save_game(slot_name)

    def _handle_save_slot_cancel(self) -> None:
        """Handle save slot selection cancellation."""
        self.save_slot_ui.hide_save_slots()
        # Show settings UI again when canceling save slot selection
        self.settings_ui.show_settings(self.current_game_state, self.current_save_slot)

    def _handle_load_game(self, slot_name: str) -> None:
        """Handle loading a game from a save slot."""
        self.logger.info(f"Loading game from slot '{slot_name}'...")
        try:
            loaded_state = self.save_repository.load_game(slot_name)
            if loaded_state:
                # Use unified loading to merge saved state with current level data
                # This ensures NPCs and other entities are properly loaded from level config
                self.game_state = self._load_level_data(loaded_state.current_level_id, loaded_state)

                # Set up game state
                self.current_save_slot = slot_name
                self.current_game_state = GameState.PLAYING

                # Preload assets for this level
                self.asset_manager.preload_level_assets(loaded_state.current_level_id)

                # Load level config and start audio
                self._load_level_audio(loaded_state.current_level_id)

                # Initialize level event handlers
                self._initialize_level_event_handlers(loaded_state.current_level_id)

                # Clear tile interaction tracking
                self.last_interacted_tile = None

                # Hide UI and play sound
                self.settings_ui.hide_settings()
                self.save_slot_ui.hide_save_slots()
                self.audio_player.play_sound("ui_load")
                self.logger.info(f"Game loaded successfully from slot '{slot_name}'")
            else:
                self.logger.warning(f"No save found in slot '{slot_name}'")
                self.audio_player.play_sound("ui_error")
        except Exception as e:
            self.logger.error(f"Failed to load game from slot '{slot_name}': {e}")
            self.audio_player.play_sound("ui_error")

    def _handle_save_game(self, slot_name: str) -> None:
        """Handle saving the game to a save slot."""
        if not self.game_state:
            self.logger.warning("Cannot save game - no game state available")
            self.audio_player.play_sound("ui_error")
            return

        self.logger.info(f"Saving game to slot '{slot_name}'...")
        try:
            # Update quest data in game state before saving
            if self.quest_manager:
                self.game_state.quest_data = self.quest_manager.get_all_quests_data()

            self.save_repository.save_game(self.game_state, slot_name)
            self.current_save_slot = slot_name
            self.settings_ui.hide_settings()
            self.save_slot_ui.hide_save_slots()
            self.audio_player.play_sound("ui_save")
            self.logger.info(f"Game saved successfully to slot '{slot_name}'")
        except Exception as e:
            self.logger.error(f"Failed to save game to slot '{slot_name}': {e}")
            self.audio_player.play_sound("ui_error")

    def _handle_exit_game(self) -> None:
        """Handle exit game request."""
        self.logger.info("Exiting game...")
        self.running = False

    def _handle_toggle_fullscreen(self) -> None:
        """Handle fullscreen toggle request."""
        self.logger.info("Toggling fullscreen...")
        self.renderer.toggle_fullscreen()
        
        # Update the settings UI with the new fullscreen state
        self.settings_ui.update_fullscreen_state(self.renderer.fullscreen)

    def _handle_help_close(self) -> None:
        """Handle help panel close."""
        self.help_ui.hide_help()
