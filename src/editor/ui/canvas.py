"""
Map Canvas

This module provides the main map editing canvas with zoom, pan, grid display,
and coordinate tracking functionality.
"""

import pygame
from typing import Optional, Tuple, List, Dict, Any
import math

from src.application.interfaces import IAssetManager
from src.infrastructure.logging import get_logger
from src.game_core.config import get_config
from ..map_operations import EditorMapData, MapOperations
from ..ui_config import get_ui_config, get_colors, FONT_NORMAL, FONT_SMALL


class MapCanvas:
    """
    Main map editing canvas.
    
    This component handles rendering the map, zoom/pan functionality,
    grid display, and coordinate tracking.
    """
    
    def __init__(self, asset_manager: IAssetManager, x: int, y: int, width: int, height: int, map_operations: Optional[MapOperations] = None):
        """
        Initialize the map canvas.

        Args:
            asset_manager: Asset manager for loading sprites
            x: Canvas X position
            y: Canvas Y position
            width: Canvas width
            height: Canvas height
            map_operations: Optional map operations for accessing merged legend
        """
        self.asset_manager = asset_manager
        self.x = x
        self.y = y
        self.width = width
        self.height = height
        self.map_operations = map_operations

        self.logger = get_logger(__name__)
        self.config = get_config()
        self.ui_config = get_ui_config()

        # Colors - using centralized color scheme
        self.colors = get_colors()

        # Current map data
        self.map_data: Optional[EditorMapData] = None
        
        # Camera/view state
        self.camera_x = 0.0
        self.camera_y = 0.0
        self.zoom_level = 1.0
        self.min_zoom = 0.25
        self.max_zoom = 4.0
        self.zoom_step = 0.1
        
        # Base tile size from config
        self.base_tile_size = self.config.rendering.tile_size
        
        # Grid settings - using modern blue-grey colors
        self.show_grid = True
        self.grid_color = self.colors.get('border', (70, 80, 95, 128))  # Blue-grey grid
        
        # Mouse state
        self.mouse_pos = (0, 0)
        self.mouse_world_pos = (0, 0)
        self.mouse_tile_pos = (0, 0)
        self.is_panning = False
        self.pan_start_pos = (0, 0)
        self.pan_start_camera = (0, 0)

        # Fonts - using centralized font configuration
        self.font = self.ui_config.get_font(FONT_NORMAL)
        self.font_small = self.ui_config.get_font(FONT_SMALL)
    
    def set_map_data(self, map_data: Optional[EditorMapData]) -> None:
        """Set the current map data to display."""
        self.map_data = map_data
        if map_data:
            # Center camera on the map
            self.camera_x = (map_data.layout.width * self.base_tile_size) / 2
            self.camera_y = (map_data.layout.height * self.base_tile_size) / 2
    
    def update(self, dt: float, mouse_pos: Tuple[int, int], events: List[pygame.event.Event]) -> None:
        """
        Update the canvas.
        
        Args:
            dt: Delta time
            mouse_pos: Current mouse position
            events: Pygame events for this frame
        """
        self.mouse_pos = mouse_pos
        
        # Update world and tile coordinates
        self._update_mouse_coordinates()
        
        # Handle events
        for event in events:
            self._handle_event(event)
    
    def render(self, screen: pygame.Surface) -> None:
        """
        Render the map canvas.
        
        Args:
            screen: Surface to render to
        """
        # Create canvas rect and clip to it
        canvas_rect = pygame.Rect(self.x, self.y, self.width, self.height)
        screen.set_clip(canvas_rect)
        
        try:
            # Clear background
            pygame.draw.rect(screen, self.colors['background_dark'], canvas_rect)
            
            if self.map_data:
                # Render map tiles
                self._render_map_tiles(screen)
                
                # Render grid if enabled
                if self.show_grid:
                    self._render_grid(screen)
            else:
                # No map loaded - show message
                self._render_no_map_message(screen)
            
            # Draw border
            pygame.draw.rect(screen, self.colors['border'], canvas_rect, 2)
            
        finally:
            screen.set_clip(None)
        
        # Render coordinate display (outside clipping)
        self._render_coordinate_display(screen)
    
    def get_tile_at_mouse(self) -> Optional[Tuple[int, int]]:
        """Get the tile coordinates at the current mouse position."""
        if self._is_mouse_in_canvas():
            return self.mouse_tile_pos
        return None
    
    def world_to_screen(self, world_x: float, world_y: float) -> Tuple[int, int]:
        """Convert world coordinates to screen coordinates."""
        screen_x = (world_x - self.camera_x) * self.zoom_level + self.x + self.width // 2
        screen_y = (world_y - self.camera_y) * self.zoom_level + self.y + self.height // 2
        return (int(screen_x), int(screen_y))
    
    def screen_to_world(self, screen_x: int, screen_y: int) -> Tuple[float, float]:
        """Convert screen coordinates to world coordinates."""
        world_x = (screen_x - self.x - self.width // 2) / self.zoom_level + self.camera_x
        world_y = (screen_y - self.y - self.height // 2) / self.zoom_level + self.camera_y
        return (world_x, world_y)
    
    def _handle_event(self, event: pygame.event.Event) -> None:
        """Handle a pygame event."""
        if event.type == pygame.MOUSEBUTTONDOWN:
            if self._is_mouse_in_canvas():
                if event.button == 2:  # Middle mouse button - start panning
                    self.is_panning = True
                    self.pan_start_pos = event.pos
                    self.pan_start_camera = (self.camera_x, self.camera_y)
                elif event.button == 4:  # Mouse wheel up - zoom in
                    self._zoom_at_mouse(1 + self.zoom_step)
                elif event.button == 5:  # Mouse wheel down - zoom out
                    self._zoom_at_mouse(1 - self.zoom_step)
        
        elif event.type == pygame.MOUSEBUTTONUP:
            if event.button == 2:  # Middle mouse button - stop panning
                self.is_panning = False
        
        elif event.type == pygame.MOUSEMOTION:
            if self.is_panning:
                # Update camera position based on mouse movement
                dx = event.pos[0] - self.pan_start_pos[0]
                dy = event.pos[1] - self.pan_start_pos[1]
                
                self.camera_x = self.pan_start_camera[0] - dx / self.zoom_level
                self.camera_y = self.pan_start_camera[1] - dy / self.zoom_level
        
        elif event.type == pygame.KEYDOWN:
            if event.key == pygame.K_g:  # Toggle grid
                self.show_grid = not self.show_grid
            elif event.key == pygame.K_HOME:  # Reset zoom and center
                self.zoom_level = 1.0
                if self.map_data:
                    self.camera_x = (self.map_data.layout.width * self.base_tile_size) / 2
                    self.camera_y = (self.map_data.layout.height * self.base_tile_size) / 2
    
    def _update_mouse_coordinates(self) -> None:
        """Update mouse world and tile coordinates."""
        if self._is_mouse_in_canvas():
            # Convert to world coordinates
            world_x, world_y = self.screen_to_world(self.mouse_pos[0], self.mouse_pos[1])
            self.mouse_world_pos = (world_x, world_y)
            
            # Convert to tile coordinates
            tile_size = self.base_tile_size
            tile_x = int(world_x // tile_size)
            tile_y = int(world_y // tile_size)
            self.mouse_tile_pos = (tile_x, tile_y)
        else:
            self.mouse_world_pos = (0, 0)
            self.mouse_tile_pos = (0, 0)
    
    def _is_mouse_in_canvas(self) -> bool:
        """Check if mouse is within the canvas area."""
        return (self.x <= self.mouse_pos[0] <= self.x + self.width and
                self.y <= self.mouse_pos[1] <= self.y + self.height)
    
    def _zoom_at_mouse(self, zoom_factor: float) -> None:
        """Zoom in/out at the current mouse position."""
        if not self._is_mouse_in_canvas():
            return
        
        # Calculate world position at mouse before zoom
        world_pos_before = self.screen_to_world(self.mouse_pos[0], self.mouse_pos[1])
        
        # Apply zoom
        new_zoom = self.zoom_level * zoom_factor
        self.zoom_level = max(self.min_zoom, min(self.max_zoom, new_zoom))
        
        # Calculate world position at mouse after zoom
        world_pos_after = self.screen_to_world(self.mouse_pos[0], self.mouse_pos[1])
        
        # Adjust camera to keep the same world position under the mouse
        self.camera_x += world_pos_before[0] - world_pos_after[0]
        self.camera_y += world_pos_before[1] - world_pos_after[1]

    def _render_map_tiles(self, screen: pygame.Surface) -> None:
        """Render the map tiles."""
        if not self.map_data:
            return

        tile_size = int(self.base_tile_size * self.zoom_level)

        # Calculate visible tile range
        start_x = max(0, int((self.camera_x - self.width // 2 / self.zoom_level) // self.base_tile_size) - 1)
        end_x = min(self.map_data.layout.width,
                   int((self.camera_x + self.width // 2 / self.zoom_level) // self.base_tile_size) + 2)
        start_y = max(0, int((self.camera_y - self.height // 2 / self.zoom_level) // self.base_tile_size) - 1)
        end_y = min(self.map_data.layout.height,
                   int((self.camera_y + self.height // 2 / self.zoom_level) // self.base_tile_size) + 2)

        # Render tiles
        for y in range(start_y, end_y):
            for x in range(start_x, end_x):
                if (y < len(self.map_data.layout.tiles) and
                    x < len(self.map_data.layout.tiles[y])):

                    asset_id = self.map_data.layout.tiles[y][x]
                    if asset_id:
                        world_x = x * self.base_tile_size
                        world_y = y * self.base_tile_size
                        screen_x, screen_y = self.world_to_screen(world_x, world_y)

                        try:
                            tile_surface = self.asset_manager.get_asset(asset_id, (tile_size, tile_size))
                            screen.blit(tile_surface, (screen_x, screen_y))
                        except Exception as e:
                            # Fallback: draw colored rectangle using modern colors
                            fallback_rect = pygame.Rect(screen_x, screen_y, tile_size, tile_size)
                            pygame.draw.rect(screen, self.colors.get('panel_bg', (60, 70, 85)), fallback_rect)
                            pygame.draw.rect(screen, self.colors.get('border', (80, 90, 105)), fallback_rect, 1)

        # Render entities
        for entity in self.map_data.layout.entities:
            # Entity coordinates are in tile units, convert to world coordinates
            entity_x = entity.get('x', 0)
            entity_y = entity.get('y', 0)
            world_x = entity_x * self.base_tile_size
            world_y = entity_y * self.base_tile_size
            screen_x, screen_y = self.world_to_screen(world_x, world_y)

            # Only render if visible
            if (-tile_size <= screen_x <= self.width + tile_size and
                -tile_size <= screen_y <= self.height + tile_size):

                asset_id = entity.get('asset_id')
                if asset_id:
                    try:
                        entity_surface = self.asset_manager.get_asset(asset_id, (tile_size, tile_size))
                        screen.blit(entity_surface, (screen_x, screen_y))
                    except Exception:
                        # Fallback: draw colored circle using modern colors
                        center = (screen_x + tile_size // 2, screen_y + tile_size // 2)
                        pygame.draw.circle(screen, self.colors.get('error', (255, 100, 100)), center, tile_size // 4)

    def _render_grid(self, screen: pygame.Surface) -> None:
        """Render the grid overlay."""
        if not self.map_data:
            return

        tile_size = int(self.base_tile_size * self.zoom_level)

        # Only show grid if tiles are large enough
        if tile_size < 8:
            return

        # Calculate grid line positions
        start_x = int((self.camera_x - self.width // 2 / self.zoom_level) // self.base_tile_size) * self.base_tile_size
        end_x = start_x + (self.width / self.zoom_level) + self.base_tile_size
        start_y = int((self.camera_y - self.height // 2 / self.zoom_level) // self.base_tile_size) * self.base_tile_size
        end_y = start_y + (self.height / self.zoom_level) + self.base_tile_size

        # Create a surface for the grid with alpha
        grid_surface = pygame.Surface((self.width, self.height), pygame.SRCALPHA)
        grid_color = self.colors.get('border', (70, 80, 95, 128))  # Blue-grey grid

        # Draw vertical lines
        x = start_x
        while x <= end_x:
            screen_x, _ = self.world_to_screen(x, 0)
            if 0 <= screen_x <= self.width:
                pygame.draw.line(grid_surface, grid_color,
                               (screen_x - self.x, 0), (screen_x - self.x, self.height))
            x += self.base_tile_size

        # Draw horizontal lines
        y = start_y
        while y <= end_y:
            _, screen_y = self.world_to_screen(0, y)
            if 0 <= screen_y <= self.height:
                pygame.draw.line(grid_surface, grid_color,
                               (0, screen_y - self.y), (self.width, screen_y - self.y))
            y += self.base_tile_size

        # Blit the grid surface
        screen.blit(grid_surface, (self.x, self.y))

    def _render_no_map_message(self, screen: pygame.Surface) -> None:
        """Render a message when no map is loaded."""
        message_lines = [
            "No map loaded",
            "Create a new map or open an existing one"
        ]

        for i, line in enumerate(message_lines):
            text = self.font.render(line, True, self.colors['text_primary'])
            text_rect = text.get_rect(center=(self.x + self.width // 2,
                                            self.y + self.height // 2 + i * 30))
            screen.blit(text, text_rect)

    def _render_coordinate_display(self, screen: pygame.Surface) -> None:
        """Render coordinate and zoom information."""
        if not self._is_mouse_in_canvas():
            return

        # Prepare coordinate text
        tile_x, tile_y = self.mouse_tile_pos
        world_x, world_y = self.mouse_world_pos

        coord_lines = [
            f"Tile: ({tile_x}, {tile_y})",
            f"World: ({world_x:.1f}, {world_y:.1f})",
            f"Zoom: {self.zoom_level:.2f}x"
        ]

        # Calculate background size
        text_surfaces = [self.font_small.render(line, True, self.colors['text_primary'])
                        for line in coord_lines]
        max_width = max(surface.get_width() for surface in text_surfaces)
        total_height = sum(surface.get_height() for surface in text_surfaces) + 10

        # Position in bottom-right of canvas
        bg_x = self.x + self.width - max_width - 15
        bg_y = self.y + self.height - total_height - 10

        # Draw background
        bg_rect = pygame.Rect(bg_x - 5, bg_y - 5, max_width + 10, total_height)
        bg_surface = pygame.Surface((bg_rect.width, bg_rect.height), pygame.SRCALPHA)
        bg_surface.fill(self.colors.get('tooltip_bg', (35, 40, 50, 200)))  # Blue-grey background
        screen.blit(bg_surface, bg_rect)

        # Draw text
        current_y = bg_y
        for surface in text_surfaces:
            screen.blit(surface, (bg_x, current_y))
            current_y += surface.get_height() + 2
