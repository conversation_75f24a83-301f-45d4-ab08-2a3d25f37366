"""
Map Operations Module

This module provides high-level operations for creating, loading, and saving maps
in the map editor. It acts as a bridge between the editor U<PERSON> and the MapParser.
"""

from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
from dataclasses import dataclass

from src.infrastructure.repositories import MapParser
from src.application.interfaces import LevelLayoutData
from src.infrastructure.logging import get_logger
from src.game_core.config import get_config


@dataclass
class MapMetadata:
    """Metadata for a map."""
    name: str = ""
    description: str = ""
    background_music: str = ""
    weather: str = "sunny"
    time_of_day: str = "day"


@dataclass
class EditorMapData:
    """Complete map data for the editor."""
    layout: LevelLayoutData
    legend_overrides: Dict[str, Any]
    placements: List[Dict[str, Any]]
    metadata: MapMetadata
    file_path: Optional[Path] = None
    modified: bool = False
    # Map from position (x, y) to transition number for preserving transition symbols
    transition_metadata: Dict[Tuple[int, int], int] = None
    
    def __post_init__(self):
        """Initialize transition metadata if not provided."""
        if self.transition_metadata is None:
            self.transition_metadata = {}


class MapOperations:
    """
    High-level map operations for the editor.
    
    This class provides methods for creating, loading, saving, and manipulating
    maps in a way that's convenient for the editor UI.
    """
    
    def __init__(self, map_parser: MapParser, levels_path: str):
        """
        Initialize map operations.
        
        Args:
            map_parser: The map parser instance
            levels_path: Path to the levels directory
        """
        self.map_parser = map_parser
        self.levels_path = Path(levels_path)
        self.logger = get_logger(__name__)
        self.config = get_config()
    
    def create_new_map(self, width: int, height: int, name: str = "New Map") -> EditorMapData:
        """
        Create a new empty map with the specified dimensions.
        
        Args:
            width: Map width in tiles
            height: Map height in tiles
            name: Name for the new map
            
        Returns:
            EditorMapData for the new map
        """
        # Create empty tile grid (filled with floor tiles)
        floor_asset_id = "tile.floor.dirt"  # Default floor from base legend
        tiles = [[floor_asset_id for _ in range(width)] for _ in range(height)]
        
        # Create empty collision map
        collision_map = [[False for _ in range(width)] for _ in range(height)]
        
        # Create layout data
        layout = LevelLayoutData(
            width=width,
            height=height,
            tiles=tiles,
            entities=[],
            collision_map=collision_map,
            metadata={}
        )
        
        # Create metadata
        metadata = MapMetadata(name=name)
        
        # Create editor map data
        editor_map = EditorMapData(
            layout=layout,
            legend_overrides={},
            placements=[],
            metadata=metadata,
            modified=True,
            transition_metadata={}
        )
        
        self.logger.info(f"Created new map: {width}x{height} tiles, name: {name}")
        return editor_map
    
    def load_map(self, map_file_path: str) -> EditorMapData:
        """
        Load a map from a .map file.
        
        Args:
            map_file_path: Path to the .map file
            
        Returns:
            EditorMapData for the loaded map
        """
        try:
            # Parse the map file
            layout = self.map_parser.parse_map_file(map_file_path)
            
            # Load the raw file to extract YAML header
            with open(map_file_path, 'r') as f:
                content = f.read()
            
            # Parse YAML header
            legend_overrides = {}
            placements = []
            raw_metadata = {}
            
            if '---' in content:
                yaml_part = content.split('---', 1)[0]
                if yaml_part.strip():
                    import yaml
                    yaml_data = yaml.safe_load(yaml_part) or {}
                    legend_overrides = yaml_data.get('legend', {})
                    placements = yaml_data.get('placements', [])
                    raw_metadata = yaml_data.get('metadata', {})
            
            # Convert metadata
            metadata = MapMetadata(
                name=raw_metadata.get('name', Path(map_file_path).stem),
                description=raw_metadata.get('description', ''),
                background_music=raw_metadata.get('background_music', ''),
                weather=raw_metadata.get('weather', 'sunny'),
                time_of_day=raw_metadata.get('time_of_day', 'day')
            )
            
            # Apply custom tile assets from level config (if any)
            level_id = self._extract_level_id_from_path(map_file_path)
            if level_id:
                self._apply_custom_tile_assets(layout, level_id)
            
            # Extract transition metadata from the layout
            transition_metadata = self._extract_transition_metadata_from_ascii(map_file_path, layout)
            
            # Create editor map data
            editor_map = EditorMapData(
                layout=layout,
                legend_overrides=legend_overrides,
                placements=placements,
                metadata=metadata,
                file_path=Path(map_file_path),
                modified=False,
                transition_metadata=transition_metadata
            )
            
            self.logger.info(f"Loaded map: {map_file_path}")
            return editor_map
            
        except Exception as e:
            self.logger.error(f"Failed to load map {map_file_path}: {e}")
            raise
    
    def save_map(self, editor_map: EditorMapData, file_path: Optional[str] = None) -> None:
        """
        Save a map to a .map file.
        
        Args:
            editor_map: The map data to save
            file_path: Optional path to save to (uses existing path if None)
        """
        if file_path is None:
            if editor_map.file_path is None:
                raise ValueError("No file path specified and map has no existing path")
            file_path = str(editor_map.file_path)
        
        try:
            # Convert metadata back to dict
            metadata_dict = {
                'name': editor_map.metadata.name,
                'description': editor_map.metadata.description,
                'background_music': editor_map.metadata.background_music,
                'weather': editor_map.metadata.weather,
                'time_of_day': editor_map.metadata.time_of_day
            }
            
            # Update layout metadata
            editor_map.layout.metadata = metadata_dict
            
            # Save using map parser
            self.map_parser.save_map_file(
                file_path,
                editor_map.layout,
                editor_map.legend_overrides,
                editor_map.placements,
                editor_map.transition_metadata
            )
            
            # Update editor map state
            editor_map.file_path = Path(file_path)
            editor_map.modified = False
            
            self.logger.info(f"Saved map: {file_path}")
            
        except Exception as e:
            self.logger.error(f"Failed to save map {file_path}: {e}")
            raise
    
    def get_available_assets(self) -> Dict[str, Dict[str, Any]]:
        """
        Get all available assets from the base legend.
        
        Returns:
            Dictionary mapping symbols to their definitions
        """
        return self.map_parser.base_legend.copy()
    
    def get_assets_by_category(self) -> Dict[str, List[Tuple[str, Dict[str, Any]]]]:
        """
        Get assets organized by category.

        Returns:
            Dictionary mapping categories to lists of (symbol, definition) tuples
        """
        categories = {}
        for symbol, definition in self.map_parser.base_legend.items():
            category = definition.get('category', 'unknown')
            if category not in categories:
                categories[category] = []
            categories[category].append((symbol, definition))

        return categories

    def get_merged_legend(self, editor_map: EditorMapData) -> Dict[str, Dict[str, Any]]:
        """
        Get the merged legend for the given editor map.

        Args:
            editor_map: The editor map data

        Returns:
            Dictionary mapping symbols to their merged definitions
        """
        merged_legend = self.map_parser.base_legend.copy()
        if editor_map.legend_overrides:
            merged_legend.update(editor_map.legend_overrides)
        return merged_legend
    
    def validate_map(self, editor_map: EditorMapData) -> List[str]:
        """
        Validate a map and return any issues.
        
        Args:
            editor_map: The map to validate
            
        Returns:
            List of validation issues (empty if valid)
        """
        issues = []
        
        # Check dimensions
        if editor_map.layout.width <= 0 or editor_map.layout.height <= 0:
            issues.append("Map must have positive width and height")
        
        # Check for player start position
        has_player = any(entity.get('type') == 'player' for entity in editor_map.layout.entities)
        if not has_player:
            issues.append("Map must have a player start position (@)")
        
        # Check for valid tiles
        for y, row in enumerate(editor_map.layout.tiles):
            for x, asset_id in enumerate(row):
                if asset_id and not self._is_valid_asset_id(asset_id):
                    issues.append(f"Unknown asset ID '{asset_id}' at position ({x}, {y})")
        
        return issues
    
    def _is_valid_asset_id(self, asset_id: str) -> bool:
        """Check if an asset ID is valid."""
        # Check if it exists in base legend
        for definition in self.map_parser.base_legend.values():
            if definition.get('asset_id') == asset_id:
                return True
        return False
    
    def set_transition_at_position(self, pos: Tuple[int, int], transition_number: int, 
                                  editor_map: EditorMapData) -> None:
        """
        Set transition metadata at a specific position.
        
        Args:
            pos: Position (x, y) of the transition
            transition_number: Transition number (0-9)
            editor_map: Editor map data to modify
        """
        editor_map.transition_metadata[pos] = transition_number
        editor_map.modified = True
        
    def get_transition_at_position(self, pos: Tuple[int, int], 
                                  editor_map: EditorMapData) -> Optional[int]:
        """
        Get transition number at a specific position.
        
        Args:
            pos: Position (x, y) to check
            editor_map: Editor map data to read from
            
        Returns:
            Transition number if found, None otherwise
        """
        # First check the in-memory metadata
        if pos in editor_map.transition_metadata:
            return editor_map.transition_metadata[pos]
        
        # If not found and we have a file path, try to extract from the original map file
        if editor_map.file_path:
            level_id = self._extract_level_id_from_path(str(editor_map.file_path))
            if level_id:
                x, y = pos
                transition_number = self._get_exit_number_from_map_position(x, y, level_id)
                if transition_number is not None:
                    # Store it for future reference
                    editor_map.transition_metadata[pos] = transition_number
                    return transition_number
        
        return None
    
    def remove_transition_at_position(self, pos: Tuple[int, int], 
                                     editor_map: EditorMapData) -> None:
        """
        Remove transition metadata at a specific position.
        
        Args:
            pos: Position (x, y) of the transition to remove
            editor_map: Editor map data to modify
        """
        if pos in editor_map.transition_metadata:
            del editor_map.transition_metadata[pos]
            editor_map.modified = True
            
    def _extract_transition_metadata(self, layout: LevelLayoutData) -> Dict[Tuple[int, int], int]:
        """
        Extract transition metadata from a loaded map by reading the original ASCII content.
        
        Args:
            layout: The loaded layout data
            
        Returns:
            Dictionary mapping positions to transition numbers
        """
        transition_metadata = {}
        
        # This is a simplified implementation. In practice, we would need access
        # to the original ASCII content to properly extract transition numbers.
        # For now, we'll scan the tiles and mark transitions as needing user configuration.
        for y in range(layout.height):
            for x in range(layout.width):
                if (y < len(layout.tiles) and x < len(layout.tiles[y]) and
                    layout.tiles[y][x] and layout.tiles[y][x].startswith("tile.exit.")):
                    # Default to transition 0 - this will be corrected when the user
                    # interacts with the transition or places a new one
                    transition_metadata[(x, y)] = 0
                    
        return transition_metadata
    
    def _extract_transition_metadata_from_ascii(self, map_file_path: str, layout: LevelLayoutData) -> Dict[Tuple[int, int], int]:
        """
        Extract transition metadata by reading the original ASCII content from the map file.
        
        Args:
            map_file_path: Path to the original map file
            layout: The loaded layout data
            
        Returns:
            Dictionary mapping positions to transition numbers
        """
        transition_metadata = {}
        
        try:
            with open(map_file_path, 'r') as f:
                content = f.read()
            
            # Split at the --- separator to get ASCII content
            if '---' in content:
                ascii_part = content.split('---', 1)[1].strip()
                ascii_lines = ascii_part.split('\n')
                
                # Scan ASCII content for transition symbols
                for y, line in enumerate(ascii_lines):
                    for x, char in enumerate(line):
                        if char in "0123456789":
                            # Check if this position has a transition tile in the layout (any exit tile type)
                            if (y < layout.height and x < layout.width and
                                y < len(layout.tiles) and x < len(layout.tiles[y]) and
                                layout.tiles[y][x] and layout.tiles[y][x].startswith("tile.exit.")):
                                transition_metadata[(x, y)] = int(char)
                                self.logger.info(f"Extracted transition {char} at position ({x}, {y}) from ASCII map")
                                
        except Exception as e:
            self.logger.warning(f"Could not extract transition metadata from {map_file_path}: {e}")
            # Fall back to default extraction
            return self._extract_transition_metadata(layout)
            
        return transition_metadata

    def create_level_config(self, level_name: str, level_id: str) -> None:
        """
        Create a level_config.py file for a new level.

        Args:
            level_name: Human-readable name of the level
            level_id: Directory name/ID of the level
        """
        level_dir = self.levels_path / level_id
        config_file = level_dir / "level_config.py"

        # Create directory if it doesn't exist
        level_dir.mkdir(exist_ok=True)

        # Generate level config content
        config_content = self._generate_level_config_content(level_name, level_id)

        try:
            with open(config_file, 'w') as f:
                f.write(config_content)
            self.logger.info(f"Created level config: {config_file}")
        except Exception as e:
            self.logger.error(f"Failed to create level config {config_file}: {e}")
            raise

    def update_level_config_transitions(self, level_id: str, transitions: Dict[str, Dict[str, str]]) -> None:
        """
        Update the transitions in a level's config file.

        Args:
            level_id: Directory name/ID of the level
            transitions: Dictionary mapping exit numbers to transition configs
        """
        level_dir = self.levels_path / level_id
        config_file = level_dir / "level_config.py"

        if not config_file.exists():
            # Create a basic config file first
            self.create_level_config(level_id.replace('_', ' ').title(), level_id)

        try:
            # Read existing config
            with open(config_file, 'r') as f:
                content = f.read()

            # Update the exits section
            updated_content = self._update_exits_in_config(content, transitions)

            # Write back to file
            with open(config_file, 'w') as f:
                f.write(updated_content)

            self.logger.info(f"Updated transitions in level config: {config_file}")

        except Exception as e:
            self.logger.error(f"Failed to update level config {config_file}: {e}")
            raise

    def get_level_config_transitions(self, level_id: str) -> Dict[str, Dict[str, str]]:
        """
        Get the current transitions from a level's config file.

        Args:
            level_id: Directory name/ID of the level

        Returns:
            Dictionary mapping exit numbers to transition configs
        """
        level_dir = self.levels_path / level_id
        config_file = level_dir / "level_config.py"

        if not config_file.exists():
            return {}

        try:
            # Import the level config dynamically
            import importlib.util
            spec = importlib.util.spec_from_file_location("level_config", config_file)
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)

            level_config = getattr(module, "LEVEL_CONFIG", {})
            return level_config.get("exits", {})

        except Exception as e:
            self.logger.error(f"Failed to read level config {config_file}: {e}")
            return {}

    def _generate_level_config_content(self, level_name: str, level_id: str) -> str:
        """Generate the content for a new level_config.py file."""
        return f'''"""
{level_name} Level Configuration

This module contains level-specific configuration and metadata.
"""

from typing import Dict, Any

LEVEL_CONFIG: Dict[str, Any] = {{
    "id": "{level_id}",
    "name": "{level_name}",
    "description": "A level created with the map editor",

    # Audio settings
    "background_music": "default_theme",
    "ambient_sounds": [],

    # Visual settings
    "weather": "sunny",
    "time_of_day": "day",
    "fog_of_war": False,

    # Gameplay settings
    "allow_save": True,
    "respawn_monsters": False,
    "difficulty_modifier": 1.0,

    # Connected levels
    "exits": {{
    }}
}}
'''

    def _update_exits_in_config(self, content: str, transitions: Dict[str, Dict[str, str]]) -> str:
        """Update the exits section in a level config file content."""
        import re

        # Generate the new exits section
        exits_lines = []
        exits_lines.append('    "exits": {')

        for exit_num, config in sorted(transitions.items()):
            target_map = config.get('target_map', '')
            spawn_point = config.get('spawn_point', '0')
            tile_asset = config.get('tile_asset', 'exit_portal')

            if target_map:  # Only add if target map is specified
                exits_lines.append(f'        "{exit_num}": {{')
                exits_lines.append(f'            "target_map": "{target_map}",')
                exits_lines.append(f'            "spawn_point": "{spawn_point}",')
                exits_lines.append(f'            "tile_asset": "{tile_asset}"')
                exits_lines.append('        },')

        # Remove trailing comma from last entry if present
        if exits_lines and exits_lines[-1].endswith(','):
            exits_lines[-1] = exits_lines[-1][:-1]

        exits_lines.append('    }')
        new_exits_section = '\n'.join(exits_lines)

        # Find the start of the exits section
        match = re.search(r'"exits"\s*:\s*{', content)
        if not match:
            # If exits section not found, add it before the last closing brace
            pattern = r'(\s*)(\}$\s*$)'
            replacement = f'\n    # Connected levels\n{new_exits_section}\n\1\2'
            return re.sub(pattern, replacement, content, flags=re.MULTILINE)

        start_index = match.start()
        
        # Find the matching closing brace
        open_braces = 0
        end_index = -1
        for i, char in enumerate(content[start_index:]):
            if char == '{':
                open_braces += 1
            elif char == '}':
                open_braces -= 1
                if open_braces == 0:
                    end_index = start_index + i + 1
                    break
        
        if end_index == -1:
            # This case should ideally not be reached if the config file is valid
            return content 

        # Replace the old exits section with the new one
        return content[:start_index] + new_exits_section + content[end_index:]

    def _extract_level_id_from_path(self, map_file_path: str) -> Optional[str]:
        """
        Extract the level ID from a map file path.
        
        Args:
            map_file_path: Path to the .map file
            
        Returns:
            Level ID if found, None otherwise
        """
        try:
            path = Path(map_file_path)
            # Map files are typically in src/levels/{level_id}/{map_name}.map
            if path.parent.parent.name == "levels":
                return path.parent.name
            return None
        except Exception:
            return None
    
    def _apply_custom_tile_assets(self, layout_data: LevelLayoutData, level_id: str) -> None:
        """
        Apply custom tile assets from level config to transition tiles.
        
        This method mimics the functionality in GameEngine._apply_custom_tile_assets()
        to ensure the editor shows the same custom assets as the game.
        
        Args:
            layout_data: The layout data to modify
            level_id: The level ID to get configuration for
        """
        try:
            self.logger.info(f"Applying custom tile assets for level '{level_id}' in editor")

            # Load the level configuration
            try:
                level_module = __import__(f"src.levels.{level_id}.level_config", fromlist=["LEVEL_CONFIG"])
                level_config = getattr(level_module, "LEVEL_CONFIG", {})
            except ImportError:
                self.logger.info(f"No level config found for level '{level_id}', skipping custom tile assets")
                return

            # Get exits configuration
            exits = level_config.get("exits", {})
            if not exits:
                self.logger.info(f"No exits configuration found for level '{level_id}'")
                return

            self.logger.info(f"Found {len(exits)} exits in level config: {list(exits.keys())}")

            # Find all transition tiles and apply custom assets
            transition_tiles_found = 0
            for y in range(layout_data.height):
                for x in range(layout_data.width):
                    if (y < len(layout_data.tiles) and x < len(layout_data.tiles[y])):
                        tile_asset = layout_data.tiles[y][x]

                        # Check if this is a transition tile
                        if tile_asset and tile_asset.startswith("tile.exit."):
                            transition_tiles_found += 1
                            self.logger.info(f"Found transition tile at ({x}, {y}): {tile_asset}")

                            # Find the exit number for this position by checking the original map file
                            exit_number = self._get_exit_number_from_map_position(x, y, level_id)
                            self.logger.info(f"Exit number at ({x}, {y}): {exit_number}")

                            if exit_number and str(exit_number) in exits:
                                exit_config = exits[str(exit_number)]
                                custom_asset = exit_config.get("tile_asset", "exit_portal")
                                self.logger.info(f"Exit {exit_number} custom asset: {custom_asset}")

                                # Apply custom tile asset
                                if custom_asset != "exit_portal":
                                    new_asset_id = f"tile.exit.{custom_asset}"

                                    # Verify the asset exists, fallback to missing asset if not
                                    try:
                                        from src.infrastructure.assets.registry import is_asset_registered
                                        if is_asset_registered(new_asset_id):
                                            layout_data.tiles[y][x] = new_asset_id
                                            self.logger.info(f"✓ Applied custom tile asset '{new_asset_id}' to exit {exit_number} at ({x}, {y})")
                                        else:
                                            layout_data.tiles[y][x] = "tile.missing_asset"
                                            self.logger.warning(f"Custom tile asset '{new_asset_id}' not found, using missing asset placeholder")
                                    except Exception as e:
                                        self.logger.warning(f"Error checking asset '{new_asset_id}': {e}, using missing asset placeholder")
                                        layout_data.tiles[y][x] = "tile.missing_asset"
                                else:
                                    self.logger.info(f"Exit {exit_number} using default portal asset")
                            else:
                                self.logger.info(f"Exit number {exit_number} not found in config or no exit number found")

            self.logger.info(f"Custom tile asset application complete. Found {transition_tiles_found} transition tiles.")

        except Exception as e:
            self.logger.warning(f"Failed to apply custom tile assets for level '{level_id}': {e}")
            import traceback
            self.logger.warning(f"Traceback: {traceback.format_exc()}")

    def _get_exit_number_from_map_position(self, x: int, y: int, level_id: str) -> Optional[int]:
        """
        Get the exit number from the original map file at the given position.
        
        This method mimics the functionality in GameEngine._get_exit_number_from_map_position()
        
        Args:
            x: X position in the map
            y: Y position in the map
            level_id: Level ID to get map file for
            
        Returns:
            Exit number if found, None otherwise
        """
        try:
            # Find the map file using absolute path from project root
            # Get the project root directory (where main.py is located)
            current_file = Path(__file__).resolve()
            project_root = current_file.parent.parent.parent  # Go up from src/editor/map_operations.py to project root

            level_dir = project_root / "src" / "levels" / level_id

            map_files = list(level_dir.glob("*.map"))

            if not map_files:
                self.logger.warning(f"No map file found for level {level_id}")
                return None

            map_file_path = map_files[0]

            # Read the map file and extract the ASCII part
            with open(map_file_path, 'r') as f:
                content = f.read()

            if '---' in content:
                ascii_part = content.split('---', 1)[1].strip()
                ascii_lines = ascii_part.split('\n')

                # Check the character at the given position
                if y < len(ascii_lines) and x < len(ascii_lines[y]):
                    char = ascii_lines[y][x]
                    if char.isdigit():
                        self.logger.info(f"Found exit number {char} at position ({x}, {y}) in {map_file_path}")
                        return int(char)

        except Exception as e:
            self.logger.warning(f"Failed to get exit number from map position ({x}, {y}) in level '{level_id}': {e}")

        return None
