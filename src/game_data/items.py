"""
Item Definitions

This file contains all item data definitions as pure data.
NO PYGAME IMPORTS - this is framework-agnostic game data.
"""

from pydantic import BaseModel
from typing import Dict, Optional, Any
from enum import Enum


class ItemType(Enum):
    """Types of items in the game."""
    WEAPON = "weapon"
    ARMOR = "armor"
    CONSUMABLE = "consumable"
    TREASURE = "treasure"
    QUEST = "quest"


class ItemDefinition(BaseModel):
    """Definition for an item type."""
    id: str
    name: str
    asset_id: str  # Links to asset generation function
    item_type: ItemType
    stackable: bool = False
    max_stack_size: int = 1
    value: int = 0  # Gold value
    description: Optional[str] = None
    properties: Dict[str, Any] = {}


# Item definitions - the game's item database
ITEMS: Dict[str, ItemDefinition] = {
    # Weapons
    "rusty_sword": ItemDefinition(
        id="rusty_sword",
        name="Rusty Sword",
        asset_id="item.weapon.sword.rusty",
        item_type=ItemType.WEAPON,
        value=10,
        description="An old, worn sword. Better than bare hands, barely.",
        properties={
            "damage": 3,
            "attack_speed": 1.0,
            "range": 1.2,  # tiles
            "damage_arc": 60,  # degrees
            "durability": 50,
            "weapon_type": "sword",
            "slot": "main_hand",
            "visual_style": "straight_sword",
            "color_primary": (139, 69, 19),  # Rusty brown
            "color_secondary": (101, 67, 33)  # Dark rust
        }
    ),

    "iron_sword": ItemDefinition(
        id="iron_sword",
        name="Iron Sword",
        asset_id="item.weapon.sword.iron",
        item_type=ItemType.WEAPON,
        value=50,
        description="A well-forged iron blade with good balance.",
        properties={
            "damage": 8,
            "attack_speed": 1.1,
            "range": 1.3,  # tiles
            "damage_arc": 60,  # degrees
            "durability": 100,
            "weapon_type": "sword",
            "slot": "main_hand",
            "visual_style": "straight_sword",
            "color_primary": (192, 192, 192),  # Silver
            "color_secondary": (120, 120, 120)  # Gray
        }
    ),
    
    "wooden_bow": ItemDefinition(
        id="wooden_bow",
        name="Wooden Bow",
        asset_id="item.weapon.bow.wooden",
        item_type=ItemType.WEAPON,
        value=30,
        description="A simple bow carved from seasoned wood.",
        properties={
            "damage": 5,
            "attack_speed": 0.8,
            "range": 8,  # tiles - ranged weapon
            "damage_arc": 10,  # degrees - narrow for ranged
            "weapon_type": "bow",
            "ammo_type": "arrow",
            "slot": "main_hand",
            "visual_style": "longbow",
            "color_primary": (160, 82, 45),  # Saddle brown
            "color_secondary": (139, 69, 19)  # Brown
        }
    ),
    
    # Armor
    "leather_armor": ItemDefinition(
        id="leather_armor",
        name="Leather Armor",
        asset_id="item.armor.leather.chest",
        item_type=ItemType.ARMOR,
        value=25,
        description="Lightweight armor made from cured leather.",
        properties={
            "defense_bonus": 3,
            "armor_type": "light",
            "slot": "chest",
            "visual_style": "leather_vest",
            "color_primary": (139, 69, 19),  # Brown leather
            "color_secondary": (101, 67, 33)  # Dark brown
        }
    ),

    "iron_helmet": ItemDefinition(
        id="iron_helmet",
        name="Iron Helmet",
        asset_id="item.armor.iron.helmet",
        item_type=ItemType.ARMOR,
        value=40,
        description="A sturdy iron helmet that protects the head.",
        properties={
            "defense_bonus": 5,
            "armor_type": "heavy",
            "slot": "head",
            "visual_style": "full_helmet",
            "color_primary": (120, 120, 120),  # Iron gray
            "color_secondary": (80, 80, 80)  # Dark gray
        }
    ),

    "iron_breastplate": ItemDefinition(
        id="iron_breastplate",
        name="Iron Breastplate",
        asset_id="item.armor.iron.chest",
        item_type=ItemType.ARMOR,
        value=60,
        description="A heavy iron breastplate that provides excellent chest protection.",
        properties={
            "defense_bonus": 8,
            "armor_type": "heavy",
            "slot": "chest",
            "visual_style": "plate_chest",
            "color_primary": (120, 120, 120),  # Iron gray
            "color_secondary": (80, 80, 80)  # Dark gray
        }
    ),

    # Additional Equipment
    "leather_cap": ItemDefinition(
        id="leather_cap",
        name="Leather Cap",
        asset_id="item.armor.leather.head",
        item_type=ItemType.ARMOR,
        value=15,
        description="A simple leather cap that provides basic head protection.",
        properties={
            "defense_bonus": 2,
            "armor_type": "light",
            "slot": "head",
            "visual_style": "light_cap",
            "color_primary": (139, 69, 19),  # Brown
            "color_secondary": (101, 67, 33)  # Dark brown
        }
    ),

    "iron_greaves": ItemDefinition(
        id="iron_greaves",
        name="Iron Greaves",
        asset_id="item.armor.iron.legs",
        item_type=ItemType.ARMOR,
        value=35,
        description="Heavy iron leg armor that provides excellent protection.",
        properties={
            "defense_bonus": 4,
            "armor_type": "heavy",
            "slot": "legs",
            "visual_style": "plate_legs",
            "color_primary": (120, 120, 120),  # Iron gray
            "color_secondary": (80, 80, 80)  # Dark gray
        }
    ),

    "iron_boots": ItemDefinition(
        id="iron_boots",
        name="Iron Boots",
        asset_id="item.armor.iron.boots",
        item_type=ItemType.ARMOR,
        value=30,
        description="Heavy iron boots that provide excellent foot protection.",
        properties={
            "defense_bonus": 3,
            "armor_type": "heavy",
            "slot": "boots",
            "visual_style": "plate_boots",
            "color_primary": (120, 120, 120),  # Iron gray
            "color_secondary": (80, 80, 80)  # Dark gray
        }
    ),

    "leather_boots": ItemDefinition(
        id="leather_boots",
        name="Leather Boots",
        asset_id="item.armor.leather.boots",
        item_type=ItemType.ARMOR,
        value=20,
        description="Sturdy leather boots that protect your feet.",
        properties={
            "defense_bonus": 2,
            "armor_type": "light",
            "slot": "boots",
            "visual_style": "simple_boots",
            "color_primary": (101, 67, 33),  # Dark brown
            "color_secondary": (70, 50, 30)  # Very dark brown
        }
    ),

    "leather_pants": ItemDefinition(
        id="leather_pants",
        name="Leather Pants",
        asset_id="item.armor.leather.legs",
        item_type=ItemType.ARMOR,
        value=30,
        description="Flexible leather pants that provide good protection for your legs.",
        properties={
            "defense_bonus": 3,
            "armor_type": "light",
            "slot": "legs",
            "visual_style": "leather_pants",
            "color_primary": (139, 69, 19),  # Brown leather
            "color_secondary": (101, 67, 33)  # Dark brown
        }
    ),

    "wooden_shield": ItemDefinition(
        id="wooden_shield",
        name="Wooden Shield",
        asset_id="item.armor.shield.wooden",
        item_type=ItemType.ARMOR,
        value=25,
        description="A basic wooden shield that can block incoming attacks.",
        properties={
            "defense_bonus": 3,
            "armor_type": "shield",
            "slot": "off_hand_equipment",
            "visual_style": "round_shield",
            "color_primary": (139, 69, 19),  # Brown
            "color_secondary": (160, 82, 45),  # Saddle brown
            "shield_type": "small"
        }
    ),

    "iron_shield": ItemDefinition(
        id="iron_shield",
        name="Iron Shield",
        asset_id="item.armor.shield.iron",
        item_type=ItemType.ARMOR,
        value=60,
        description="A heavy iron shield that provides excellent protection.",
        properties={
            "defense_bonus": 6,
            "armor_type": "shield",
            "slot": "off_hand_equipment",
            "visual_style": "kite_shield",
            "color_primary": (120, 120, 120),  # Iron gray
            "color_secondary": (80, 80, 80),  # Dark gray
            "shield_type": "medium"
        }
    ),

    # Consumables
    "health_potion": ItemDefinition(
        id="health_potion",
        name="Health Potion",
        asset_id="item.consumable.potion.health",
        item_type=ItemType.CONSUMABLE,
        stackable=True,
        max_stack_size=10,
        value=15,
        description="A red potion that restores health when consumed.",
        properties={
            "heal_amount": 50,
            "effect_type": "instant_heal"
        }
    ),
    
    "mana_potion": ItemDefinition(
        id="mana_potion",
        name="Mana Potion", 
        asset_id="item.consumable.potion.mana",
        item_type=ItemType.CONSUMABLE,
        stackable=True,
        max_stack_size=10,
        value=20,
        description="A blue potion that restores magical energy.",
        properties={
            "mana_amount": 30,
            "effect_type": "instant_mana"
        }
    ),
    
    "bread": ItemDefinition(
        id="bread",
        name="Bread",
        asset_id="item.consumable.food.bread",
        item_type=ItemType.CONSUMABLE,
        stackable=True,
        max_stack_size=20,
        value=2,
        description="A simple loaf of bread. Restores a small amount of health.",
        properties={
            "heal_amount": 10,
            "effect_type": "instant_heal"
        }
    ),

    "ale": ItemDefinition(
        id="ale",
        name="Ale",
        asset_id="icon.ale",
        item_type=ItemType.CONSUMABLE,
        stackable=True,
        max_stack_size=10,
        value=3,
        description="A frothy mug of ale. Restores a small amount of health and spirits.",
        properties={
            "heal_amount": 5,
            "effect_type": "instant_heal"
        }
    ),

    "cheese": ItemDefinition(
        id="cheese",
        name="Cheese",
        asset_id="icon.cheese",
        item_type=ItemType.CONSUMABLE,
        stackable=True,
        max_stack_size=15,
        value=4,
        description="A wheel of aged cheese. Nutritious and filling.",
        properties={
            "heal_amount": 15,
            "effect_type": "instant_heal"
        }
    ),

    "room_key": ItemDefinition(
        id="room_key",
        name="Room Key",
        asset_id="icon.room_key",
        item_type=ItemType.QUEST,
        stackable=False,
        max_stack_size=1,
        value=10,
        description="A key to a comfortable room at the inn.",
        properties={
            "effect_type": "rest_bonus"
        }
    ),

    "hot_meal": ItemDefinition(
        id="hot_meal",
        name="Hot Meal",
        asset_id="icon.hot_meal",
        item_type=ItemType.CONSUMABLE,
        stackable=True,
        max_stack_size=5,
        value=8,
        description="A hearty, hot meal. Restores significant health.",
        properties={
            "heal_amount": 30,
            "effect_type": "instant_heal"
        }
    ),
    
    # Treasure
    "gold_coin": ItemDefinition(
        id="gold_coin",
        name="Gold Coin",
        asset_id="item.treasure.coin.gold",
        item_type=ItemType.TREASURE,
        stackable=True,
        max_stack_size=999,
        value=1,
        description="Standard currency of the realm.",
        properties={}
    ),

    # Loot items
    "meat": ItemDefinition(
        id="meat",
        name="Raw Meat",
        asset_id="item.consumable.meat.raw",
        item_type=ItemType.CONSUMABLE,
        stackable=True,
        max_stack_size=50,
        value=3,
        description="Fresh meat from a slain creature. Can be cooked or eaten raw.",
        properties={
            "heal_amount": 8,
            "effect_type": "instant_heal"
        }
    ),

    "leather": ItemDefinition(
        id="leather",
        name="Leather",
        asset_id="item.material.leather.raw",
        item_type=ItemType.TREASURE,
        stackable=True,
        max_stack_size=100,
        value=5,
        description="Raw leather hide. Useful for crafting armor and equipment.",
        properties={
            "material_type": "leather",
            "crafting_component": True
        }
    ),

    "spider_sac": ItemDefinition(
        id="spider_sac",
        name="Spider Sac",
        asset_id="item.material.spider_sac.silk",
        item_type=ItemType.TREASURE,
        stackable=True,
        max_stack_size=50,
        value=8,
        description="A silk-filled sac from a forest spider. Valuable for making fine tapestries and cloth.",
        properties={
            "material_type": "silk",
            "crafting_component": True,
            "quest_item": True
        }
    ),

    "ruby_gem": ItemDefinition(
        id="ruby_gem",
        name="Ruby Gem",
        asset_id="item.treasure.gem.ruby",
        item_type=ItemType.TREASURE,
        stackable=True,
        max_stack_size=50,
        value=25,
        description="A precious ruby gem. Valuable for trading.",
        properties={}
    ),
    
    "ruby_gem": ItemDefinition(
        id="ruby_gem",
        name="Ruby Gem",
        asset_id="item.treasure.gem.ruby",
        item_type=ItemType.TREASURE,
        stackable=True,
        max_stack_size=50,
        value=100,
        description="A precious ruby that gleams with inner fire.",
        properties={}
    ),
    
    # Quest Items
    "ancient_key": ItemDefinition(
        id="ancient_key",
        name="Ancient Key",
        asset_id="item.quest.key.ancient",
        item_type=ItemType.QUEST,
        value=0,
        description="An ornate key covered in mysterious runes.",
        properties={
            "opens": "ancient_door",
            "quest_item": True
        }
    ),

    # Basic starting clothing (no defense bonus)
    "cloth_shirt": ItemDefinition(
        id="cloth_shirt",
        name="Cloth Shirt",
        asset_id="item.armor.cloth.chest",
        item_type=ItemType.ARMOR,
        value=1,
        description="A simple cloth shirt. Provides modesty but no protection.",
        properties={
            "defense_bonus": 0,
            "armor_type": "clothing",
            "slot": "chest",
            "visual_style": "simple_shirt",
            "color_primary": (240, 240, 240),  # Light gray/white
            "color_secondary": (200, 200, 200)
        }
    ),

    "cloth_pants": ItemDefinition(
        id="cloth_pants",
        name="Cloth Pants",
        asset_id="item.armor.cloth.legs",
        item_type=ItemType.ARMOR,
        value=1,
        description="Basic cloth trousers. Better than nothing.",
        properties={
            "defense_bonus": 0,
            "armor_type": "clothing",
            "slot": "legs",
            "visual_style": "simple_pants",
            "color_primary": (139, 69, 19),  # Brown
            "color_secondary": (101, 67, 33)
        }
    ),

    "cloth_cap": ItemDefinition(
        id="cloth_cap",
        name="Cloth Cap",
        asset_id="item.armor.cloth.head",
        item_type=ItemType.ARMOR,
        value=1,
        description="A simple cloth cap to keep the sun out of your eyes.",
        properties={
            "defense_bonus": 0,
            "armor_type": "clothing",
            "slot": "head",
            "visual_style": "simple_cap",
            "color_primary": (139, 69, 19),  # Brown
            "color_secondary": (101, 67, 33)
        }
    ),

    "simple_shoes": ItemDefinition(
        id="simple_shoes",
        name="Simple Shoes",
        asset_id="item.armor.cloth.boots",
        item_type=ItemType.ARMOR,
        value=2,
        description="Basic leather shoes. They'll protect your feet from rocks.",
        properties={
            "defense_bonus": 0,
            "armor_type": "clothing",
            "slot": "boots",
            "visual_style": "simple_shoes",
            "color_primary": (101, 67, 33),  # Dark brown
            "color_secondary": (70, 50, 30)
        }
    ),

    # Armourer Items



    # Weaponsmith Items

    "steel_dagger": ItemDefinition(
        id="steel_dagger",
        name="Steel Dagger",
        asset_id="icon.steel_dagger",
        item_type=ItemType.WEAPON,
        value=20,
        description="A sharp steel dagger perfect for quick strikes.",
        properties={
            "damage": 4,
            "attack_speed": 1.5,
            "range": 1.0,  # tiles - short range
            "damage_arc": 45,  # degrees - narrow arc
            "durability": 80,
            "weapon_type": "dagger",
            "slot": "main_hand_weapon",
            "visual_style": "dagger",
            "color_primary": (180, 180, 180),  # Steel grey
            "color_secondary": (139, 69, 19)  # Leather handle
        }
    ),

    "battle_axe": ItemDefinition(
        id="battle_axe",
        name="Battle Axe",
        asset_id="icon.battle_axe",
        item_type=ItemType.WEAPON,
        value=40,
        description="A heavy battle axe that deals devastating damage.",
        properties={
            "damage": 8,
            "attack_speed": 0.7,
            "range": 1.4,  # tiles - good reach
            "damage_arc": 90,  # degrees - wide sweeping arc
            "durability": 90,
            "weapon_type": "axe",
            "slot": "main_hand_weapon",
            "visual_style": "battle_axe",
            "color_primary": (120, 120, 120),  # Iron head
            "color_secondary": (139, 69, 19)  # Wood handle
        }
    ),
}


# Default starting equipment for new players
DEFAULT_STARTING_EQUIPMENT = {
    "head_equipment": "cloth_cap",
    "chest_equipment": "cloth_shirt",
    "legs_equipment": "cloth_pants",
    "boots_equipment": "simple_shoes",
    "main_hand_weapon": "rusty_sword",
    "off_hand_equipment": None
}

DEFAULT_STARTING_INVENTORY = {
    "bread": 3,
    "health_potion": 2,
    "gold_coin": 25
}


def get_item_definition(item_id: str) -> Optional[ItemDefinition]:
    """Get an item definition by ID."""
    return ITEMS.get(item_id)


def get_all_item_ids() -> list[str]:
    """Get a list of all available item IDs."""
    return list(ITEMS.keys())


def get_items_by_type(item_type: ItemType) -> Dict[str, ItemDefinition]:
    """Get all items of a specific type."""
    return {
        item_id: item_def 
        for item_id, item_def in ITEMS.items() 
        if item_def.item_type == item_type
    }
