# Base legend for map parsing
# This YAML file defines the default symbols used in .map files

# TILES - Static environment elements
'#':
  category: tile
  type: wall
  solid: true
  asset_id: "tile.wall.stone"

'.':
  category: tile
  type: floor
  solid: false
  asset_id: "tile.floor.dirt"

'~':
  category: tile
  type: water
  solid: true
  asset_id: "tile.water.deep"

'^':
  category: tile
  type: mountain
  solid: true
  asset_id: "tile.mountain.rock"

'T':
  category: tile
  type: tree
  solid: true
  asset_id: "tile.tree.oak"

'W':
  category: tile
  type: willow_tree
  solid: true
  asset_id: "tile.tree.willow"

',':
  category: tile
  type: grass
  solid: false
  asset_id: "tile.floor.grass"

';':
  category: tile
  type: gravel
  solid: false
  asset_id: "tile.floor.gravel"

'h':
  category: tile
  type: hill
  solid: true
  asset_id: "tile.hill.grass"

'&':
  category: tile
  type: swamp
  solid: false
  asset_id: "tile.water.swamp"
  properties:
    movement_modifier: 0.7

'o':
  category: tile
  type: cobblestone_path
  solid: false
  asset_id: "tile.path.cobblestone"

'r':
  category: tile
  type: dirt_road
  solid: false
  asset_id: "tile.path.dirt_road"
  properties:
    rotation_snap: vertical
    rotation_snap_tile: dirt_road

'|':
  category: tile
  type: wood_fence
  solid: true
  asset_id: "tile.structure.wood_fence"
  properties:
    rotation_snap: horizontal
    rotation_snap_tile: wood_fence

'_':
  category: tile
  type: wood_walkway
  solid: false
  asset_id: "tile.structure.wood_walkway"

'=':
  category: tile
  type: bridge
  solid: false
  asset_id: "tile.bridge.wooden"

'+':
  category: tile
  type: door
  solid: true
  asset_id: "tile.door.wooden"
  properties:
    can_interact: true
    is_open: false
    closed_asset_id: "tile.door.wooden"
    open_asset_id: "tile.door.wooden.open"

'F':
  category: tile
  type: fence_gate
  solid: true
  asset_id: "tile.gate.wood_fence"
  properties:
    can_interact: true
    is_open: false
    closed_asset_id: "tile.gate.wood_fence"
    open_asset_id: "tile.gate.wood_fence.open"
    rotation_snap: horizontal
    rotation_snap_tile: wood_fence

# LEVEL EXITS - Numbered exit points (0-9)
'0':
  category: tile
  type: exit
  solid: false
  asset_id: "tile.exit.portal"
  properties:
    exit_number: 0

'1':
  category: tile
  type: exit
  solid: false
  asset_id: "tile.exit.portal"
  properties:
    exit_number: 1

'2':
  category: tile
  type: exit
  solid: false
  asset_id: "tile.exit.portal"
  properties:
    exit_number: 2

'3':
  category: tile
  type: exit
  solid: false
  asset_id: "tile.exit.portal"
  properties:
    exit_number: 3

'4':
  category: tile
  type: exit
  solid: false
  asset_id: "tile.exit.portal"
  properties:
    exit_number: 4

'5':
  category: tile
  type: exit
  solid: false
  asset_id: "tile.exit.portal"
  properties:
    exit_number: 5

'6':
  category: tile
  type: exit
  solid: false
  asset_id: "tile.exit.portal"
  properties:
    exit_number: 6

'7':
  category: tile
  type: exit
  solid: false
  asset_id: "tile.exit.portal"
  properties:
    exit_number: 7

'8':
  category: tile
  type: exit
  solid: false
  asset_id: "tile.exit.portal"
  properties:
    exit_number: 8

'9':
  category: tile
  type: exit
  solid: false
  asset_id: "tile.exit.portal"
  properties:
    exit_number: 9

# ENTITIES - Dynamic game objects

# Player
'@':
  category: entity
  type: player
  data_id: "player"

# Monsters
'g':
  category: entity
  type: monster
  data_id: "goblin_grunt"

'G':
  category: entity
  type: monster
  data_id: "goblin_shaman"

'd':
  category: entity
  type: monster
  data_id: "green_drake"

'w':
  category: entity
  type: monster
  data_id: "ice_wolf"

't':
  category: entity
  type: monster
  data_id: "forest_troll"

'S':
  category: entity
  type: monster
  data_id: "sand_scorpion"

# New monsters
'f':
  category: entity
  type: monster
  data_id: "wolf"

'b':
  category: entity
  type: monster
  data_id: "goblin"

's':
  category: entity
  type: monster
  data_id: "spider"

# Wandering Animals - Peaceful creatures
'H':
  category: entity
  type: monster
  data_id: "horse"
  asset_id: "animal.horse.brown.side"

'O':
  category: entity
  type: monster
  data_id: "cow"
  asset_id: "animal.cow.spotted.side"

'k':
  category: entity
  type: monster
  data_id: "chicken"
  asset_id: "animal.chicken.white.side"

'D':
  category: entity
  type: monster
  data_id: "deer"
  asset_id: "animal.deer.brown.side"

'P':
  category: entity
  type: monster
  data_id: "pig"
  asset_id: "animal.pig.pink.side"

'Q':
  category: entity
  type: monster
  data_id: "dog"
  asset_id: "animal.dog.brown.side"

# Items
'$':
  category: entity
  type: item
  data_id: "gold_coin"

'!':
  category: entity
  type: item
  data_id: "health_potion"

'%':
  category: entity
  type: item
  data_id: "bread"

# Interactive objects
'C':
  category: entity
  type: chest
  data_id: "wooden_chest"
  properties:
    contains: ["gold_coin", "health_potion"]

'A':
  category: entity
  type: altar
  data_id: "stone_altar"
  properties:
    effect: "heal_player"

# NPCs
'M':
  category: entity
  type: npc
  data_id: "merchant"
  asset_id: "npc.merchant.general"

'B':
  category: entity
  type: npc
  data_id: "armourer"
  asset_id: "npc.armourer.town"

'W':
  category: entity
  type: npc
  data_id: "weaponsmith"
  asset_id: "npc.weaponsmith.forge"

'I':
  category: entity
  type: npc
  data_id: "innkeeper"
  asset_id: "npc.innkeeper.tavern"

'c':
  category: entity
  type: npc
  data_id: "commoner"
  asset_id: "npc.commoner.citizen"

'G':
  category: entity
  type: npc
  data_id: "guard"
  asset_id: "npc.guard.town"
