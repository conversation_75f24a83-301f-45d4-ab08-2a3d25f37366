"""
Event handlers for the Town of Caledon level.

This module contains level-specific event handling logic for the Town of Caledon.
It manages quest states, NPC interactions, goblin spawning, and other level-specific behaviors.
"""

from typing import Dict, Any, Optional
import time
from src.application.interfaces import IEventBus, GameStateData
from src.game_core import <PERSON><PERSON>vent, <PERSON><PERSON><PERSON>, Monster, Stats
from src.game_core.quest_manager import QuestState
from src.application.use_cases import CreateMonsterUseCase
from src.infrastructure.logging import get_logger
from .quest_definitions import get_caledon_quests

class TownCaledonEventHandlers:
    """Event handlers specific to the Town of Caledon level."""

    def __init__(self, event_bus: IEventBus, quest_manager=None, game_state=None):
        self.event_bus = event_bus
        self.quest_manager = quest_manager
        self.game_state = game_state
        self.logger = get_logger(__name__)

        # Track spawned quest entities
        self.spawned_quest_entities: Dict[str, str] = {}  # quest_id -> entity_id

        # Track NPC dialog states
        self.npc_dialog_states: Dict[str, Dict[str, Any]] = {}

        # Register quests with quest manager
        if self.quest_manager:
            self._register_level_quests()
            # Check for active quests and spawn their entities if needed
            self._restore_active_quest_entities()

    def set_game_state(self, game_state):
        """Update the game state reference for accessing player data."""
        self.game_state = game_state

    def _register_level_quests(self) -> None:
        """Register all quests for this level with the quest manager."""
        try:
            quests = get_caledon_quests()
            for quest_def in quests:
                success = self.quest_manager.register_quest(quest_def)
                if success:
                    self.logger.info(f"Registered quest: {quest_def['id']}")
                else:
                    self.logger.error(f"Failed to register quest: {quest_def['id']}")
        except Exception as e:
            self.logger.error(f"Failed to register level quests: {e}")

    def register_handlers(self) -> None:
        """Register all event handlers for this level."""
        self.event_bus.subscribe("entity_defeated", self.on_entity_defeated)
        self.event_bus.subscribe("npc_interaction", self.on_npc_interaction)

        # Subscribe to quest events
        self.event_bus.subscribe("quest_started", self.on_quest_started)
        self.event_bus.subscribe("quest_completed", self.on_quest_completed)
        self.event_bus.subscribe("quest_closed", self.on_quest_closed)
        self.event_bus.subscribe("objective_completed", self.on_objective_completed)

    def unregister_handlers(self) -> None:
        """Unregister all event handlers when leaving the level."""
        self.event_bus.unsubscribe("entity_defeated", self.on_entity_defeated)
        self.event_bus.unsubscribe("npc_interaction", self.on_npc_interaction)
        self.event_bus.unsubscribe("quest_started", self.on_quest_started)
        self.event_bus.unsubscribe("quest_completed", self.on_quest_completed)
        self.event_bus.unsubscribe("quest_closed", self.on_quest_closed)
        self.event_bus.unsubscribe("objective_completed", self.on_objective_completed)

    def on_entity_defeated(self, event: GameEvent) -> None:
        """Handle when an entity is defeated."""
        entity_id = event.data.get("entity_id")

        if not entity_id or not self.quest_manager:
            return

        # Check if this is the quest goblin
        if entity_id in self.spawned_quest_entities.values():
            # Find which quest this entity belongs to
            for quest_id, spawned_entity_id in self.spawned_quest_entities.items():
                if spawned_entity_id == entity_id:
                    # Complete the defeat_goblin objective
                    success = self.quest_manager.complete_objective(quest_id, "defeat_goblin")
                    if success:
                        self.logger.info(f"Quest objective completed: defeat_goblin for quest {quest_id}")
                        # Remove from spawned entities
                        del self.spawned_quest_entities[quest_id]
                    break

    def on_npc_interaction(self, event: GameEvent) -> None:
        """Handle player interactions with NPCs."""
        npc_id = event.data.get("npc_id", "")
        npc_position = event.data.get("npc_position")

        if not self.quest_manager:
            return

        # Check if this is the guard at position (3,7) - the quest giver
        if npc_position and npc_position[0] == 3 and npc_position[1] == 7:
            self._handle_guard_interaction(npc_id, npc_position)

    def _handle_guard_interaction(self, npc_id: str, position: tuple) -> None:
        """Handle interaction with the quest-giving guard."""
        quest_id = "goblin_loose_in_caledon"
        quest_state = self.quest_manager.get_quest_state(quest_id)

        if quest_state == QuestState.NOT_STARTED:
            # Start the quest
            success = self.quest_manager.start_quest(quest_id)
            if success:
                self.logger.info("Guard: 'There's a dangerous goblin wandering our streets! Can you help us deal with it? I'll reward you with 10 gold if you can defeat it.'")
                # Update NPC dialog state
                self.npc_dialog_states[npc_id] = {
                    "quest_offered": True,
                    "quest_accepted": True
                }
        elif quest_state == QuestState.ACTIVE:
            self.logger.info("Guard: 'The goblin is still out there somewhere. Be careful, and come back when you've dealt with it.'")
        elif quest_state == QuestState.COMPLETED:
            # Give reward and close quest
            self._give_quest_reward(quest_id)
            # Transition quest to CLOSED state
            self.quest_manager.close_quest(quest_id)
            self.logger.info("Guard: 'You've done it! You defeated the goblin! Here's your reward of 10 gold.'")
        elif quest_state == QuestState.CLOSED:
            self.logger.info("Guard: 'Thank you again for dealing with that goblin threat!'")

    def _give_quest_reward(self, quest_id: str) -> None:
        """Give quest rewards to the player."""
        # Emit an event that the game engine can handle to give rewards
        reward_event = GameEvent(
            event_type="quest_reward",
            timestamp=time.time(),
            data={
                "quest_id": quest_id,
                "gold": 10,
                "experience": 25
            }
        )
        self.event_bus.publish(reward_event)

    def on_quest_started(self, event: GameEvent) -> None:
        """Handle quest started events."""
        quest_id = event.data.get("quest_id")

        if quest_id == "goblin_loose_in_caledon":
            # Spawn the quest goblin
            self._spawn_quest_goblin(quest_id)
            self.logger.info(f"Quest started: {quest_id} - Goblin spawned!")

    def _spawn_quest_goblin(self, quest_id: str) -> None:
        """Spawn the quest goblin at the designated position."""
        try:
            # Create goblin at position (15, 13) as specified in quest definition
            goblin_position = Position.from_tile_coords(15, 13, 128)  # 128 is tile size

            # Use CreateMonsterUseCase to properly initialize AI behaviors
            create_monster_use_case = CreateMonsterUseCase(self.event_bus)
            
            goblin = create_monster_use_case.execute(
                monster_id="goblin_grunt",
                position=goblin_position,
                name="Dangerous Goblin",
                custom_stats={
                    "hp": 30,
                    "max_hp": 30,
                    "strength": 8,
                    "defense": 3,
                    "speed": 6
                },
                custom_id=f"quest_goblin_{quest_id}_{int(time.time())}"
            )

            # Override the ID to match quest system expectations
            goblin.id = f"quest_goblin_{quest_id}_{int(time.time())}"

            # Track this spawned entity
            self.spawned_quest_entities[quest_id] = goblin.id

            # Emit event to spawn the goblin in the game world
            spawn_event = GameEvent(
                event_type="spawn_monster",
                timestamp=time.time(),
                data={
                    "monster": goblin,
                    "quest_id": quest_id
                }
            )
            self.event_bus.publish(spawn_event)

            self.logger.info(f"Spawned quest goblin {goblin.id} at position ({15}, {13}) with AI behaviors initialized")

        except Exception as e:
            self.logger.error(f"Failed to spawn quest goblin: {e}")

    def on_quest_completed(self, event: GameEvent) -> None:
        """Handle quest completed events."""
        quest_id = event.data.get("quest_id")
        self.logger.info(f"Quest completed: {quest_id}")

    def on_quest_closed(self, event: GameEvent) -> None:
        """Handle quest closed events."""
        quest_id = event.data.get("quest_id")
        self.logger.info(f"Quest closed: {quest_id} (rewards distributed)")

    def on_objective_completed(self, event: GameEvent) -> None:
        """Handle objective completed events."""
        quest_id = event.data.get("quest_id")
        objective_id = event.data.get("objective_id")
        self.logger.info(f"Objective completed: {objective_id} for quest {quest_id}")

    def get_npc_dialog(self, npc_id: str, npc_position: tuple) -> Optional[str]:
        """Get custom dialog for NPCs based on quest state."""
        if not self.quest_manager:
            return None

        # Check if this is the guard at position (3,7)
        if npc_position and npc_position[0] == 3 and npc_position[1] == 7:
            return self._get_guard_dialog()

        return None

    def _get_guard_dialog(self) -> str:
        """Get dialog for the quest-giving guard based on quest state."""
        quest_id = "goblin_loose_in_caledon"
        quest_state = self.quest_manager.get_quest_state(quest_id)

        if quest_state == QuestState.NOT_STARTED:
            return "There's a dangerous goblin wandering our streets! Can you help us deal with it? I'll reward you with 10 gold if you can defeat it."
        elif quest_state == QuestState.ACTIVE:
            return "The goblin is still out there somewhere. Be careful, and come back when you've dealt with it."
        elif quest_state == QuestState.COMPLETED:
            return "You've done it! You defeated the goblin! Here's your reward of 10 gold."
        elif quest_state == QuestState.CLOSED:
            return "Thank you again for dealing with that goblin threat!"
        else:
            return "Stay safe, citizen."

    def _restore_active_quest_entities(self) -> None:
        """Check for active quests and spawn their entities if needed."""
        try:
            quest_id = "goblin_loose_in_caledon"
            quest_state = self.quest_manager.get_quest_state(quest_id)
            
            if quest_state == QuestState.ACTIVE:
                # Quest is active but goblin might not be spawned (e.g., after loading save)
                self.logger.info(f"Found active quest '{quest_id}' - restoring quest entities")
                self._spawn_quest_goblin(quest_id)
                
        except Exception as e:
            self.logger.error(f"Failed to restore active quest entities: {e}")
