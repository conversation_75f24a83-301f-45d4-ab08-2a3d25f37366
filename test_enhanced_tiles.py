#!/usr/bin/env python3
"""
Test script to verify the enhanced procedural tiles work correctly.
"""

import pygame
import sys
import os

# Add the source directory to the path so we can import from it
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from infrastructure.assets.procedural_tiles import (
    get_grass_tile, get_dirt_floor_tile, get_sand_floor_tile, 
    get_swamp_tile, get_wood_floor_tile, get_dirt_road_tile, 
    get_wooden_bridge_tile, get_water_tile
)

def test_enhanced_tiles():
    """Test all enhanced tiles to ensure they generate correctly."""
    pygame.init()
    
    # Create a display surface
    screen = pygame.display.set_mode((800, 600))
    pygame.display.set_caption("Enhanced Procedural Tiles Test")
    
    # Test tile size
    tile_size = (64, 64)  # Larger for better visibility
    
    # Create all tiles
    tiles = {
        'grass': get_grass_tile(tile_size),
        'dirt': get_dirt_floor_tile(tile_size),
        'sand': get_sand_floor_tile(tile_size),
        'swamp': get_swamp_tile(tile_size),
        'wood_floor': get_wood_floor_tile(tile_size),
        'dirt_road': get_dirt_road_tile(tile_size),
        'wooden_bridge': get_wooden_bridge_tile(tile_size),
        'water': get_water_tile(tile_size)
    }
    
    # Display tiles in a grid
    tile_names = list(tiles.keys())
    cols = 4
    rows = 2
    
    screen.fill((50, 50, 50))  # Dark gray background
    
    for i, (name, tile) in enumerate(tiles.items()):
        col = i % cols
        row = i // cols
        
        x = col * (tile_size[0] + 20) + 50
        y = row * (tile_size[1] + 50) + 50
        
        # Blit the tile
        screen.blit(tile, (x, y))
        
        # Add label
        font = pygame.font.Font(None, 24)
        text = font.render(name, True, (255, 255, 255))
        screen.blit(text, (x, y + tile_size[1] + 5))
    
    # Show tiling example with a few tiles
    tiling_start_x = 50
    tiling_start_y = 350
    
    # Create a 4x4 grid of grass tiles to show tiling
    for row in range(4):
        for col in range(4):
            x = tiling_start_x + col * 32
            y = tiling_start_y + row * 32
            screen.blit(get_grass_tile((32, 32)), (x, y))
    
    # Label the tiling example
    font = pygame.font.Font(None, 24)
    text = font.render("Grass Tiling Example", True, (255, 255, 255))
    screen.blit(text, (tiling_start_x, tiling_start_y - 25))
    
    # Create a 4x4 grid of swamp tiles to show reed placement
    swamp_start_x = 200
    for row in range(4):
        for col in range(4):
            x = swamp_start_x + col * 32
            y = tiling_start_y + row * 32
            screen.blit(get_swamp_tile((32, 32)), (x, y))
    
    # Label the swamp example
    text = font.render("Swamp with Reeds", True, (255, 255, 255))
    screen.blit(text, (swamp_start_x, tiling_start_y - 25))
    
    # Create a 4x4 grid of bridge tiles to show water background
    bridge_start_x = 350
    for row in range(4):
        for col in range(4):
            x = bridge_start_x + col * 32
            y = tiling_start_y + row * 32
            screen.blit(get_wooden_bridge_tile((32, 32)), (x, y))
    
    # Label the bridge example
    text = font.render("Bridge with Water", True, (255, 255, 255))
    screen.blit(text, (bridge_start_x, tiling_start_y - 25))
    
    pygame.display.flip()
    
    # Wait for user input
    print("Enhanced tiles test complete!")
    print("- All tiles generated successfully")
    print("- Check the display window to verify visual quality")
    print("- Grass tiles show enhanced detail while remaining tileable")
    print("- Swamp tiles include random reed clumps with cattails")
    print("- Bridge tiles have full water background matching water tile")
    print("- Press any key or close window to exit")
    
    clock = pygame.time.Clock()
    running = True
    
    while running:
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False
            elif event.type == pygame.KEYDOWN:
                running = False
        
        clock.tick(60)
    
    pygame.quit()
    return True

if __name__ == "__main__":
    try:
        success = test_enhanced_tiles()
        if success:
            print("✓ All enhanced tiles work correctly!")
        else:
            print("✗ Some tiles had issues")
            sys.exit(1)
    except Exception as e:
        print(f"✗ Error testing tiles: {e}")
        sys.exit(1)
