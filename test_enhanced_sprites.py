#!/usr/bin/env python3
"""
Test script to verify enhanced sprite generation works correctly.
"""

import pygame
import sys
import os

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.infrastructure.assets.procedural_npcs import (
    get_merchant_sprite, get_armourer_sprite, get_weaponsmith_sprite,
    get_innkeeper_sprite, get_commoner_sprite, get_guard_sprite
)
from src.infrastructure.assets.procedural_player import (
    get_base_player_sprite, get_equipped_player_sprite, get_player_sprite
)

def test_sprite_generation():
    """Test that all enhanced sprites generate without errors."""
    pygame.init()
    
    print("Testing enhanced NPC sprite generation...")
    
    # Test NPC sprites
    npc_functions = [
        ("Merchant", get_merchant_sprite),
        ("Armourer", get_armourer_sprite), 
        ("Weaponsmith", get_weaponsmith_sprite),
        ("Innkeeper", get_innkeeper_sprite),
        ("Commoner", get_commoner_sprite),
        ("Guard", get_guard_sprite)
    ]
    
    for name, func in npc_functions:
        try:
            sprite = func()
            print(f"✅ {name} sprite generated successfully ({sprite.get_width()}x{sprite.get_height()})")
        except Exception as e:
            print(f"❌ {name} sprite failed: {e}")
    
    print("\nTesting enhanced player sprite generation...")
    
    # Test player sprites
    try:
        base_sprite = get_base_player_sprite()
        print(f"✅ Base player sprite generated successfully ({base_sprite.get_width()}x{base_sprite.get_height()})")
    except Exception as e:
        print(f"❌ Base player sprite failed: {e}")
    
    try:
        equipped_sprite = get_player_sprite()
        print(f"✅ Equipped player sprite generated successfully ({equipped_sprite.get_width()}x{equipped_sprite.get_height()})")
    except Exception as e:
        print(f"❌ Equipped player sprite failed: {e}")
    
    try:
        equipment_ids = {
            "head_equipment": "iron_helmet",
            "chest_equipment": "iron_breastplate", 
            "legs_equipment": "iron_greaves",
            "boots_equipment": "iron_boots",
            "main_hand_weapon": "iron_sword",
            "off_hand_equipment": "iron_shield"
        }
        full_equipped_sprite = get_equipped_player_sprite(equipment_ids)
        print(f"✅ Fully equipped player sprite generated successfully ({full_equipped_sprite.get_width()}x{full_equipped_sprite.get_height()})")
    except Exception as e:
        print(f"❌ Fully equipped player sprite failed: {e}")
    
    pygame.quit()
    print("\n🎉 Sprite generation testing completed!")

if __name__ == "__main__":
    test_sprite_generation()
