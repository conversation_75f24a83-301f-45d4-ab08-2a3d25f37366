Write a prompt I can give to an AI to build the following:

I would like to add ranged attacks to the game.

A player of entity can make ranged attachsk. This should fire a projectile in the direction of the mouse click. The projectile should travel in a straight line for it's range, and at it's speed. If it interacts with an entity, it should deal damage to it.

the ranged weapon should require both hands. So when equiped it will sit in the main hand equipment slot  and will disable the offhand item (Show a grayed out effect).

