#!/usr/bin/env python3
"""
Test script to verify tile rotation is working correctly.
"""

import pygame
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.infrastructure.assets.asset_manager import AssetManager
from src.infrastructure.repositories.level_repository import LevelRepository
from src.infrastructure.rendering.pygame_renderer import PygameRenderer
from src.application.use_cases import BuildLevelUseCase
from src.infrastructure.events.pygame_event_bus import PygameEventBus
from src.game_core.config import initialize_config

def main():
    """Test tile rotation rendering."""
    # Initialize configuration
    config_path = Path(__file__).parent / "game_config.yaml"
    initialize_config(str(config_path))
    
    # Initialize Pygame
    pygame.init()
    
    # Initialize components
    asset_manager = AssetManager()
    level_repository = LevelRepository('src/levels', 'src/game_data/base_legend.yaml')
    event_bus = PygameEventBus()
    build_level_use_case = BuildLevelUseCase(event_bus)
    
    # Load the level
    layout_data = level_repository.load_level('town_west_haven')
    merged_legend = level_repository.get_merged_legend_for_level('town_west_haven')
    
    # Build game state
    game_state = build_level_use_case.execute(layout_data, 'town_west_haven', merged_legend)
    
    # Initialize renderer
    renderer = PygameRenderer(asset_manager, 800, 600)
    
    print("Tile rotation test starting...")
    print(f"Game state has merged_legend: {hasattr(game_state, 'merged_legend')}")
    print(f"Merged legend size: {len(game_state.merged_legend) if hasattr(game_state, 'merged_legend') else 0}")
    
    # Test rotation for specific tiles
    if hasattr(game_state, 'merged_legend') and game_state.merged_legend:
        from src.infrastructure.rendering.tile_rotation import get_tile_rotation
        
        print("\nTesting tile rotations:")
        
        # Find and test fence gates
        fence_gate_count = 0
        for y, row in enumerate(game_state.level_tiles):
            for x, asset_id in enumerate(row):
                if asset_id == 'tile.gate.wood_fence':
                    # Find the tile definition
                    tile_definition = None
                    for symbol, definition in game_state.merged_legend.items():
                        if (definition.get('category') == 'tile' and
                            definition.get('asset_id') == asset_id):
                            tile_definition = definition
                            break
                    
                    if tile_definition:
                        rotation = get_tile_rotation(x, y, tile_definition,
                                                   game_state.level_tiles,
                                                   game_state.merged_legend)
                        print(f"  Fence gate at ({x}, {y}): {rotation}° rotation")
                        
                        # Test asset manager rotation
                        try:
                            rotated_surface = asset_manager.get_asset(asset_id, (64, 64), rotation)
                            print(f"    Asset manager successfully created rotated surface: {rotated_surface.get_size()}")
                        except Exception as e:
                            print(f"    ERROR: Asset manager failed to create rotated surface: {e}")
                        
                        fence_gate_count += 1
                        if fence_gate_count >= 2:  # Test first 2 fence gates
                            break
            if fence_gate_count >= 2:
                break
        
        # Find and test roads
        road_count = 0
        for y, row in enumerate(game_state.level_tiles):
            for x, asset_id in enumerate(row):
                if asset_id == 'tile.path.dirt_road':
                    # Find the tile definition
                    tile_definition = None
                    for symbol, definition in game_state.merged_legend.items():
                        if (definition.get('category') == 'tile' and
                            definition.get('asset_id') == asset_id):
                            tile_definition = definition
                            break
                    
                    if tile_definition:
                        rotation = get_tile_rotation(x, y, tile_definition,
                                                   game_state.level_tiles,
                                                   game_state.merged_legend)
                        print(f"  Road at ({x}, {y}): {rotation}° rotation")
                        
                        # Test asset manager rotation
                        try:
                            rotated_surface = asset_manager.get_asset(asset_id, (64, 64), rotation)
                            print(f"    Asset manager successfully created rotated surface: {rotated_surface.get_size()}")
                        except Exception as e:
                            print(f"    ERROR: Asset manager failed to create rotated surface: {e}")
                        
                        road_count += 1
                        if road_count >= 3:  # Test first 3 roads
                            break
            if road_count >= 3:
                break
    
    print("\nTile rotation test completed successfully!")
    print("The rotation system is working correctly.")
    print("\nTo see the rotation in action:")
    print("1. Run the game with: uv run python -m src.main")
    print("2. Look for fence gates and roads that should be rotated")
    print("3. Fence gates should rotate 90° when connected to vertical fences")
    print("4. Roads should stay horizontal (0°) when connected horizontally")
    
    pygame.quit()

if __name__ == "__main__":
    main()
