"""
Unit tests for player asset generation system.
"""

import pytest
import pygame
from unittest.mock import patch, Mock

from src.infrastructure.assets.procedural_player import (
    get_base_player_sprite, get_equipped_player_sprite, get_player_sprite
)
from src.infrastructure.assets.registry import (
    generate_dynamic_player_asset_id, get_dynamic_player_asset_generator,
    is_dynamic_player_asset
)
from src.infrastructure.assets.asset_manager import AssetManager


class TestPlayerSpriteGeneration:
    """Test player sprite generation functions."""
    
    def setup_method(self):
        """Set up test fixtures."""
        pygame.init()
    
    def test_get_base_player_sprite(self):
        """Test base player sprite generation."""
        sprite = get_base_player_sprite((128, 128))
        
        assert sprite is not None
        assert isinstance(sprite, pygame.Surface)
        assert sprite.get_size() == (128, 128)
        assert sprite.get_flags() & pygame.SRCALPHA  # Should have alpha channel
    
    def test_get_base_player_sprite_different_sizes(self):
        """Test base player sprite with different sizes."""
        sizes = [(64, 64), (128, 128), (256, 256)]
        
        for size in sizes:
            sprite = get_base_player_sprite(size)
            assert sprite.get_size() == size
    
    def test_get_equipped_player_sprite_empty_equipment(self):
        """Test equipped player sprite with no equipment."""
        equipment = {
            "head_equipment": None,
            "chest_equipment": None,
            "legs_equipment": None,
            "boots_equipment": None,
            "main_hand_weapon": None,
            "off_hand_equipment": None
        }
        
        sprite = get_equipped_player_sprite(equipment, (128, 128))
        
        assert sprite is not None
        assert isinstance(sprite, pygame.Surface)
        assert sprite.get_size() == (128, 128)
    
    def test_get_equipped_player_sprite_with_equipment(self):
        """Test equipped player sprite with equipment."""
        equipment = {
            "head_equipment": "cloth_cap",
            "chest_equipment": "cloth_shirt",
            "legs_equipment": "cloth_pants",
            "boots_equipment": "simple_shoes",
            "main_hand_weapon": "rusty_sword",
            "off_hand_equipment": None
        }
        
        sprite = get_equipped_player_sprite(equipment, (128, 128))
        
        assert sprite is not None
        assert isinstance(sprite, pygame.Surface)
        assert sprite.get_size() == (128, 128)
    
    def test_get_player_sprite_backward_compatibility(self):
        """Test backward compatibility of get_player_sprite."""
        sprite = get_player_sprite((128, 128))
        
        assert sprite is not None
        assert isinstance(sprite, pygame.Surface)
        assert sprite.get_size() == (128, 128)
    
    def test_equipped_sprites_are_different(self):
        """Test that different equipment combinations produce different sprites."""
        equipment1 = {
            "head_equipment": "cloth_cap",
            "chest_equipment": "cloth_shirt",
            "legs_equipment": "cloth_pants",
            "boots_equipment": "simple_shoes",
            "main_hand_weapon": "rusty_sword",
            "off_hand_equipment": None
        }
        
        equipment2 = {
            "head_equipment": "iron_helmet",
            "chest_equipment": "leather_armor",
            "legs_equipment": "iron_greaves",
            "boots_equipment": "leather_boots",
            "main_hand_weapon": "iron_sword",
            "off_hand_equipment": "iron_shield"
        }
        
        sprite1 = get_equipped_player_sprite(equipment1, (128, 128))
        sprite2 = get_equipped_player_sprite(equipment2, (128, 128))
        
        # Sprites should be different objects
        assert sprite1 is not sprite2
        
        # Convert to string representation to compare pixel data
        sprite1_data = pygame.image.tostring(sprite1, 'RGBA')
        sprite2_data = pygame.image.tostring(sprite2, 'RGBA')
        assert sprite1_data != sprite2_data


class TestDynamicAssetRegistry:
    """Test dynamic asset registry functions."""
    
    def test_generate_dynamic_player_asset_id(self):
        """Test dynamic asset ID generation."""
        equipment = {
            "head_equipment": "cloth_cap",
            "chest_equipment": "cloth_shirt",
            "legs_equipment": "cloth_pants",
            "boots_equipment": "simple_shoes",
            "main_hand_weapon": "rusty_sword",
            "off_hand_equipment": None
        }
        
        asset_id = generate_dynamic_player_asset_id(equipment)
        
        assert asset_id.startswith("player.equipped.")
        assert len(asset_id.split(".")) == 3  # player.equipped.XXXXXX
        assert asset_id.split(".")[-1].isdigit()  # Hash should be numeric
    
    def test_dynamic_asset_id_consistency(self):
        """Test that same equipment produces same asset ID."""
        equipment = {
            "head_equipment": "cloth_cap",
            "chest_equipment": "cloth_shirt",
            "legs_equipment": None,
            "boots_equipment": None,
            "main_hand_weapon": "rusty_sword",
            "off_hand_equipment": None
        }
        
        asset_id1 = generate_dynamic_player_asset_id(equipment)
        asset_id2 = generate_dynamic_player_asset_id(equipment)
        
        assert asset_id1 == asset_id2
    
    def test_dynamic_asset_id_uniqueness(self):
        """Test that different equipment produces different asset IDs."""
        equipment1 = {
            "head_equipment": "cloth_cap",
            "chest_equipment": "cloth_shirt",
            "legs_equipment": None,
            "boots_equipment": None,
            "main_hand_weapon": "rusty_sword",
            "off_hand_equipment": None
        }
        
        equipment2 = {
            "head_equipment": "iron_helmet",
            "chest_equipment": "leather_armor",
            "legs_equipment": None,
            "boots_equipment": None,
            "main_hand_weapon": "iron_sword",
            "off_hand_equipment": None
        }
        
        asset_id1 = generate_dynamic_player_asset_id(equipment1)
        asset_id2 = generate_dynamic_player_asset_id(equipment2)
        
        assert asset_id1 != asset_id2
    
    def test_is_dynamic_player_asset(self):
        """Test dynamic player asset detection."""
        assert is_dynamic_player_asset("player.equipped.123456") == True
        assert is_dynamic_player_asset("player.equipped.000001") == True
        assert is_dynamic_player_asset("player.hero") == False
        assert is_dynamic_player_asset("monster.goblin.grunt.side") == False
        assert is_dynamic_player_asset("item.weapon.sword.iron") == False
    
    def test_get_dynamic_player_asset_generator(self):
        """Test dynamic asset generator creation."""
        equipment = {
            "head_equipment": "cloth_cap",
            "chest_equipment": "cloth_shirt",
            "legs_equipment": None,
            "boots_equipment": None,
            "main_hand_weapon": "rusty_sword",
            "off_hand_equipment": None
        }
        
        generator = get_dynamic_player_asset_generator(equipment)
        
        assert callable(generator)
        
        # Test that generator produces valid sprite
        pygame.init()
        sprite = generator((128, 128))
        assert sprite is not None
        assert isinstance(sprite, pygame.Surface)
        assert sprite.get_size() == (128, 128)


class TestAssetManagerEquipment:
    """Test asset manager equipment functionality."""
    
    def setup_method(self):
        """Set up test fixtures."""
        pygame.init()
        self.asset_manager = AssetManager()
    
    def test_get_equipped_player_asset(self):
        """Test getting equipped player asset from asset manager."""
        equipment = {
            "head_equipment": "cloth_cap",
            "chest_equipment": "cloth_shirt",
            "legs_equipment": "cloth_pants",
            "boots_equipment": "simple_shoes",
            "main_hand_weapon": "rusty_sword",
            "off_hand_equipment": None
        }
        
        sprite = self.asset_manager.get_equipped_player_asset(equipment, (128, 128))
        
        assert sprite is not None
        assert isinstance(sprite, pygame.Surface)
        assert sprite.get_size() == (128, 128)
    
    def test_equipped_player_asset_caching(self):
        """Test that equipped player assets are cached."""
        equipment = {
            "head_equipment": "cloth_cap",
            "chest_equipment": "cloth_shirt",
            "legs_equipment": None,
            "boots_equipment": None,
            "main_hand_weapon": "rusty_sword",
            "off_hand_equipment": None
        }
        
        initial_cache_size = self.asset_manager.get_cache_size()
        
        # First request
        sprite1 = self.asset_manager.get_equipped_player_asset(equipment, (128, 128))
        cache_size_after_first = self.asset_manager.get_cache_size()
        
        # Second request (should be cached)
        sprite2 = self.asset_manager.get_equipped_player_asset(equipment, (128, 128))
        cache_size_after_second = self.asset_manager.get_cache_size()
        
        # Cache should have grown after first request
        assert cache_size_after_first > initial_cache_size
        
        # Cache should not grow after second request (cached)
        assert cache_size_after_second == cache_size_after_first
        
        # Should return same sprite object
        assert sprite1 is sprite2
    
    def test_different_sizes_cached_separately(self):
        """Test that different sizes are cached separately."""
        equipment = {
            "head_equipment": "cloth_cap",
            "chest_equipment": None,
            "legs_equipment": None,
            "boots_equipment": None,
            "main_hand_weapon": None,
            "off_hand_equipment": None
        }
        
        sprite_128 = self.asset_manager.get_equipped_player_asset(equipment, (128, 128))
        sprite_64 = self.asset_manager.get_equipped_player_asset(equipment, (64, 64))
        
        assert sprite_128.get_size() == (128, 128)
        assert sprite_64.get_size() == (64, 64)
        assert sprite_128 is not sprite_64
    
    def test_clear_player_equipment_cache(self):
        """Test clearing player equipment cache."""
        equipment = {
            "head_equipment": "cloth_cap",
            "chest_equipment": "cloth_shirt",
            "legs_equipment": None,
            "boots_equipment": None,
            "main_hand_weapon": "rusty_sword",
            "off_hand_equipment": None
        }
        
        # Generate some equipped player assets
        self.asset_manager.get_equipped_player_asset(equipment, (128, 128))
        self.asset_manager.get_equipped_player_asset(equipment, (64, 64))
        
        # Also get a regular asset to ensure it's not cleared
        self.asset_manager.get_asset("player.hero", (128, 128))
        
        initial_cache_size = self.asset_manager.get_cache_size()
        assert initial_cache_size >= 3  # At least the assets we just created
        
        # Clear player equipment cache
        self.asset_manager.clear_player_equipment_cache()
        
        final_cache_size = self.asset_manager.get_cache_size()
        
        # Cache should be smaller (equipment assets removed)
        assert final_cache_size < initial_cache_size
        
        # Regular player asset should still be cached
        cached_regular = self.asset_manager.get_asset("player.hero", (128, 128))
        assert cached_regular is not None
