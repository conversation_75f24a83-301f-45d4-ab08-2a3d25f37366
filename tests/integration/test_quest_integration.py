"""
Integration tests for the Quest System.

Tests the complete quest workflow including level event handlers,
quest manager integration, and the goblin quest in Town Caledon.
"""

import pytest
import time
from unittest.mock import <PERSON><PERSON>, MagicMock

from src.game_core.quest_manager import GlobalQuestManager, QuestState
from src.game_core.events import GameEvent
from src.infrastructure.events.pygame_event_bus import PygameEventBus
from src.levels.town_caledon.events import TownCaledonEventHandlers
from src.levels.town_caledon.quest_definitions import get_caledon_quests


class TestQuestIntegration:
    """Integration tests for the quest system."""

    def setup_method(self):
        """Set up test fixtures."""
        self.event_bus = PygameEventBus()
        self.quest_manager = GlobalQuestManager(self.event_bus)
        self.event_handlers = TownCaledonEventHandlers(self.event_bus, self.quest_manager)
        self.event_handlers.register_handlers()
        
        # Track events
        self.events_received = []
        def track_events(event):
            self.events_received.append(event)
        
        self.event_bus.subscribe("quest_started", track_events)
        self.event_bus.subscribe("objective_completed", track_events)
        self.event_bus.subscribe("quest_completed", track_events)
        self.event_bus.subscribe("quest_closed", track_events)
        self.event_bus.subscribe("spawn_monster", track_events)
        self.event_bus.subscribe("quest_reward", track_events)

    def test_caledon_quest_definitions(self):
        """Test that Caledon quest definitions are valid."""
        quests = get_caledon_quests()
        
        assert len(quests) == 1
        quest = quests[0]
        assert quest["id"] == "goblin_loose_in_caledon"
        assert quest["name"] == "Goblin Problem"
        assert "defeat_goblin" in quest["objectives"]
        assert quest["metadata"]["reward_gold"] == 10

    def test_quest_registration_on_handler_init(self):
        """Test that quests are automatically registered when event handlers initialize."""
        # Quest should be registered automatically
        quest_state = self.quest_manager.get_quest_state("goblin_loose_in_caledon")
        assert quest_state == QuestState.NOT_STARTED
        
        quest = self.quest_manager.get_quest("goblin_loose_in_caledon")
        assert quest is not None
        assert quest.name == "Goblin Problem"
        assert len(quest.objectives) == 1
        assert quest.objectives[0].id == "defeat_goblin"

    def test_guard_interaction_starts_quest(self):
        """Test that interacting with the guard starts the quest."""
        # Simulate NPC interaction with guard at position (3, 7)
        interaction_event = GameEvent(
            event_type="npc_interaction",
            timestamp=time.time(),
            data={
                "npc_id": "guard_at_3_7",
                "npc_position": (3, 7),
                "npc_type": "guard"
            }
        )
        
        self.event_bus.publish(interaction_event)
        
        # Quest should now be active
        quest_state = self.quest_manager.get_quest_state("goblin_loose_in_caledon")
        assert quest_state == QuestState.ACTIVE
        
        # Should have received quest_started and spawn_monster events
        quest_started_events = [e for e in self.events_received if e.event_type == "quest_started"]
        spawn_monster_events = [e for e in self.events_received if e.event_type == "spawn_monster"]
        
        assert len(quest_started_events) == 1
        assert len(spawn_monster_events) == 1
        
        # Check quest started event data
        quest_event = quest_started_events[0]
        assert quest_event.data["quest_id"] == "goblin_loose_in_caledon"
        assert quest_event.data["quest_name"] == "Goblin Problem"
        
        # Check spawn monster event data
        spawn_event = spawn_monster_events[0]
        assert "monster" in spawn_event.data
        monster = spawn_event.data["monster"]
        assert monster.name == "Dangerous Goblin"
        assert monster.monster_type == "goblin_grunt"

    def test_goblin_defeat_completes_objective(self):
        """Test that defeating the quest goblin completes the objective."""
        # Start the quest first
        self.test_guard_interaction_starts_quest()

        # Get the spawned goblin ID from all events received so far
        spawn_events = [e for e in self.events_received if e.event_type == "spawn_monster"]
        assert len(spawn_events) > 0, "No spawn events found after quest start"

        goblin_id = spawn_events[0].data["monster"].id

        # Clear events to track only defeat-related events
        self.events_received.clear()
        
        # Simulate goblin defeat
        defeat_event = GameEvent(
            event_type="entity_defeated",
            timestamp=time.time(),
            data={
                "entity_id": goblin_id,
                "entity_type": "monster",
                "killer_id": "player"
            }
        )
        
        self.event_bus.publish(defeat_event)
        
        # Objective should be completed
        assert self.quest_manager.is_objective_completed("goblin_loose_in_caledon", "defeat_goblin")
        
        # Quest should be completed
        quest_state = self.quest_manager.get_quest_state("goblin_loose_in_caledon")
        assert quest_state == QuestState.COMPLETED
        
        # Should have received objective_completed and quest_completed events
        objective_events = [e for e in self.events_received if e.event_type == "objective_completed"]
        quest_completed_events = [e for e in self.events_received if e.event_type == "quest_completed"]
        
        assert len(objective_events) == 1
        assert len(quest_completed_events) == 1

    def test_guard_interaction_after_quest_completion(self):
        """Test guard dialog after quest completion gives reward and closes quest."""
        # Complete the quest first
        self.test_goblin_defeat_completes_objective()

        # Clear events to track only reward-related events
        self.events_received.clear()
        
        # Interact with guard again - should give reward and close quest
        interaction_event = GameEvent(
            event_type="npc_interaction",
            timestamp=time.time(),
            data={
                "npc_id": "guard_at_3_7",
                "npc_position": (3, 7),
                "npc_type": "guard"
            }
        )
        
        self.event_bus.publish(interaction_event)
        
        # Should receive quest reward and quest closed events
        reward_events = [e for e in self.events_received if e.event_type == "quest_reward"]
        closed_events = [e for e in self.events_received if e.event_type == "quest_closed"]
        
        assert len(reward_events) == 1
        assert len(closed_events) == 1
        
        reward_event = reward_events[0]
        assert reward_event.data["quest_id"] == "goblin_loose_in_caledon"
        assert reward_event.data["gold"] == 10
        assert reward_event.data["experience"] == 25
        
        closed_event = closed_events[0]
        assert closed_event.data["quest_id"] == "goblin_loose_in_caledon"
        
        # Quest should now be in CLOSED state
        quest_state = self.quest_manager.get_quest_state("goblin_loose_in_caledon")
        assert quest_state == QuestState.CLOSED
        
        # Interacting with guard again should give thanking dialog, no more rewards
        self.events_received.clear()
        self.event_bus.publish(interaction_event)
        
        # Should NOT receive any reward events this time
        reward_events = [e for e in self.events_received if e.event_type == "quest_reward"]
        assert len(reward_events) == 0

    def test_guard_dialog_states(self):
        """Test that guard dialog changes based on quest state."""
        # Initial state - quest not started
        dialog = self.event_handlers.get_npc_dialog("guard_at_3_7", (3, 7))
        assert "dangerous goblin wandering our streets" in dialog
        
        # Start quest
        self.quest_manager.start_quest("goblin_loose_in_caledon")
        dialog = self.event_handlers.get_npc_dialog("guard_at_3_7", (3, 7))
        assert "goblin is still out there" in dialog
        
        # Complete quest
        self.quest_manager.complete_objective("goblin_loose_in_caledon", "defeat_goblin")
        dialog = self.event_handlers.get_npc_dialog("guard_at_3_7", (3, 7))
        assert "You've done it" in dialog and "reward" in dialog
        
        # Close quest (after reward given)
        self.quest_manager.close_quest("goblin_loose_in_caledon")
        dialog = self.event_handlers.get_npc_dialog("guard_at_3_7", (3, 7))
        assert "Thank you again" in dialog

    def test_quest_persistence_data(self):
        """Test that quest data can be serialized for persistence."""
        # Start and partially complete quest
        self.quest_manager.start_quest("goblin_loose_in_caledon")
        
        # Get persistence data
        quest_data = self.quest_manager.get_all_quests_data()
        
        assert "goblin_loose_in_caledon" in quest_data
        quest_info = quest_data["goblin_loose_in_caledon"]
        assert quest_info["state"] == "active"
        assert quest_info["name"] == "Goblin Problem"
        assert len(quest_info["objectives"]) == 1
        assert not quest_info["objectives"][0]["completed"]

    def test_quest_loading_from_persistence(self):
        """Test loading quest state from persistence data."""
        # Create persistence data for active quest
        quest_data = {
            "goblin_loose_in_caledon": {
                "id": "goblin_loose_in_caledon",
                "name": "Goblin Problem",
                "description": "Deal with the goblin threat in the town",
                "state": "active",
                "start_time": time.time(),
                "completion_time": None,
                "metadata": {"level": "town_caledon", "reward_gold": 10},
                "objectives": [
                    {"id": "defeat_goblin", "completed": False, "completion_time": None}
                ]
            }
        }
        
        # Create new quest manager and load data
        new_quest_manager = GlobalQuestManager(self.event_bus)
        new_event_handlers = TownCaledonEventHandlers(self.event_bus, new_quest_manager)
        new_quest_manager.load_quests_data(quest_data)
        
        # Verify loaded state
        assert new_quest_manager.get_quest_state("goblin_loose_in_caledon") == QuestState.ACTIVE
        assert not new_quest_manager.is_objective_completed("goblin_loose_in_caledon", "defeat_goblin")

    def test_multiple_guard_interactions_no_duplicate_spawns(self):
        """Test that multiple guard interactions don't spawn multiple goblins."""
        # Interact with guard multiple times
        for _ in range(3):
            interaction_event = GameEvent(
                event_type="npc_interaction",
                timestamp=time.time(),
                data={"npc_id": "guard_at_3_7", "npc_position": (3, 7), "npc_type": "guard"}
            )
            self.event_bus.publish(interaction_event)
        
        # Should only have one spawn event
        spawn_events = [e for e in self.events_received if e.event_type == "spawn_monster"]
        assert len(spawn_events) == 1
        
        # Quest should only be started once
        quest_started_events = [e for e in self.events_received if e.event_type == "quest_started"]
        assert len(quest_started_events) == 1

    def teardown_method(self):
        """Clean up after tests."""
        if hasattr(self.event_handlers, 'unregister_handlers'):
            self.event_handlers.unregister_handlers()


if __name__ == "__main__":
    pytest.main([__file__])
